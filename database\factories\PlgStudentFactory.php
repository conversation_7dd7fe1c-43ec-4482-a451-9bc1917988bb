<?php

namespace Database\Factories;

use App\Models\PlgStudent;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PlgStudentFactory extends Factory
{
    protected $model = PlgStudent::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'apelido' => $this->faker->optional()->firstName(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt('password'),
            'document' => $this->faker->optional()->numerify('###.###.###-##'), // CPF fake
            'crm' => $this->faker->optional()->numerify('CRM/SP ######'),
            'avatar' => null,
            'phone1' => $this->faker->phoneNumber(),
            'is_phone1_whatsapp' => $this->faker->boolean(70), // 70% chance de ser WhatsApp
            'phone2' => $this->faker->optional(0.3)->phoneNumber(), // 30% chance de ter phone2
            'is_phone2_whatsapp' => $this->faker->boolean(50),
            'birth_date' => $this->faker->date(),
            'active' => true,
            'registration_status' => $this->faker->randomElement(['pending', 'approved']),
            'approved_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 year', 'now'),
            'approved_by' => null, // Será preenchido depois se necessário
            'last_login' => null,
            'remember_token' => Str::random(10),
            'company_id' => 1
        ];
    }
} 