<x-layouts.student title="{{ $module->title }} - {{ $course->title }}">
    <!-- Breadcrumb -->
    <x-student.breadcrumb :course="$course" :module="$module" />

    <div class="min-h-screen">
        <!-- Main Content Area -->
        <div class="container mx-auto py-6 px-6">
            <!-- Header do Módulo -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $module->title }}</h1>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                            Módulo {{ $module->order ?? 1 }}
                        </span>
                    </div>
                </div>
                
                @if($module->description)
                    <p class="text-gray-600 dark:text-gray-400 text-lg">{!! $module->description !!}</p>
                @endif
            </div>

            <!-- Grid Principal -->
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
                <!-- <PERSON><PERSON> Principal - Quizzes e Testes (9 colunas no desktop, full width no mobile) -->
                <div class="lg:col-span-9 space-y-6">
                    @php
                        $allTests = $module->tests->where('active', true);
                        $quizzes = $allTests->filter(function($test) { return $test->isQuiz(); });
                        $challenges = $allTests->filter(function($test) { return $test->isChallenge(); });
                        $exams = $allTests->filter(function($test) { return $test->isExam(); });
                        $hasAccess = true; // Por enquanto sempre true, pode ser implementada lógica de acesso
                    @endphp

                    <!-- Bloco 1: Quizzes de Treino -->
                    @if($quizzes->count() > 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3 mb-2">
                                    <i class="fas fa-brain text-red-600 text-xl"></i>
                                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Quizzes</h2>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    Teste seus conhecimentos básicos. Você precisa de pelo menos 70% para ser aprovado.
                                </p>
                            </div>

                            <div class="p-6">
                                @foreach($quizzes as $quiz)
                                    @php
                                        $student = auth('students')->user();
                                        $quizStats = $student ? $student->getQuizStats($quiz) : null;
                                        $state = $quizStats['state'] ?? 'NEVER_STARTED';
                                        $incompleteAttempt = $quizStats['incomplete_attempt'] ?? null;
                                        $bestScore = $quizStats['best_score'] ?? 0;
                                        $hasPassed = $quizStats['has_passed'] ?? false;
                                        $passingScore = $quiz->passing_score ?? 70;
                                    @endphp

                                    <div class="flex items-center justify-between p-4 border border-red-200 dark:border-red-700 rounded-lg mb-4 last:mb-0 bg-red-50 dark:bg-red-900/10">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                @if($state === 'NEVER_STARTED')
                                                    <i class="fas fa-play-circle text-red-600 text-2xl"></i>
                                                @elseif($state === 'IN_PROGRESS')
                                                    <i class="fas fa-redo text-red-600 text-2xl"></i>
                                                @else
                                                    @if($hasPassed)
                                                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                                                    @else
                                                        <i class="fas fa-times-circle text-orange-600 text-2xl"></i>
                                                    @endif
                                                @endif
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-red-700 dark:text-white">
                                                    {{ $quiz->title }}
                                                    @if($state === 'COMPLETED' && $hasPassed)
                                                        <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Aprovado</span>
                                                    @elseif($state === 'COMPLETED' && !$hasPassed)
                                                        <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">Reprovado</span>
                                                    @endif
                                                </h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    <span>
                                                        <i class="fas fa-clock mr-1"></i>
                                                        {{ $quiz->time_limit_minutes ?? 30 }} min
                                                    </span>
                                                    <span>
                                                        <i class="fas fa-question-circle mr-1"></i>
                                                        {{ is_array($quiz->questions) ? count($quiz->questions) : 0 }} questões
                                                    </span>
                                                    @if($state === 'COMPLETED')
                                                        <span class="text-red-600">
                                                            <i class="fas fa-star mr-1"></i>
                                                            {{ round($bestScore) }}%
                                                        </span>
                                                    @endif
                                                </div>
                                                @if($state === 'COMPLETED')
                                                    <span class="inline-block mt-2 text-xs text-gray-500 dark:text-gray-400">
                                                        @if($hasPassed)
                                                            Aprovado ({{ round($bestScore) }}% - Mín: {{ round($passingScore) }}%)
                                                        @else
                                                            Reprovado ({{ round($bestScore) }}% - Mín: {{ round($passingScore) }}%)
                                                        @endif
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            @if($state === 'NEVER_STARTED')
                                                <!-- Nunca iniciado: Botão Iniciar (Vermelho para Quiz) -->
                                                <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $quiz->slug]) }}"
                                                   class="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors inline-flex items-center">
                                                    <i class="fas fa-play mr-2"></i>
                                                    Iniciar
                                                </a>
                                            @else
                                                <!-- Qualquer outro estado: Refazer (Vermelho para Quiz) -->
                                                <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $quiz->slug]) }}"
                                                   class="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors inline-flex items-center">
                                                    <i class="fas fa-redo mr-2"></i>
                                                    Refazer
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Bloco 2: Desafios -->
                    @if($challenges->count() > 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3 mb-2">
                                    <i class="fas fa-trophy text-orange-600 text-xl"></i>
                                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Desafios</h2>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    Desafios especiais para testar suas habilidades. Necessário pelo menos 80% para aprovação.
                                </p>
                            </div>

                            <div class="p-6">
                                @foreach($challenges as $challenge)
                                    @php
                                        $student = auth('students')->user();
                                        $challengeStats = $student ? $student->getQuizStats($challenge) : null;
                                        $state = $challengeStats['state'] ?? 'NEVER_STARTED';
                                        $incompleteAttempt = $challengeStats['incomplete_attempt'] ?? null;
                                        $bestScore = $challengeStats['best_score'] ?? 0;
                                        $hasPassed = $challengeStats['has_passed'] ?? false;
                                        $passingScore = $challenge->passing_score ?? 80;
                                    @endphp

                                    <div class="flex items-center justify-between p-4 border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20 rounded-lg mb-4 last:mb-0">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                @if($state === 'NEVER_STARTED')
                                                    <i class="fas fa-trophy text-orange-600 text-2xl"></i>
                                                @elseif($state === 'IN_PROGRESS')
                                                    <i class="fas fa-redo text-orange-600 text-2xl"></i>
                                                @else
                                                    @if($hasPassed)
                                                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                                                    @else
                                                        <i class="fas fa-times-circle text-orange-600 text-2xl"></i>
                                                    @endif
                                                @endif
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-orange-600">
                                                    {{ $challenge->title }}
                                                    @if($state === 'COMPLETED' && $hasPassed)
                                                        <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Aprovado</span>
                                                    @elseif($state === 'COMPLETED' && !$hasPassed)
                                                        <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">Reprovado</span>
                                                    @endif
                                                    <span class="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded">Desafio</span>
                                                </h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    <span>
                                                        <i class="fas fa-clock mr-1"></i>
                                                        {{ $challenge->time_limit_minutes ?? 60 }} min
                                                    </span>
                                                    <span>
                                                        <i class="fas fa-question-circle mr-1"></i>
                                                        {{ is_array($challenge->questions) ? count($challenge->questions) : 0 }} questões
                                                    </span>
                                                    <span>
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                        {{ round($passingScore) }}% mínimo
                                                    </span>
                                                    @if($state === 'COMPLETED')
                                                        <span class="text-orange-600">
                                                            <i class="fas fa-star mr-1"></i>
                                                            {{ round($bestScore) }}%
                                                        </span>
                                                    @endif
                                                </div>
                                                @if($state === 'COMPLETED')
                                                    <span class="inline-block mt-2 text-xs text-gray-500 dark:text-gray-400">
                                                        @if($hasPassed)
                                                            Aprovado ({{ round($bestScore) }}% - Mín: {{ round($passingScore) }}%)
                                                        @else
                                                            Reprovado ({{ round($bestScore) }}% - Mín: {{ round($passingScore) }}%)
                                                        @endif
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            @if($state === 'NEVER_STARTED')
                                                <!-- Nunca iniciado: Botão Iniciar (Laranja para Desafio) -->
                                                <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $challenge->slug]) }}"
                                                   class="px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors inline-flex items-center">
                                                    <i class="fas fa-play mr-2"></i>
                                                    Iniciar
                                                </a>
                                            @else
                                                <!-- Qualquer outro estado: Refazer (Laranja para Desafio) -->
                                                <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $challenge->slug]) }}"
                                                   class="px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors inline-flex items-center">
                                                    <i class="fas fa-redo mr-2"></i>
                                                    Refazer
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Bloco 3: Testes Práticos (Certificação) -->
                    @if($exams->count() > 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3 mb-2">
                                    <i class="fas fa-certificate text-purple-600 text-xl"></i>
                                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Testes Práticos</h2>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    Avaliações práticas mais avançadas. Necessário pelo menos 75% para aprovação.
                                </p>
                            </div>

                            <div class="p-6">
                                @foreach($exams as $exam)
                                    @php
                                        $student = auth('students')->user();
                                        $examStats = $student ? $student->getQuizStats($exam) : null;
                                        $state = $examStats['state'] ?? 'NEVER_STARTED';
                                        $incompleteAttempt = $examStats['incomplete_attempt'] ?? null;
                                        $bestScore = $examStats['best_score'] ?? 0;
                                        $hasPassed = $examStats['has_passed'] ?? false;
                                        $passingScore = $exam->passing_score ?? 75;
                                    @endphp

                                    <div class="flex items-center justify-between p-4 border border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-900/20 rounded-lg mb-4 last:mb-0">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                @if($state === 'NEVER_STARTED')
                                                    <i class="fas fa-graduation-cap text-purple-600 text-2xl"></i>
                                                @elseif($state === 'IN_PROGRESS')
                                                    <i class="fas fa-redo text-purple-600 text-2xl"></i>
                                                @else
                                                    @if($hasPassed)
                                                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                                                    @else
                                                        <i class="fas fa-times-circle text-orange-600 text-2xl"></i>
                                                    @endif
                                                @endif
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-purple-600">
                                                    {{ $exam->title }}
                                                    @if($state === 'COMPLETED' && $hasPassed)
                                                        <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Aprovado</span>
                                                    @elseif($state === 'COMPLETED' && !$hasPassed)
                                                        <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">Reprovado</span>
                                                    @endif
                                                    <span class="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">Final</span>
                                                </h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    <span>
                                                        <i class="fas fa-clock mr-1"></i>
                                                        {{ $exam->time_limit_minutes ?? 45 }} min
                                                    </span>
                                                    <span>
                                                        <i class="fas fa-question-circle mr-1"></i>
                                                        {{ is_array($exam->questions) ? count($exam->questions) : 0 }} questões
                                                    </span>
                                                    <span>
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                        {{ round($passingScore) }}% mínimo
                                                    </span>
                                                    @if($state === 'COMPLETED')
                                                        <span class="text-purple-600">
                                                            <i class="fas fa-star mr-1"></i>
                                                            {{ round($bestScore) }}%
                                                        </span>
                                                    @endif
                                                </div>
                                                @if($state === 'COMPLETED')
                                                    <span class="inline-block mt-2 text-xs text-gray-500 dark:text-gray-400">
                                                        @if($hasPassed)
                                                            Aprovado ({{ round($bestScore) }}% - Mín: {{ round($passingScore) }}%)
                                                        @else
                                                            Reprovado ({{ round($bestScore) }}% - Mín: {{ round($passingScore) }}%)
                                                        @endif
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            @if($state === 'NEVER_STARTED')
                                                <!-- Nunca iniciado: Botão Iniciar (Roxo para Teste) -->
                                                <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $exam->slug]) }}"
                                                   class="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors inline-flex items-center">
                                                    <i class="fas fa-play mr-2"></i>
                                                    Iniciar
                                                </a>
                                            @else
                                                <!-- Qualquer outro estado: Refazer (Roxo para Teste) -->
                                                <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $exam->slug]) }}"
                                                   class="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors inline-flex items-center">
                                                    <i class="fas fa-redo mr-2"></i>
                                                    Refazer
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Estado Vazio -->
                    @if($quizzes->count() === 0 && $exams->count() === 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-12 text-center">
                                <i class="fas fa-clipboard-list text-gray-400 text-5xl mb-6"></i>
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    Nenhum teste disponível
                                </h3>
                                <p class="text-gray-500 dark:text-gray-400">
                                    Este módulo ainda não possui quizzes ou testes configurados.
                                </p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar Direita (3 colunas no desktop, full width no mobile) -->
                <div class="lg:col-span-3 space-y-6">
                    <!-- Card Material de Estudo -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center space-x-3 mb-2">
                                <i class="fas fa-book text-blue-600 text-xl"></i>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Material de Estudo</h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                {{ $totalContents }} materiais disponíveis para estudo antes dos quizzes.
                            </p>
                        </div>

                        <!-- Progresso do Módulo dentro do card Material de Estudo -->
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-base font-semibold text-gray-900 dark:text-white">Progresso do Módulo</h4>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $completedContents }}/{{ $totalContents }} materiais completos
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
                                <div class="bg-green-600 h-3 rounded-full transition-all duration-300"
                                     style="width: {{ round($progressPercentage) }}%"></div>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ round($progressPercentage) }}% concluído
                            </p>
                        </div>

                        <div class="p-6">
                            <a href="{{ route('student.module.show', [$course->slug, $module->slug]) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
                                <i class="fas fa-book-open mr-2"></i>
                                Estudar Materiais
                            </a>
                        </div>
                    </div>

                    <!-- Card de Estatísticas -->
                    {{-- @php
                        $hasActivity = $moduleStats['overall']['tests_passed'] > 0 ||
                                      $moduleStats['quizzes']['total_attempts'] > 0 ||
                                      $moduleStats['exams']['total_attempts'] > 0;
                    @endphp --}}

                    {{-- <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center space-x-3 mb-2">
                                <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Seu Progresso</h2>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                @if($hasActivity)
                                    Acompanhe seu desempenho neste módulo.
                                @else
                                    Comece fazendo os quizzes para ver suas estatísticas aqui.
                                @endif
                            </p>
                        </div>

                        <div class="p-6">
                            @if($hasActivity)

                                <!-- Resumo Compacto -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    @if($moduleStats['totals']['total_quizzes'] > 0)
                                    <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                        <div class="text-lg font-bold text-red-600">{{ $moduleStats['quizzes']['total_passed'] }}/{{ $moduleStats['totals']['total_quizzes'] }}</div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">Quizzes Aprovados</div>
                                        @if($moduleStats['quizzes']['best_score'] > 0)
                                            <div class="text-xs text-red-600 mt-1">{{ number_format($moduleStats['quizzes']['best_score'], 0) }}%</div>
                                        @endif
                                    </div>
                                    @endif

                                    @if($moduleStats['totals']['total_exams'] > 0)
                                    <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                        <div class="text-lg font-bold text-purple-600">{{ $moduleStats['exams']['total_passed'] }}/{{ $moduleStats['totals']['total_exams'] }}</div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">Testes Aprovados</div>
                                        @if($moduleStats['exams']['best_score'] > 0)
                                            <div class="text-xs text-purple-600 mt-1">{{ number_format($moduleStats['exams']['best_score'], 0) }}%</div>
                                        @endif
                                    </div>
                                    @endif
                                </div>

                                <!-- Detalhes Relevantes -->
                                @if($moduleStats['quizzes']['total_attempts'] > 0 || $moduleStats['exams']['total_attempts'] > 0)
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        @if($moduleStats['quizzes']['total_attempts'] > 0)
                                        <div class="text-center">
                                            <div class="font-medium text-gray-900 dark:text-white">{{ $moduleStats['quizzes']['total_attempts'] }}</div>
                                            <div class="text-xs text-gray-500">Tentativas Quiz</div>
                                        </div>
                                        @endif

                                        @if($moduleStats['exams']['total_attempts'] > 0)
                                        <div class="text-center">
                                            <div class="font-medium text-gray-900 dark:text-white">{{ $moduleStats['exams']['total_attempts'] }}</div>
                                            <div class="text-xs text-gray-500">Tentativas Teste</div>
                                        </div>
                                        @endif

                                        @if($moduleStats['quizzes']['average_score'] > 0)
                                        <div class="text-center">
                                            <div class="font-medium text-gray-900 dark:text-white">{{ number_format($moduleStats['quizzes']['average_score'], 0) }}%</div>
                                            <div class="text-xs text-gray-500">Média Quiz</div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                @endif
                            @else
                                <!-- Estado Inicial -->
                                <div class="text-center py-8">
                                    <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma atividade ainda</h3>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">
                                        Suas estatísticas aparecerão aqui conforme você faz os quizzes e testes.
                                    </p>
                                    <div class="text-xs text-gray-400">
                                        📊 {{ $moduleStats['totals']['total_quizzes'] }} quizzes • 🎓 {{ $moduleStats['totals']['total_exams'] }} testes disponíveis
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </div>
</x-layouts.student>