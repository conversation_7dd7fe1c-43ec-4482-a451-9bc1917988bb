<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->string('key')->unique(); // Chave da configuração
            $table->text('value')->nullable(); // Valor da configuração
            $table->string('type')->default('string'); // Tipo: string, integer, boolean, json, etc.
            $table->string('group')->nullable(); // Grupo da configuração (quiz, email, etc.)
            $table->text('description')->nullable(); // Descrição da configuração
            $table->boolean('is_public')->default(false); // Se é visível publicamente
            $table->boolean('is_editable')->default(true); // Se pode ser editada
            $table->timestamps();
            
            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            
            // Índices para performance
            $table->index(['company_id', 'key']); // Multi-tenancy + chave
            $table->index(['group']); // Grupo
            $table->index(['is_public']); // Públicas
            $table->index(['is_editable']); // Editáveis
            $table->index(['company_id', 'group']); // Multi-tenancy + grupo
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_settings');
    }
};
