@extends('layouts.panel')

@section('title', '<PERSON><PERSON><PERSON><PERSON>')
@section('page_title', '<PERSON>ici<PERSON><PERSON>')

@push('styles')
    @vite(['resources/css/quill-editor.css'])
@endpush

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Adicionar Conteúdo ao módulo ' . $module->title,
            'description' => 'Adicione um novo conteúdo ao módulo ' . $module->title . '.',
            'actions' => [
                [
                    'route' => route('admin.modules.edit', $module->id),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ]
            ]
        ])

        <div class="p-6">
            @include('panel.admin.includes.alerts')

            <form action="{{ route('admin.modules.contents.store', $module->id) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf

                <div class="space-y-6">
                    <!-- Título -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Título <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" id="title"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('title') border-red-500 @enderror"
                            value="{{ old('title') }}"
                            placeholder="Digite o título do conteúdo" required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tipo de Conteúdo -->
                    <div>
                        <label for="content_type" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Tipo de Conteúdo <span class="text-red-500">*</span>
                        </label>
                        <select name="content_type" id="content_type"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('content_type') border-red-500 @enderror"
                            required>
                            <option value="">Selecione o tipo</option>
                            <option value="media" {{ old('content_type') == 'media' ? 'selected' : '' }}>Imagem / PDF / MP3</option>
                            <option value="video" {{ old('content_type') == 'video' ? 'selected' : '' }}>Vídeo</option>
                            <option value="link" {{ old('content_type') == 'link' ? 'selected' : '' }}>Link Externo</option>
                        </select>
                        @error('content_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Campo para Mídia (Imagem / PDF / MP3) -->
                    <div id="media-field" class="content-field hidden">
                        
                    
                    
                    
                    
                     <!-- Thumbnail do curso -->
                            <div class="media-selector">
                                <div class="flex justify-between items-center mb-2">
                       
                                <button type="button" 
                                class="open-media-modal w-full px-4 py-8 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg hover:border-trends-primary dark:hover:border-trends-primary transition-colors group"
                               data-mediable-type="App\Models\PlgModuleContent"
                                        data-tag="media_id"
                                        data-input-id="media_id"
                                        data-preview-id="thumbnail-preview" 
                                        data-multiple="false">
                                <div class="text-center">
                                    <i class="fas fa-image text-2xl text-zinc-400 dark:text-zinc-600 group-hover:text-trends-primary transition-colors"></i>
                                    <p class="mt-2 text-sm text-zinc-500 dark:text-zinc-400 group-hover:text-trends-primary transition-colors">
                                        Clique para selecionar uma mídia!
                                    </p>
                                </div>
                            </button>

                                </div>
                                
                                
                                <input type="hidden" name="media_id" class="media-input" data-preview-id="thumbnail-preview" id="media_id" value="{{ old('media_id') }}">

                                
                                  <div class="media-preview hidden" id="thumbnail-preview">
                                    <!-- Preview da mídia será inserido aqui -->
                                    </div>

                                <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendado: 1280x720px</p>

                            </div>
                    

                    </div>



                    <!-- Campo para Vídeo (Embed) -->
                    <div id="video-field" class="content-field hidden">
                        <label for="video_embed" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Código de Incorporação (Embed) <span class="text-red-500">*</span>
                        </label>
                        <textarea name="video_embed" id="video_embed" rows="4"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('video_embed') border-red-500 @enderror"
                            placeholder="Cole aqui o código de incorporação do vídeo (YouTube, Vimeo, etc.)...">{{ old('video_embed') }}</textarea>
                        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
                            Exemplo: &lt;iframe src="https://www.youtube.com/embed/VIDEO_ID"&gt;&lt;/iframe&gt;
                        </p>
                        @error('video_embed')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Campo para Link Externo -->
                    <div id="link-field" class="content-field hidden">
                        <label for="external_link" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            URL do Link <span class="text-red-500">*</span>
                        </label>
                        <input type="url" name="external_link" id="external_link"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('external_link') border-red-500 @enderror"
                            value="{{ old('external_link') }}"
                            placeholder="https://exemplo.com">
                        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
                            Digite a URL completa do link externo
                        </p>
                        @error('external_link')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Descrição -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Descrição
                        </label>
                        <div id="description-editor" class="editor" data-input="description"
                            data-placeholder="Digite uma descrição para o conteúdo..."></div>
                        <input type="hidden" name="description" id="description"
                            value="{{ old('description') }}">
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Duração -->
                    <div>
                        <label for="duration" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Duração (em minutos)
                        </label>
                        <input type="number" name="duration" id="duration" min="0"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('duration') border-red-500 @enderror"
                            value="{{ old('duration') }}"
                            placeholder="Duração estimada em minutos">
                        @error('duration')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Ordem -->
                    <div>
                        <label for="order" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Ordem
                        </label>
                        <input type="number" name="order" id="order" min="0"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('order') border-red-500 @enderror"
                            value="{{ old('order') }}">
                        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">Ordem de exibição do conteúdo (0 = primeiro)</p>
                        @error('order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Status
                        </label>
                        <div class="flex items-center gap-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="active" value="1"
                                    {{ old('active', '1') == '1' ? 'checked' : '' }}
                                    class="h-4 w-4 text-trends-primary border-zinc-300 dark:border-zinc-700 focus:ring-trends-primary/30 dark:bg-zinc-800/50">
                                <span class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">Ativo</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="active" value="0"
                                    {{ old('active') == '0' ? 'checked' : '' }}
                                    class="h-4 w-4 text-trends-primary border-zinc-300 dark:border-zinc-700 focus:ring-trends-primary/30 dark:bg-zinc-800/50">
                                <span class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">Inativo</span>
                            </label>
                        </div>
                        @error('active')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Botões -->
                <div class="flex justify-end gap-4">
                    <a href="{{ route('admin.modules.edit', $module->id) }}"
                        class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                        <i class="fas fa-times"></i>
                        <span>Cancelar</span>
                    </a>
                    <button type="submit"
                        class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                        <i class="fas fa-save"></i>
                        <span>Salvar Conteúdo</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
@vite(['resources/js/quill-editor.js'])
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const contentTypeSelect = document.getElementById('content_type');
        const mediaField = document.getElementById('media-field');
        const videoField = document.getElementById('video-field');
        const linkField = document.getElementById('link-field');
        const contentMediaInput = document.getElementById('content_media');
        const contentMediaPreview = document.getElementById('content_media-preview');

        // Função para esconder todos os campos de conteúdo
        function hideAllContentFields() {
            mediaField.classList.add('hidden');
            videoField.classList.add('hidden');
            linkField.classList.add('hidden');
        }

        // Função para mostrar campo específico baseado no tipo
        function showContentField(type) {
            hideAllContentFields();
            
            switch(type) {
                case 'media':
                    mediaField.classList.remove('hidden');
                    break;
                case 'video':
                    videoField.classList.remove('hidden');
                    break;
                case 'link':
                    linkField.classList.remove('hidden');
                    break;
            }
        }

        // Listener para mudança no tipo de conteúdo
        contentTypeSelect.addEventListener('change', function() {
            showContentField(this.value);
        });

        /*
        // Preview dinâmico da mídia selecionada
        if (contentMediaInput && contentMediaPreview) {
            contentMediaInput.addEventListener('mediaSelected', function(e) {
                const media = e.detail.media;
                if (media && media.url) {
                    let previewHtml = '';
                    
                    // Verificar se é imagem, PDF ou áudio
                    if (media.mime_type && media.mime_type.startsWith('image/')) {
                        previewHtml = `
                            <div class="relative w-64">
                                <img src="${media.url}" alt="${media.filename}" class="w-full h-32 object-cover rounded-lg">
                                <button type="button" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        `;
                    } else if (media.mime_type && media.mime_type === 'application/pdf') {
                        previewHtml = `
                            <div class="relative bg-red-100 p-4 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-file-pdf text-red-500 text-2xl"></i>
                                    <div class="flex-1">
                                        <p class="font-medium text-red-800">${media.filename}</p>
                                        <p class="text-sm text-red-600">Documento PDF</p>
                                    </div>
                                    <button type="button" class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                    } else if (media.mime_type && media.mime_type.startsWith('audio/')) {
                        previewHtml = `
                            <div class="relative bg-blue-100 p-4 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-music text-blue-500 text-2xl"></i>
                                    <div class="flex-1">
                                        <p class="font-medium text-blue-800">${media.filename}</p>
                                        <p class="text-sm text-blue-600">Arquivo de Áudio</p>
                                    </div>
                                    <button type="button" class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                    } else {
                        previewHtml = `
                            <div class="relative bg-gray-100 p-4 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-file text-gray-500 text-2xl"></i>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-800">${media.filename}</p>
                                        <p class="text-sm text-gray-600">Arquivo</p>
                                    </div>
                                    <button type="button" class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                    }
                    
                    contentMediaPreview.innerHTML = previewHtml;
                    contentMediaPreview.classList.remove('hidden');
                }
            });
        }

        // Função global para remover preview de mídia
        window.removeMediaPreview = function(button) {
            const previewContainer = button.closest('.media-preview');
            const inputId = 'content_media';
            const hiddenInput = document.getElementById(inputId);
            
            if (hiddenInput) {
                hiddenInput.value = '';
            }
            
            previewContainer.classList.add('hidden');
            previewContainer.innerHTML = '';
        };
        */

        // Inicializar estado baseado no valor selecionado (se houver)
        if (contentTypeSelect.value) {
            showContentField(contentTypeSelect.value);
        }
    });
</script>
@endpush 