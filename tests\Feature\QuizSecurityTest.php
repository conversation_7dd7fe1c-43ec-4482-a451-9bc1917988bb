<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PlgTest;
use App\Models\PlgTestAttempt;
use App\Models\PlgStudent;
use App\Models\PlgModule;
use App\Models\PlgCourse;
use App\Models\PlgQuestion;
use App\Models\PlgQuestionAnswer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class QuizSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected $student;
    protected $test;
    protected $attempt;
    protected $question;

    protected function setUp(): void
    {
        parent::setUp();

        // Criar dados de teste
        $course = PlgCourse::create([
            'company_id' => 1,
            'title' => 'Curso Teste',
            'slug' => 'curso-teste',
            'active' => true
        ]);

        $module = PlgModule::create([
            'company_id' => 1,
            'course_id' => $course->id,
            'title' => 'Módulo Teste',
            'slug' => 'modulo-teste',
            'active' => true
        ]);

        $this->question = PlgQuestion::create([
            'company_id' => 1,
            'question' => 'Qual é a resposta correta?',
            'question_type' => 'single'
        ]);

        // Criar alternativas
        PlgQuestionAnswer::create([
            'company_id' => 1,
            'question_id' => $this->question->id,
            'answer_number' => 1,
            'answer' => 'Resposta incorreta 1',
            'correct' => false
        ]);

        PlgQuestionAnswer::create([
            'company_id' => 1,
            'question_id' => $this->question->id,
            'answer_number' => 2,
            'answer' => 'Resposta correta',
            'correct' => true,
            'explanation' => 'Esta é a explicação da resposta correta'
        ]);

        PlgQuestionAnswer::create([
            'company_id' => 1,
            'question_id' => $this->question->id,
            'answer_number' => 3,
            'answer' => 'Resposta incorreta 2',
            'correct' => false
        ]);

        $this->test = PlgTest::create([
            'company_id' => 1,
            'title' => 'Teste Segurança',
            'description' => 'Teste de segurança',
            'module_id' => $module->id,
            'questions' => [['id' => $this->question->id, 'order' => 0]],
            'randomize_questions' => false,
            'randomize_alternatives' => true,
            'active' => true
        ]);

        $this->student = PlgStudent::create([
            'company_id' => 1,
            'name' => 'Estudante Teste',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'active' => true
        ]);

        $this->attempt = PlgTestAttempt::create([
            'company_id' => 1,
            'test_id' => $this->test->id,
            'student_id' => $this->student->id,
            'attempt_number' => 1,
            'total_questions' => 1,
            'status' => 'in_progress',
            'started_at' => now()
        ]);
    }

    /** @test */
    public function getCurrentQuestion_should_not_expose_correct_answers()
    {
        // Autenticar como estudante
        Auth::guard('students')->login($this->student);

        // Fazer requisição para obter questão
        $response = $this->getJson("/panel/student/quiz/{$this->attempt->id}/question?question=1");

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verificar que a estrutura básica está presente
        $this->assertArrayHasKey('question', $data);
        $this->assertArrayHasKey('answers', $data['question']);
        
        // Verificar que informações sensíveis NÃO estão presentes
        foreach ($data['question']['answers'] as $answer) {
            $this->assertArrayNotHasKey('correct', $answer, 'Campo "correct" não deve estar presente na resposta');
            $this->assertArrayNotHasKey('explanation', $answer, 'Campo "explanation" não deve estar presente na resposta');
        }
        
        // Verificar que informações necessárias estão presentes
        foreach ($data['question']['answers'] as $answer) {
            $this->assertArrayHasKey('id', $answer);
            $this->assertArrayHasKey('answer', $answer);
            $this->assertArrayHasKey('answer_number', $answer);
        }
    }

    /** @test */
    public function submitAnswer_should_validate_answer_belongs_to_question()
    {
        // Autenticar como estudante
        Auth::guard('students')->login($this->student);

        // Criar uma questão diferente com alternativas
        $otherQuestion = PlgQuestion::create([
            'company_id' => 1,
            'question' => 'Outra questão?',
            'question_type' => 'single'
        ]);

        $otherAnswer = PlgQuestionAnswer::create([
            'company_id' => 1,
            'question_id' => $otherQuestion->id,
            'answer_number' => 1,
            'answer' => 'Resposta de outra questão',
            'correct' => true
        ]);

        // Tentar submeter resposta de outra questão
        $response = $this->postJson("/panel/student/quiz/{$this->attempt->id}/answer", [
            'question_number' => 1,
            'answer_id' => $otherAnswer->id
        ]);

        $response->assertStatus(400);
        $response->assertJson(['error' => 'Resposta inválida para esta questão.']);
    }

    /** @test */
    public function submitAnswer_should_return_correct_info_only_after_submission()
    {
        // Autenticar como estudante
        Auth::guard('students')->login($this->student);

        // Obter ID da resposta correta
        $correctAnswer = PlgQuestionAnswer::where('question_id', $this->question->id)
            ->where('correct', true)
            ->first();

        // Submeter resposta correta
        $response = $this->postJson("/panel/student/quiz/{$this->attempt->id}/answer", [
            'question_number' => 1,
            'answer_id' => $correctAnswer->id
        ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verificar que informações sobre correção são retornadas APENAS após submissão
        $this->assertArrayHasKey('is_correct', $data);
        $this->assertArrayHasKey('correct_answer', $data);
        $this->assertTrue($data['is_correct']);
        $this->assertEquals($correctAnswer->id, $data['correct_answer']['id']);
        $this->assertEquals('Esta é a explicação da resposta correta', $data['correct_answer']['explanation']);
    }
}
