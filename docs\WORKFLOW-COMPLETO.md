# 📋 Guia Completo de Workflow - Sistema de Gestão de Cursos e Quizzes

## 📖 Índice
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Estrutura de Branches](#estrutura-de-branches)
3. [Configuração Inicial](#configuração-inicial)
4. [Ambientes e Configurações](#ambientes-e-configurações)
5. [CI/CD e Automação](#cicd-e-automação)
6. [Scripts de Deploy](#scripts-de-deploy)
7. [Workflow Diário](#workflow-diário)
8. [Situações Específicas](#situações-específicas)
9. [Troubleshooting](#troubleshooting)
10. [Checklist de Deploy](#checklist-de-deploy)

---

## 🎯 Visão Geral

### O que é este workflow?
Este é um sistema de desenvolvimento que organiza como o código sai do seu computador e chega aos usuários finais, passando por etapas de teste e validação.

### Por que usar este workflow?
- **Segurança**: Evita que bugs cheguem à produção
- **Organização**: Cada desenvolvedor sabe exatamente o que fazer
- **Automação**: Reduz trabalho manual e erros humanos
- **Rastreabilidade**: Histórico completo de todas as mudanças

### Fluxo Resumido
```
Desenvolvimento Local → Staging (Teste) → Produção (Usuários)
     (seu PC)           (quizz.sytes.net)   (app.trendsquiz.com.br)
```

---

## 🌿 Estrutura de Branches

### Branches Principais

#### `main` - Produção
- **O que é**: Código que está rodando para os usuários finais
- **Quando usar**: Apenas quando o código foi 100% testado
- **Quem pode mexer**: Apenas o líder técnico ou após aprovação
- **Deploy**: Automático para https://app.trendsquiz.com.br

#### `staging` - Homologação
- **O que é**: Ambiente de testes que simula a produção
- **Quando usar**: Para testar funcionalidades antes de ir para produção
- **Quem pode mexer**: Desenvolvedores após testes locais
- **Deploy**: Automático para https://quizz.sytes.net

#### `develop` - Desenvolvimento
- **O que é**: Branch principal de desenvolvimento
- **Quando usar**: Base para criar novas funcionalidades
- **Quem pode mexer**: Todos os desenvolvedores
- **Deploy**: Não tem deploy automático

### Branches Temporárias

#### `feature/nome-da-funcionalidade`
- **O que é**: Branch para desenvolver uma funcionalidade específica
- **Exemplo**: `feature/sistema-de-quiz`, `feature/relatorio-alunos`
- **Quando criar**: Sempre que for desenvolver algo novo
- **Quando deletar**: Após fazer merge para `develop`

#### `hotfix/nome-da-correcao`
- **O que é**: Branch para correções urgentes em produção
- **Exemplo**: `hotfix/erro-login`, `hotfix/bug-calculo-nota`
- **Quando criar**: Quando há um bug crítico em produção
- **Diferença**: Sai direto da `main` e volta para `main`, `staging` e `develop`

#### `release/v1.x.x`
- **O que é**: Branch para preparar uma nova versão
- **Exemplo**: `release/v1.2.0`
- **Quando criar**: Quando várias funcionalidades estão prontas para produção
- **O que fazer**: Ajustes finais, testes, correção de bugs menores

---

## ⚙️ Configuração Inicial

### 1. Configuração do Repositório GitHub

```bash
# Clone o repositório
git clone https://github.com/seu-usuario/sistema-cursos.git
cd sistema-cursos

# Criar e configurar branches
git checkout -b develop
git push -u origin develop

git checkout -b staging
git push -u origin staging

git checkout main
```

### 2. Configuração de Secrets no GitHub

Vá em: `Repositório → Settings → Secrets and variables → Actions`

**Para Staging:**
- `STAGING_SSH_HOST`: IP ou domínio do servidor de staging
- `STAGING_SSH_USERNAME`: usuário SSH do staging
- `STAGING_SSH_PRIVATE_KEY`: chave privada SSH do staging
- `STAGING_SSH_PORT`: porta SSH (geralmente 22)

**Para Produção:**
- `PROD_SSH_HOST`: IP ou domínio do servidor de produção
- `PROD_SSH_USERNAME`: usuário SSH da produção
- `PROD_SSH_PRIVATE_KEY`: chave privada SSH da produção
- `PROD_SSH_PORT`: porta SSH (geralmente 22)

### 3. Configuração Local

```bash
# Instalar dependências
composer install
npm install

# Configurar ambiente local
cp .env.example .env
php artisan key:generate

# Configurar banco de dados no .env
# DB_DATABASE=sistema_cursos_local
# DB_USERNAME=root
# DB_PASSWORD=

# Rodar migrações
php artisan migrate
php artisan db:seed
```

---

## 🌍 Ambientes e Configurações

### Desenvolvimento Local (http://127.0.0.1:8000)

**Características:**
- Banco de dados local
- Debug ativado
- Logs detalhados
- Hot reload para desenvolvimento

**Configuração (.env):**
```env
APP_NAME="Sistema Cursos - Local"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sistema_cursos_local
DB_USERNAME=root
DB_PASSWORD=

MAIL_MAILER=log
QUEUE_CONNECTION=sync
```

### Staging/Homologação (https://quizz.sytes.net)

**Para que serve:**
- Testar funcionalidades em ambiente similar à produção
- Validar integrações com APIs externas
- Testes de performance
- Demonstrações para clientes

**Configuração (.env.staging):**
```env
APP_NAME="Sistema Cursos - Staging"
APP_ENV=staging
APP_DEBUG=false
APP_URL=https://quizz.sytes.net

DB_CONNECTION=mysql
DB_HOST=staging_db_host
DB_DATABASE=staging_database
DB_USERNAME=staging_user
DB_PASSWORD=staging_password

MAIL_MAILER=smtp
QUEUE_CONNECTION=database
CACHE_STORE=database
```

### Produção (https://app.trendsquiz.com.br)

**Características:**
- Ambiente real dos usuários
- Performance otimizada
- Logs de erro apenas
- Backup automático

**Configuração (.env.production):**
```env
APP_NAME="TrendsQuiz"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://app.trendsquiz.com.br

DB_CONNECTION=mysql
DB_HOST=production_db_host
DB_DATABASE=production_database
DB_USERNAME=production_user
DB_PASSWORD=production_password

MAIL_MAILER=smtp
QUEUE_CONNECTION=database
CACHE_STORE=redis
```

---

## 🤖 CI/CD e Automação

### O que é CI/CD?
- **CI (Continuous Integration)**: Integração contínua - testa o código automaticamente
- **CD (Continuous Deployment)**: Deploy contínuo - publica o código automaticamente

### Workflow de Staging

**Quando acontece:** Sempre que alguém faz push na branch `staging`

**O que faz:**
1. Baixa o código da branch `staging`
2. Instala dependências PHP (Composer)
3. Instala dependências JavaScript (NPM)
4. Compila assets (CSS/JS)
5. Conecta no servidor de staging via SSH
6. Atualiza o código no servidor
7. Roda migrações do banco
8. Limpa e recria cache

**Arquivo:** `.github/workflows/staging.yml`

### Workflow de Produção

**Quando acontece:** 
- Push na branch `main` OU
- Acionamento manual (com confirmação)

**O que faz:**
1. Coloca o site em manutenção
2. Faz backup do banco de dados
3. Atualiza o código
4. Roda migrações
5. Otimiza performance
6. Tira o site de manutenção

**Arquivo:** `.github/workflows/production.yml`

### Para Hospedagem Compartilhada

**Problema:** Muitas hospedagens compartilhadas não têm SSH ou acesso limitado

**Solução:** Script PHP que recebe webhooks do GitHub

**Como funciona:**
1. GitHub envia notificação para `https://seusite.com/deploy.php`
2. Script verifica se a requisição é válida
3. Executa comandos de deploy via PHP
4. Retorna status do deploy

---

## 📜 Scripts de Deploy

### Script para Staging

**Arquivo:** `scripts/deploy-staging.sh`

**O que faz:**
- Atualiza código da branch staging
- Instala dependências
- Compila assets
- Roda migrações
- Otimiza cache

**Quando usar:** Automaticamente via GitHub Actions

### Script para Produção

**Arquivo:** `scripts/deploy-production.sh`

**O que faz:**
- Ativa modo manutenção
- Faz backup do banco
- Atualiza código
- Otimiza tudo
- Desativa modo manutenção

**Quando usar:** Automaticamente via GitHub Actions ou manualmente

### Script para Hospedagem Compartilhada

**Arquivo:** `scripts/shared-hosting-deploy.php`

**O que faz:**
- Recebe webhook do GitHub
- Valida segurança
- Executa comandos de deploy
- Retorna status

**Quando usar:** Quando não há acesso SSH

---

## 🔄 Workflow Diário

### Desenvolvendo uma Nova Funcionalidade

#### 1. Preparação
```bash
# Ir para develop e atualizar
git checkout develop
git pull origin develop

# Criar branch da funcionalidade
git checkout -b feature/sistema-notificacoes
```

#### 2. Desenvolvimento
```bash
# Fazer alterações no código
# Testar localmente
php artisan serve

# Fazer commits frequentes
git add .
git commit -m "Adiciona modelo de notificação"
git commit -m "Implementa envio de email"
git commit -m "Adiciona testes unitários"
```

#### 3. Finalização
```bash
# Enviar para GitHub
git push origin feature/sistema-notificacoes

# Criar Pull Request no GitHub
# Target: develop ← feature/sistema-notificacoes
```

#### 4. Após Aprovação
```bash
# Merge foi feito via GitHub
# Deletar branch local
git checkout develop
git pull origin develop
git branch -d feature/sistema-notificacoes
```

### Testando em Staging

#### 1. Preparar Staging
```bash
# Ir para staging
git checkout staging
git pull origin staging

# Fazer merge do develop
git merge develop
```

#### 2. Deploy para Staging
```bash
# Enviar para GitHub (deploy automático)
git push origin staging

# Aguardar deploy automático
# Verificar em https://quizz.sytes.net
```

#### 3. Testes em Staging
- Testar todas as funcionalidades novas
- Verificar integrações
- Testar performance
- Validar com stakeholders

### Deploy para Produção

#### 1. Preparar Produção
```bash
# Ir para main
git checkout main
git pull origin main

# Fazer merge do staging (apenas se tudo OK)
git merge staging
```

#### 2. Deploy para Produção
```bash
# Enviar para GitHub
git push origin main

# OU fazer deploy manual via GitHub Actions
# Ir em Actions → Deploy to Production → Run workflow
```

#### 3. Verificação Pós-Deploy
- Verificar se o site está funcionando
- Monitorar logs de erro
- Testar funcionalidades críticas
- Comunicar equipe sobre deploy

---

## 🚨 Situações Específicas

### Correção Urgente (Hotfix)

**Quando usar:** Bug crítico em produção que não pode esperar

#### 1. Criar Hotfix
```bash
# Sair da main (produção)
git checkout main
git pull origin main

# Criar branch de hotfix
git checkout -b hotfix/corrige-erro-login
```

#### 2. Fazer Correção
```bash
# Fazer a correção mínima necessária
# Testar localmente
git add .
git commit -m "Corrige erro de login com email especial"
```

#### 3. Deploy do Hotfix
```bash
# Aplicar em main (produção)
git checkout main
git merge hotfix/corrige-erro-login
git push origin main

# Aplicar em staging
git checkout staging
git merge hotfix/corrige-erro-login
git push origin staging

# Aplicar em develop
git checkout develop
git merge hotfix/corrige-erro-login
git push origin develop

# Deletar branch de hotfix
git branch -d hotfix/corrige-erro-login
```

### Rollback (Voltar Versão)

**Quando usar:** Deploy deu problema e precisa voltar versão anterior

#### 1. Rollback Rápido
```bash
# Ver últimos commits
git log --oneline -10

# Voltar para commit anterior
git checkout main
git reset --hard HEAD~1
git push --force origin main
```

#### 2. Rollback Seguro
```bash
# Criar commit que desfaz mudanças
git checkout main
git revert HEAD
git push origin main
```

### Sincronizar Branch Desatualizada

**Quando usar:** Sua branch está muito atrás da develop

```bash
# Na sua branch de feature
git checkout feature/minha-funcionalidade

# Buscar atualizações
git fetch origin

# Fazer rebase com develop
git rebase origin/develop

# Se houver conflitos, resolver e continuar
git add .
git rebase --continue

# Forçar push (cuidado!)
git push --force origin feature/minha-funcionalidade
```

### Resolver Conflitos de Merge

**Quando acontece:** Duas pessoas mexeram no mesmo arquivo

#### 1. Durante Merge
```bash
# Git mostra conflitos
git status

# Abrir arquivo com conflito
# Procurar por <<<<<<< HEAD
# Resolver manualmente
# Remover marcadores de conflito

# Marcar como resolvido
git add arquivo-com-conflito.php
git commit -m "Resolve conflito de merge"
```

#### 2. Prevenção
- Fazer pull frequente
- Comunicar mudanças grandes
- Usar branches pequenas e específicas

---

## 🔧 Troubleshooting

### Deploy Falhou

#### 1. Verificar Logs
```bash
# No GitHub Actions
# Ir em Actions → Ver workflow que falhou
# Clicar no job que deu erro
# Ver logs detalhados
```

#### 2. Problemas Comuns

**Erro de Dependências:**
```bash
# Limpar cache do composer
composer clear-cache
composer install --no-dev --optimize-autoloader
```

**Erro de Migração:**
```bash
# Verificar se migration está correta
php artisan migrate:status
php artisan migrate --pretend
```

**Erro de Permissão:**
```bash
# Ajustar permissões (no servidor)
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### Site Fora do Ar

#### 1. Verificação Rápida
```bash
# Verificar se é problema de código
tail -f storage/logs/laravel.log

# Verificar se é problema de servidor
curl -I https://app.trendsquiz.com.br
```

#### 2. Ações Imediatas
```bash
# Ativar modo manutenção
php artisan down --message="Manutenção em andamento"

# Fazer rollback se necessário
git reset --hard HEAD~1
git push --force origin main

# Tirar de manutenção
php artisan up
```

### Performance Lenta

#### 1. Otimizações
```bash
# Limpar todos os caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Recriar caches otimizados
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

#### 2. Verificar Banco
```bash
# Ver queries lentas
php artisan telescope:install # se usando Telescope
# ou verificar logs do MySQL
```

---

## ✅ Checklist de Deploy

### Antes do Deploy

#### Desenvolvimento
- [ ] Código testado localmente
- [ ] Testes unitários passando
- [ ] Migrations testadas
- [ ] Assets compilados (`npm run build`)
- [ ] Sem erros no console do navegador
- [ ] Funcionalidades testadas manualmente

#### Staging
- [ ] Branch `develop` atualizada
- [ ] Merge para `staging` feito
- [ ] Deploy automático concluído
- [ ] Testes em staging realizados
- [ ] Performance verificada
- [ ] Aprovação dos stakeholders

#### Produção
- [ ] Backup do banco agendado
- [ ] Horário de menor tráfego escolhido
- [ ] Equipe comunicada sobre deploy
- [ ] Plano de rollback definido

### Durante o Deploy

#### Automático
- [ ] GitHub Actions executando
- [ ] Logs do deploy monitorados
- [ ] Sem erros nos workflows

#### Manual
- [ ] Modo manutenção ativado
- [ ] Backup realizado
- [ ] Código atualizado
- [ ] Migrations executadas
- [ ] Cache otimizado
- [ ] Modo manutenção desativado

### Após o Deploy

#### Verificação Imediata
- [ ] Site carregando normalmente
- [ ] Login funcionando
- [ ] Funcionalidades críticas testadas
- [ ] Logs sem erros críticos
- [ ] Performance aceitável

#### Monitoramento
- [ ] Logs monitorados por 30 minutos
- [ ] Métricas de performance verificadas
- [ ] Feedback dos usuários coletado
- [ ] Equipe comunicada sobre sucesso

#### Documentação
- [ ] Deploy documentado
- [ ] Changelog atualizado
- [ ] Versão taggeada no Git
- [ ] Stakeholders comunicados

---

## 📚 Comandos Úteis

### Git
```bash
# Ver status das branches
git branch -a

# Ver diferenças entre branches
git diff develop..staging

# Ver histórico de commits
git log --oneline --graph

# Criar tag de versão
git tag -a v1.2.0 -m "Versão 1.2.0"
git push origin v1.2.0
```

### Laravel
```bash
# Ver status das migrations
php artisan migrate:status

# Fazer rollback de migration
php artisan migrate:rollback

# Ver rotas
php artisan route:list

# Limpar logs antigos
php artisan log:clear
```

### Servidor
```bash
# Ver espaço em disco
df -h

# Ver processos PHP
ps aux | grep php

# Ver logs do Apache/Nginx
tail -f /var/log/apache2/error.log
```

---

## 🎯 Resumo Final

Este workflow garante:

1. **Segurança**: Código sempre testado antes da produção
2. **Organização**: Cada mudança tem seu lugar e processo
3. **Automação**: Menos trabalho manual, menos erros
4. **Rastreabilidade**: Histórico completo de mudanças
5. **Recuperação**: Fácil rollback em caso de problemas

**Lembre-se:**
- Sempre testar localmente primeiro
- Usar staging para validação final
- Fazer backup antes de deploy em produção
- Monitorar após cada deploy
- Comunicar a equipe sobre mudanças importantes

**Em caso de dúvidas:**
- Consulte este documento
- Verifique os logs do GitHub Actions
- Teste em staging primeiro
- Peça ajuda antes de fazer algo arriscado em produção