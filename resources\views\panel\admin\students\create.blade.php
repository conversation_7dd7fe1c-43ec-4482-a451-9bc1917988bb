{{-- H:\quizz\resources\views\panel\admin\students\create.blade.php --}}
@extends('layouts.panel')

@section('title', isset($student) ? 'Editar Aluno' : 'Novo Aluno')
@section('page_title', isset($student) ? 'Editar Aluno' : 'Novo Aluno')

@push('styles')
    @vite(['resources/css/quill-editor.css'])
@endpush

@section('content')
    <div class="animate-fade-in">
        @if ($errors->any())
            <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <strong class="font-medium">Atenção!</strong>
                <ul class="mt-3 list-disc list-inside text-sm">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form
            action="{{ isset($student) ? route('admin.students.update', $student->id) : route('admin.students.store') }}"
            method="POST" id="courseForm" enctype="multipart/form-data">
            @csrf
            @if (isset($student))
                @method('PUT')
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div
                        class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg mb-6">
                        <div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
                            <div class="flex items-center gap-2">
                                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                                <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Dados Pessoais
                                </h1>
                            </div>
                            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                                Preencha os dados para criar um novo aluno na plataforma.
                            </p>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Nome do Aluno -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Nome Completo <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="name" name="name" 
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                    value="{{ old('name', $student->name ?? '') }}" required>
                            </div>

 


                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                                {{-- CPF --}}
                                <div class="flex flex-col">
                                    <label for="document" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        CPF:<span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="document" name="document"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('document', $student->document ?? '') }}" required>
                                </div>



                                
                                {{-- CRM --}}
                                <div class="flex flex-col">
                                    <label for="crm" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        CRM:<span class="text-red-500"></span>
                                    </label>
                                    <input type="text" id="crm" name="crm"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('crm', $student->crm ?? '') }}">
                                </div>





                                {{-- Telefone --}}
                                <div class="flex flex-col">
                                    <label for="phone" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Telefone:<span class="text-red-500">*</span>
                                    </label>
                                    <input type="phone" id="phone1" name="phone1"
                                        class="phone w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('phone', $student->phone ?? '') }}" required>
                                </div>

                                {{-- Nascimento --}}
                                <div class="flex flex-col">
                                    <label for="birth_date" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Nascimento:<span class="text-red-500"></span>
                                    </label>
                                    <input type="date" id="birth_date" name="birth_date"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ isset($student) && $student->birth_date ? \Carbon\Carbon::parse($student->birth_date)->format('Y-m-d') : '' }}">
                                </div>
                            </div>



                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {{-- Email --}}
                            <div class="flex flex-col">
                                <label for="email" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Email:<span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="email" name="email"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                    value="{{ old('email', $student->email ?? '') }}" required>
                            </div>

                            {{-- Senha --}}
                            <div class="flex flex-col relative">


                            
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Senha</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input id="password" name="password" type="text" class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg shadow-sm text-sm
                                    focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="{{ isset($student) ? 'Deixe em branco para manter a atual' : 'Digite sua senha' }}">
                                <button type="button" class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="far text-gray-400 fa-eye-slash"></i>
                                </button>
                            </div>
                        

                            </div>
                        </div>



                     
                            <!-- Configurações -->
                            <div class="flex items-center gap-4">
                                <label class="inline-flex items-center cursor-pointer relative">
                                    <input type="checkbox" name="active" value="1" class="sr-only peer"
                                        {{ old('active', isset($student) ? $student->active : false) ? 'checked' : '' }}>
                                    <div
                                        class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary">
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Aluno ativo</span>
                                </label>
                            </div>
                        </div>









            <div class="px-6 py-4 border-t border-b border-zinc-200 dark:border-zinc-700">
                   

                <div class="flex items-center gap-2">
                    <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                    <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Endereço</h1>
                </div>


             </div>




                         <div class="p-6 space-y-6">


                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {{-- CEP --}}
                                <div class="flex flex-col">
                                    <label for="cep" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        CEP:<span class="text-red-500"></span>
                                    </label>
                                    <input type="text" id="cep" name="cep"
                                        class="cep w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('cep', $student->address->cep ?? '') }}">
                                </div>

                                {{-- Cidade --}}
                                <div class="flex flex-col">
                                    <label for="city" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Cidade:<span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="city" name="city"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('city', $student->address->city ?? '') }}" required>
                                </div>


                                 {{-- Estado --}}
                                <div class="flex flex-col">
                                    <label for="state" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Estado:<span class="text-red-500">*</span>
                                    </label>
       
                                    @php
                                        $states = [
                                            'AC' => 'Acre',
                                            'AL' => 'Alagoas',
                                            'AP' => 'Amapá',
                                            'AM' => 'Amazonas',
                                            'BA' => 'Bahia',
                                            'CE' => 'Ceará',
                                            'DF' => 'Distrito Federal',
                                            'ES' => 'Espírito Santo',
                                            'GO' => 'Goiás',
                                            'MA' => 'Maranhão',
                                            'MT' => 'Mato Grosso',
                                            'MS' => 'Mato Grosso do Sul',
                                            'MG' => 'Minas Gerais',
                                            'PA' => 'Pará',
                                            'PB' => 'Paraíba',
                                            'PR' => 'Paraná',
                                            'PE' => 'Pernambuco',
                                            'PI' => 'Piauí',
                                            'RJ' => 'Rio de Janeiro',
                                            'RN' => 'Rio Grande do Norte',
                                            'RS' => 'Rio Grande do Sul',
                                            'RO' => 'Rondônia',
                                            'RR' => 'Roraima',
                                            'SC' => 'Santa Catarina',
                                            'SP' => 'São Paulo',
                                            'SE' => 'Sergipe',
                                            'TO' => 'Tocantins',
                                        ];
                                    @endphp

                                    <select name="state" id="state" class="w-full px-3 py-2 border rounded-md">
                                        <option value="">Selecione o estado</option>
                                        @foreach ($states as $uf => $nome)
                                            <option value="{{ $uf }}" {{ old('state', $student->address->state ?? '') == $uf ? 'selected' : '' }}>
                                                {{ $uf }} - {{ $nome }}
                                            </option>
                                        @endforeach
                                    </select>


                                </div>

                            </div>


                          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {{-- Bairro --}}
                                <div class="flex flex-col">
                                    <label for="neighborhood" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Bairro:<span class="text-red-500"></span>
                                    </label>
                                    <input type="text" id="neighborhood" name="neighborhood"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('neighborhood', $student->address->neighborhood ?? '') }}">
                                </div>

                                {{-- Endereço --}}
                                <div class="flex flex-col">
                                    <label for="street" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Endereço:<span class="text-red-500"></span>
                                    </label>
                                    <input type="text" id="street" name="street"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('street', $student->address->street ?? '') }}" required>
                                </div>


                               <div class="flex flex-col">
                                    <label for="number" class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Número:<span class="text-red-500"></span>
                                    </label>
                                    <input type="number" id="number" name="number"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('number', $student->address->number ?? '') }}" required>
                                </div>
                                
                            </div>



                        </div>  















                        <div
                            class="px-6 py-4 bg-zinc-50 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800">
                            <div class="flex justify-end gap-4">
                                <a href="{{ route('admin.students.index') }}"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                                    <i class="fas fa-times"></i>
                                    <span>Cancelar</span>
                                </a>
                                <button type="submit" id="submit-button"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                                    <i class="fas fa-save"></i>
                                    <span>{{ isset($student) ? 'Atualizar' : 'Salvar' }}</span>
                                </button>
                            </div>
                        </div>
                    </div>

























                    
                </div>
















                

                
                @include('panel.admin.students.partials.preview')
            </div>
        </form>
    </div>
@endsection

@section('modals')
    {{-- @include('panel.admin.media.partials.modal') --}}
@endsection