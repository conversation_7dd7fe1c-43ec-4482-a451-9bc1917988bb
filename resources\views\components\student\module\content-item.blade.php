@props(['content', 'isActive' => false, 'isCompleted' => false])

@php
    $iconMap = [
        'video' => 'fas fa-play',
        'text' => 'fas fa-file-text',
        'pdf' => 'fas fa-file-pdf',
        'link' => 'fas fa-external-link-alt',
        'quiz' => 'fas fa-question-circle'
    ];
    
    $icon = $iconMap[$content->content_type] ?? 'fas fa-file';
@endphp

<div class="flex items-center p-3 rounded-lg cursor-pointer transition-colors {{ $isActive ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50' }}">
    <!-- Status Icon -->
    <div class="flex-shrink-0 mr-3">
        @if($isCompleted)
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-check text-white text-xs"></i>
            </div>
        @else
            <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                <i class="{{ $icon }} text-gray-600 text-xs"></i>
            </div>
        @endif
    </div>
    
    <!-- Content Info -->
    <div class="flex-1 min-w-0">
        <h4 class="text-sm font-medium text-gray-900 truncate {{ $isActive ? 'text-blue-700' : '' }}">
            {{ $content->title }}
        </h4>
        <div class="flex items-center mt-1 text-xs text-gray-500">
            <span class="capitalize">{{ $content->content_type }}</span>
            @if($content->duration)
                <span class="mx-1">•</span>
                <span>{{ $content->duration }} min</span>
            @endif
        </div>
    </div>
    
    <!-- Completion Status -->
    @if($isActive)
        <div class="flex-shrink-0 ml-2">
            <i class="fas fa-chevron-right text-blue-500 text-sm"></i>
        </div>
    @endif
</div>
