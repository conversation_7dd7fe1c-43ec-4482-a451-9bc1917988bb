<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_enrollments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy

            $table->unsignedBigInteger('user_id')->nullable(); // Quem processou a matrícula
            $table->foreignId('student_id')->constrained('plg_students')->onDelete('cascade');
            $table->unsignedBigInteger('module_id'); // Principal: matrícula em módulos

            $table->decimal('module_price_at_time', 10, 2)->nullable(); // Preço pago na época (histórico)
            $table->enum('status', ['active', 'expired', 'canceled'])->default('active');

            // Sistema de aprovação de matrículas
            $table->enum('enrollment_method', ['self_registration', 'admin_created'])->default('admin_created');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved');
            $table->timestamp('requested_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->text('rejection_reason')->nullable();

            $table->timestamp('enrolled_at')->useCurrent(); // Data da matrícula
            $table->timestamp('expires_at')->nullable(); // Data de expiração

            $table->timestamps();
            $table->softDeletes();

            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');
            $table->foreign('module_id')->references('id')->on('plg_modules')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('sys_users')->onDelete('set null');

            // Índices únicos
            $table->unique(['student_id', 'module_id']); // Principal: estudante + módulo

            // Índices para performance
            $table->index(['company_id', 'student_id']); // Multi-tenancy + estudante
            $table->index(['student_id', 'status']);
            $table->index(['module_id', 'status']); // Módulo + status
            $table->index(['enrolled_at']);
            $table->index(['expires_at']);
            $table->index(['company_id', 'status']); // Multi-tenancy + status
            $table->index(['approval_status']);
            $table->index(['enrollment_method']);
            $table->index(['approved_by']);
            $table->index(['company_id', 'approval_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_enrollments');
    }
};
