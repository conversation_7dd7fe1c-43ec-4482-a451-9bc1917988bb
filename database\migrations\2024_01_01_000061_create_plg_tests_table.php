<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_tests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id')->nullable(); // Quem criou o teste
            $table->foreignId('module_id')->constrained('plg_modules')->onDelete('cascade');
            $table->enum('test_type', ['quiz', 'challenge', 'exam'])->default('quiz'); // Tipo do teste

            // Informações básicas do teste
            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description')->nullable();
            $table->json('questions')->nullable(); // IDs das questões em formato JSON

            // Configurações de tempo
            $table->integer('time_limit_minutes')->nullable(); // Tempo limite em minutos

            // Configurações de tentativas
            $table->integer('max_attempts')->default(1);
            $table->boolean('allow_unlimited_attempts')->default(false);
            $table->integer('cooldown_hours')->default(24); // Tempo de espera entre tentativas

            // Configurações de pontuação
            $table->decimal('passing_score', 5, 2)->default(70.00); // Nota mínima para aprovação

            // Configurações de inatividade
            $table->integer('auto_abandon_inactive_minutes')->default(30);
            $table->integer('show_activity_warning_minutes')->default(25);

            // Configurações de comportamento
            $table->boolean('allow_resume')->default(true);
            $table->boolean('randomize_questions')->default(false);
            $table->boolean('randomize_alternatives')->default(false);
            $table->boolean('show_results_immediately')->default(true);
            $table->boolean('allow_review')->default(true);
            $table->boolean('active')->default(true);

            $table->timestamps();
            $table->softDeletes();

            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');

            // Índices para performance
            $table->index(['company_id', 'module_id']); // Multi-tenancy + módulo
            $table->index(['module_id', 'active']);
            $table->index(['active']);
            $table->index(['company_id', 'active']); // Multi-tenancy + ativo
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_tests');
    }
};
