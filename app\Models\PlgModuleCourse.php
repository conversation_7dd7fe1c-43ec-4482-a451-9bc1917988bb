<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlgModuleCourse extends Model
{
    use HasFactory;

    protected $table = 'plg_modules_courses';

    protected $fillable = [
        'module_id',
        'course_id',
        'order',
    ];

    /**
     * Relacionamento com o módulo.
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(PlgModule::class, 'module_id');
    }

    /**
     * Relacionamento com o curso.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(PlgCourse::class, 'course_id');
    }
} 