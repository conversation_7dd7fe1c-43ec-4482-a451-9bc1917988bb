@props(['module', 'index', 'course'])

@php
// Obter status de acesso do estudante logado
$student = auth('students')->user();
$accessStatus = $student->getModuleAccessStatus($module);

// Determinar estado do módulo baseado no sistema de matrículas
$isFree = $module->is_free;
$hasAccess = $accessStatus['has_access'];
$isLocked = !$hasAccess && !$isFree; // Módulos gratuitos nunca ficam bloqueados
$isCompleted = false; // TODO: Implementar lógica de conclusão
$isInProgress = false; // TODO: Implementar lógica de progresso

// Status específicos de matrícula
$isExpired = $accessStatus['status'] === 'expired';
$isPendingApproval = $accessStatus['status'] === 'pending_approval';
$isNotEnrolled = $accessStatus['status'] === 'not_enrolled';
$isRejected = $accessStatus['status'] === 'rejected';
$isFreeNotEnrolled = $isFree && !$student->getModuleEnrollment($module);

// Separar quizzes e testes de certificação
$quizzes = $module->tests->where('test_type', 'quiz')->where('active', true);
$certification = $module->tests->where('test_type', 'exam')->where('active', true)->first();

// Atributos para filtros
$dataAttributes = [];
if ($isFree) $dataAttributes[] = 'data-module-free';
if ($hasAccess) $dataAttributes[] = 'data-module-accessible';
if ($isLocked) $dataAttributes[] = 'data-module-locked';
if ($isCompleted) $dataAttributes[] = 'data-module-completed';
if ($isInProgress) $dataAttributes[] = 'data-module-in-progress';
@endphp

<!-- Card do Módulo -->
<div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 mx-auto overflow-hidden"
     data-module-item
     {!! implode(' ', $dataAttributes) !!}>
    <div class="flex flex-col lg:grid lg:grid-cols-12">

        <!-- Capa do Módulo -->
        <div class="relative lg:col-span-4 h-64 lg:h-full shrink-0">
            @if($module->hasMedia('thumbnail'))
                @php
                    $media = $module->getMedia('thumbnail')->first();
                    $thumbnailUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=400&fit=crop&fm=webp';
                @endphp
                <img src="{{ $thumbnailUrl }}" alt="{{ $module->title }}" class="absolute inset-0 w-full h-full object-cover lg:rounded-l-2xl">
            @else
                <img src="https://placehold.co/400x500/dc2626/ffffff?text={{ urlencode($module->title) }}"
                     alt="{{ $module->title }}"
                     class="absolute inset-0 w-full h-full object-cover lg:rounded-l-2xl">
            @endif

            <!-- Badge de Status -->
            @if($isCompleted)
                <span class="absolute top-4 left-4 bg-green-600 text-white text-xs font-semibold px-2 py-1 rounded">CONCLUÍDO</span>
            @elseif($isInProgress)
                <span class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded">EM ANDAMENTO</span>
            @elseif($hasAccess)
                <span class="absolute top-4 left-4 bg-green-600 text-white text-xs font-semibold px-2 py-1 rounded">LIBERADO</span>
            @elseif($isExpired)
                <span class="absolute top-4 left-4 bg-orange-600 text-white text-xs font-semibold px-2 py-1 rounded">EXPIRADO</span>
            @elseif($isPendingApproval)
                <span class="absolute top-4 left-4 bg-yellow-600 text-white text-xs font-semibold px-2 py-1 rounded">PENDENTE</span>
            @elseif($isRejected)
                <span class="absolute top-4 left-4 bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded">REJEITADO</span>
            @elseif($isNotEnrolled && !$isFree)
                <span class="absolute top-4 left-4 bg-gray-600 text-white text-xs font-semibold px-2 py-1 rounded flex items-center gap-1">
                    <i class="fas fa-lock text-xs"></i>
                </span>
            @endif
        </div>

        <!-- Conteúdo do Módulo -->
        <div class="lg:col-span-8 p-4 lg:py-4 lg:px-4 space-y-4 flex-1">
            <!-- Cabeçalho -->
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-3">
                <div class="inline-flex items-center gap-2 text-md md:text-lg font-bold dark:text-white">
                    @if($isCompleted)
                        <svg aria-hidden="true" class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
                        </svg>
                    @elseif($isInProgress)
                        <svg aria-hidden="true" class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6l4 2"></path>
                            <circle cx="12" cy="12" r="10"></circle>
                        </svg>
                    @elseif($isLocked)
                        <svg aria-hidden="true" class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    @else
                        <svg aria-hidden="true" class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    @endif
                    <span data-module-title>{{ $module->title }}</span>
                </div>

                @if($isCompleted)
                    <span class="inline-flex items-center bg-blue-100 text-blue-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Concluído</span>
                @elseif($isInProgress)
                    <span class="inline-flex items-center bg-blue-100 text-blue-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Em Andamento</span>

                @elseif($hasAccess)
                    <span class="inline-flex items-center bg-green-100 text-green-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Acesso Liberado</span>
                @elseif($isExpired)
                    <span class="inline-flex items-center bg-orange-100 text-orange-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Matrícula Expirada</span>
                @elseif($isPendingApproval)
                    <span class="inline-flex items-center bg-yellow-100 text-yellow-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Aguardando Aprovação</span>
                @elseif($isRejected)
                    <span class="inline-flex items-center bg-red-100 text-red-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Matrícula Rejeitada</span>
                @elseif($isNotEnrolled)
                    @if($module->price > 0)
                        <span class="inline-flex items-center bg-green-100 text-green-700 text-xs font-semibold px-3 py-1 rounded-full self-start">
                            R$ {{ number_format($module->getCurrentPrice(), 2, ',', '.') }}
                        </span>
                    @else
                        <span class="inline-flex items-center bg-gray-100 text-gray-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Não Matriculado</span>
                    @endif
                @else
                    <span class="inline-flex items-center bg-gray-100 text-gray-700 text-xs font-semibold px-3 py-1 rounded-full self-start">Bloqueado</span>
                @endif
            </div>

            <!-- Descrição -->
            @if($module->description)
                <div class="text-sm text-gray-600 dark:text-gray-400" data-module-description>
                    {!! $module->description !!}
                </div>
            @endif

            <!-- Meta informações -->
            <div class="flex flex-wrap items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                @if($module->contents->count() > 0)
                    <span class="inline-flex items-center gap-1">
                        <i class="fas fa-file text-lg mt-1"></i>
                        {{ $module->contents->count() }} {{ $module->contents->count() === 1 ? 'material' : 'materiais' }}
                    </span>
                @endif

                @if($quizzes->count() > 0 || $certification)
                    <span class="inline-flex items-center gap-1">
                        <i class="fas fa-question-circle text-lg mt-1"></i>
                        {{ $quizzes->count() + ($certification ? 1 : 0) }} {{ ($quizzes->count() + ($certification ? 1 : 0)) === 1 ? 'teste' : 'testes' }}
                    </span>
                @endif

                @if($module->estimated_duration)
                    <span class="inline-flex items-center gap-1">
                        <i class="fas fa-clock text-lg mt-1"></i>
                        {{ $module->estimated_duration }} min
                    </span>
                @endif
            </div>

            <!-- Testes & Quizzes -->
            @if($quizzes->count() > 0 || $certification)
                <section class="space-y-4">
                    {{-- <div class="flex items-center justify-between">
                        <h3 class="font-semibold">Testes e Quizzes</h3>
                    </div> --}}

                    <!-- Grid 2 itens -->
                    <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-5">
                        <!-- Quizzes -->
                        @foreach($quizzes as $quiz)
                            <x-student.test.test-card
                                :test="$quiz"
                                :course="$course"
                                :module="$module"
                                :hasAccess="$hasAccess" />
                        @endforeach

                        <!-- Teste Final -->
                        @if($certification)
                            <x-student.test.test-card
                                :test="$certification"
                                :course="$course"
                                :module="$module"
                                :hasAccess="$hasAccess" />
                        @endif
                    </div>
                </section>
            @endif

            <!-- Botão de Ação -->
            @if($isFreeNotEnrolled)
                <div class="pt-2">
                    <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                       class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-black text-white font-semibold hover:bg-black transition-colors">
                        <i class="fas fa-play text-white"></i>
                        Iniciar Módulo Gratuito
                    </a>
                </div>
            @elseif($isNotEnrolled && !$isFree)
                <div class="pt-2">
                    <button onclick="openContactModal('{{ addslashes($module->title) }}', '{{ number_format($module->getCurrentPrice(), 2, ',', '.') }}')"
                            class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-green-600 text-white font-semibold hover:bg-green-700 transition-colors">
                        <i class="fas fa-credit-card text-white"></i>
                        Solicitar Matrícula
                    </button>
                </div>
            @elseif($isExpired)
                <div class="pt-2">
                    <button onclick="openContactModal('{{ addslashes($module->title) }}', '{{ number_format($module->getCurrentPrice(), 2, ',', '.') }}')"
                            class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-orange-600 text-white font-semibold hover:bg-orange-700 transition-colors">
                        <i class="fas fa-refresh text-white"></i>
                        Renovar Matrícula
                    </button>
                </div>
            @elseif($hasAccess || $isFree)
                <div class="pt-2">
                    <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                       class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 transition-colors">
                        <i class="fas fa-book-open text-white"></i>
                        Acessar Módulo
                    </a>
                </div>
            @else
                <div class="pt-2">
                    @if(!$isFree)
                        <button onclick="openContactModal('{{ addslashes($module->title) }}', '{{ number_format($module->getCurrentPrice(), 2, ',', '.') }}')"
                                class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-gray-600 text-white font-semibold hover:bg-gray-700 transition-colors">
                            <i class="fas fa-envelope text-white"></i>
                            Entrar em Contato
                        </button>
                    @else
                        <button disabled
                                class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-gray-400 text-white font-semibold cursor-not-allowed">
                            <i class="fas fa-lock text-white"></i>
                            Acesso Restrito
                        </button>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>