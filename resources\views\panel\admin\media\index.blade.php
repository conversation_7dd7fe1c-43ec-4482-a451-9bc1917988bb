@extends('layouts.panel')

@section('title', 'Gerenciador de Mídia')
@section('page_title', 'Gerenciador de Mídia')

@section('content')
    <div class="animate-fade-in">
        <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-zinc-200 dark:border-zinc-800">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
                        Gerenciador de Mídia
                    </h2>
                </div>
            </div>

            <div>
                @include('panel.admin.media.partials.modal_content', 
                    [
                        'isStandalone' => true,
                        'multiple' => true
                    ])
            </div>
        </div>
    </div>

    <!-- Overlay de Drop Zone (usado pelo MediaManager) -->
    <div id="dropzoneOverlay" class="media-dropzone-overlay fixed inset-0 bg-trends-secondary/80 backdrop-blur-sm z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity">
        <div class="text-center">
            <div class="text-8xl text-white mb-4">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <h3 class="text-2xl font-medium text-white">Solte para fazer upload</h3>
            <p class="text-lg text-gray-200 mt-2">Os arquivos serão enviados automaticamente</p>
        </div>
    </div>
@endsection