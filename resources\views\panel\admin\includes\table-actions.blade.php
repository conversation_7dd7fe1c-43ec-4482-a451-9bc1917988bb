@php
    $actions = $actions ?? [];
    $defaultClasses = [
        'edit' => [
            'button' => 'inline-flex items-center justify-center w-8 h-8 text-blue-500 bg-blue-50 dark:bg-blue-500/10 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-500/20 transition-colors',
            'icon' => 'fas fa-edit'
        ],
        'show' => [
            'button' => 'inline-flex items-center justify-center w-8 h-8 text-green-500 bg-green-50 dark:bg-green-500/10 rounded-lg hover:bg-green-100 dark:hover:bg-green-500/20 transition-colors',
            'icon' => 'fas fa-eye'
        ],
        'duplicate' => [
            'button' => 'inline-flex items-center justify-center w-8 h-8 text-purple-500 bg-purple-50 dark:bg-purple-500/10 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-500/20 transition-colors',
            'icon' => 'fas fa-copy'
        ],
        'delete' => [
            'button' => 'inline-flex items-center justify-center w-8 h-8 text-red-500 bg-red-50 dark:bg-red-500/10 rounded-lg hover:bg-red-100 dark:hover:bg-red-500/20 transition-colors',
            'icon' => 'fas fa-trash'
        ],
        'approve' => [
            'button' => 'inline-flex items-center justify-center w-8 h-8 text-green-500 bg-green-50 dark:bg-green-500/10 rounded-lg hover:bg-green-100 dark:hover:bg-green-500/20 transition-colors',
            'icon' => 'fas fa-check'
        ],
        'reject' => [
            'button' => 'inline-flex items-center justify-center w-8 h-8 text-red-500 bg-red-50 dark:bg-red-500/10 rounded-lg hover:bg-red-100 dark:hover:bg-red-500/20 transition-colors',
            'icon' => 'fas fa-times'
        ]
    ];
@endphp

<div class="flex items-center justify-end gap-2">
    @foreach($actions as $action)
        @if($action['type'] === 'delete')
            <form action="{{ $action['route'] }}" 
                method="POST" 
                class="inline-block" 
                onsubmit="return confirm('{{ $action['confirm_message'] ?? 'Tem certeza que deseja excluir este item?' }}')">
                @csrf
                @method('DELETE')
                <button type="submit" 
                    class="{{ $action['class'] ?? $defaultClasses['delete']['button'] }}"
                    title="{{ $action['title'] ?? 'Excluir' }}">
                    <i class="{{ $action['icon'] ?? $defaultClasses['delete']['icon'] }}"></i>
                </button>
            </form>
        @elseif($action['type'] === 'duplicate')
            <form action="{{ $action['route'] }}"
                method="POST"
                class="inline-block">
                @csrf
                <button type="submit"
                    class="{{ $action['class'] ?? $defaultClasses['duplicate']['button'] }}"
                    title="{{ $action['title'] ?? 'Duplicar' }}">
                    <i class="{{ $action['icon'] ?? $defaultClasses['duplicate']['icon'] }}"></i>
                </button>
            </form>
        @elseif($action['type'] === 'approve')
            <form action="{{ $action['route'] }}"
                method="POST"
                class="inline-block"
                onsubmit="return confirm('{{ $action['confirm_message'] ?? 'Tem certeza que deseja aprovar esta matrícula?' }}')">
                @csrf
                <button type="submit"
                    class="{{ $action['class'] ?? $defaultClasses['approve']['button'] }}"
                    title="{{ $action['title'] ?? 'Aprovar' }}">
                    <i class="{{ $action['icon'] ?? $defaultClasses['approve']['icon'] }}"></i>
                </button>
            </form>
        @elseif($action['type'] === 'reject')
            <form action="{{ $action['route'] }}"
                method="POST"
                class="inline-block"
                onsubmit="return confirm('{{ $action['confirm_message'] ?? 'Tem certeza que deseja rejeitar esta matrícula?' }}')">
                @csrf
                <button type="submit"
                    class="{{ $action['class'] ?? $defaultClasses['reject']['button'] }}"
                    title="{{ $action['title'] ?? 'Rejeitar' }}">
                    <i class="{{ $action['icon'] ?? $defaultClasses['reject']['icon'] }}"></i>
                </button>
            </form>
        @else
            <a href="{{ $action['route'] }}" 
                class="{{ $action['class'] ?? $defaultClasses[$action['type']]['button'] }}"
                title="{{ $action['title'] ?? ucfirst($action['type']) }}">
                <i class="{{ $action['icon'] ?? $defaultClasses[$action['type']]['icon'] }}"></i>
            </a>
        @endif
    @endforeach
</div> 