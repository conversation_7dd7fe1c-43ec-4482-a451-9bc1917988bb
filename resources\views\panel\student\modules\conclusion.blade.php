<x-layouts.student title="<PERSON><PERSON><PERSON><PERSON>do - {{ $module->title }}">
<!-- Breadcrumb -->
<x-student.breadcrumb
    :course="$course"
    :module="$module"
    :customItems="[['url' => null, 'text' => '<PERSON><PERSON><PERSON><PERSON>ído', 'icon' => 'fas fa-check-circle']]" />

<div class="min-h-screen">
    <!-- Main Content Area -->
    <div class="container mx-auto">
        <div class="grid grid-cols-12 gap-6">
            <!-- Left Sidebar - Content Navigation (4 columns) -->
            <div class="col-span-4">
                <x-student.module.content-sidebar
                    :module="$module"
                    :course="$course"
                    :progress="$progress ?? collect()"
                    :progressPercentage="$progressPercentage ?? 0" />
            </div>

            <!-- Main Content Area (8 columns) -->
            <div class="col-span-8">

                <!-- <PERSON><PERSON> <PERSON> -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div class="p-8 text-center">
                        <!-- Ícone de Sucesso -->
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>

                        <!-- Título -->
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-300 mb-4">
                            🎉 Parabéns!
                        </h1>

                        <!-- Mensagem -->
                        <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
                            Você concluiu com sucesso todos os materiais do módulo: <strong class="text-gray-900 dark:text-gray-300 mt-6">{{ $module->title }}</strong>
                        </p>

                        <!-- Estatísticas -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">{{ $module->contents->count() }}</div>
                                    <div class="text-sm text-gray-400">Materiais Concluídos</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">100%</div>
                                    <div class="text-sm text-gray-400">Progresso</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">{{ $course->title }}</div>
                                    <div class="text-sm text-gray-400">Especialidade</div>
                                </div>
                            </div>
                        </div>

                        <!-- Próximos Passos -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-300 mb-4">Próximos Passos</h3>

                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <!-- Botão para Quizzes e Testes -->
                                @if($module->tests->where('active', true)->count() > 0)
                                    <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                                       class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                                        <i class="fas fa-clipboard-list mr-2"></i>
                                        Fazer Quizzes e Testes
                                    </a>
                                @endif

                                <!-- Botão para Voltar ao Módulo -->
                                <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Voltar ao Módulo
                                </a>

                                <!-- Botão para Voltar para especialidade -->
                                <a href="{{ route('student.course.show', $course->slug) }}"
                                   class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-home mr-2"></i>
                                    Voltar para Especialidade
                                </a>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>


</x-layouts.student>
