<?php
namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlgModule;
use App\Models\PlgModuleContent;
use App\Models\PlgCourse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Plank\Mediable\Media;
use Yajra\DataTables\Facades\DataTables;

class PlgModuleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('panel.admin.modules.index', [
            'columns' => $this->getColumns(),
            'id' => 'modules-table',
            'ajaxUrl' => route('admin.modules.data'),
            'filters' => $this->getFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    /**
     * Get data for DataTables.
     */
    public function getData()
    {
        $user = Auth::user();

        $query = PlgModule::with(['course'])
            ->select('plg_modules.*')->orderBy('id', 'desc');

        // Filtro simples: professores veem apenas seus módulos
        if ($user->role === 'teacher') {
            $query->where('user_id', $user->id);
        }
        // super_admin vê tudo
        
        return DataTables::of($query)
            ->addColumn('actions', function($module) {
                return view('panel.admin.includes.table-actions', [
                    'actions' => [
                        [
                            'type' => 'edit',
                            'route' => route('admin.modules.edit', $module->id),
                            'title' => 'Editar módulo'
                        ],
                        [
                            'type' => 'show',
                            'route' => route('admin.modules.show', $module->id),
                            'title' => 'Visualizar módulo'
                        ],
                        [
                            'type' => 'duplicate',
                            'route' => route('admin.modules.duplicate', $module->id),
                            'title' => 'Duplicar módulo'
                        ],
                        [
                            'type' => 'delete',
                            'route' => route('admin.modules.destroy', $module->id),
                            'title' => 'Excluir módulo',
                            'confirm_message' => 'Tem certeza que deseja excluir este módulo?'
                        ]
                    ]
                ])->render();
            })
            ->editColumn('title', function ($course) {
            $media = $course->getMedia('thumbnail')->first();
            return [
                'text' => $course->title,
                'thumbnail' => $media
                    ? route('media.serve', ['path' => $media->getDiskPath()]).'?w=250&fit=crop&fm=webp'
                    : null,
            ];
            })
            /*->editColumn('course.title', function($module) {
                return $module->course 
                    ? '<div class="text-zinc-500 dark:text-zinc-400">' . e($module->course->title) . '</div>'
                    : '<div class="text-red-500">Sem curso</div>';
            })*/
            ->editColumn('active', function($module) {
                return view('panel.admin.modules.partials.status', compact('module'))->render();
            })
            ->addColumn('content_count', function($module) {
                return '<div class="text-center">' . $module->contents()->count() . '</div>';
            })
            ->rawColumns(['actions', 'title', 'course.title', 'active', 'content_count'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('panel.admin.modules.create', $this->formData());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {   

        if ($request->filled('price')) {
            $rawPrice = preg_replace('/[^\d,]/', '', $request->input('price')); // remove tudo exceto dígitos e vírgula
            $normalizedPrice = str_replace(',', '.', $rawPrice); // converte vírgula para ponto
            $request->merge(['price' => $normalizedPrice]);
        }


        $validated = $this->validateRequest($request);

        $user = Auth::user();

        $validated['active'] = true;
        $validated['order'] = $validated['order'] ?? 0;
        $validated['company_id'] = $user->company_id;
        $validated['user_id'] = $user->id; // Auto-preenchimento do user_id


        $module = PlgModule::create($validated);

        // Associar a mídia ao módulo se houver thumbnail
        if ($request->filled('thumbnail')) {
            $media = Media::find($request->thumbnail);
            if ($media) {
                $module->attachMedia($media, 'thumbnail');
            }
        }

        return redirect()->route('admin.modules.index')
            ->with('success', 'Módulo criado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $module = PlgModule::with(['course', 'contents' => function($query) {
            $query->orderBy('order');
        }])->findOrFail($id);
        
        return view('panel.admin.modules.show', compact('module'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $module = PlgModule::with(['tests', 'media'])->findOrFail($id);



        $thumbnailUrl = '';
        if ($module->hasMedia('thumbnail')) {
            $media = $module->firstMedia('thumbnail');
            $thumbnailUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=250&fm=webp';
        }



        return view('panel.admin.modules.edit', 
            array_merge([
                'thumbnailUrl' => $thumbnailUrl,
                'module' => $module,
                'columns' => $this->getContentsColumns(),
                'id' => 'contents-table',
                'ajaxUrl' => route('admin.modules.contents.data', $module->id),
                'filters' => $this->getContentsFilters(),
                'clearFiltersBtn' => true
            ], $this->formData())
        );
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        

         if ($request->filled('price')) {
            $rawPrice = preg_replace('/[^\d,]/', '', $request->input('price')); // remove tudo exceto dígitos e vírgula
            $normalizedPrice = str_replace(',', '.', $rawPrice); // converte vírgula para ponto
            $request->merge(['price' => $normalizedPrice]);
        }


        $module = PlgModule::findOrFail($id);


        $validated = $this->validateRequest($request);

        $validated += [
            'active' => $request->has('active'),
            'order' => Str::slug($validated['title']),
            'is_free' => $request->has('is_free'),
        ];

        $module->update($validated);

        // Atualizar a mídia do módulo
        if ($request->filled('thumbnail')) {
            // Remover todas as mídias da coleção 'thumbnail'
            $module->detachMediaTags('thumbnail');
            
            // Associar nova mídia
            $media = Media::find($request->thumbnail);
            if ($media) {
                $module->attachMedia($media, 'thumbnail');
            }
        } else {
            // Se o thumbnail foi removido, remover todas as mídias da coleção
            $module->detachMediaTags('thumbnail');
        }

        return redirect()->route('admin.modules.index', $module->id)
            ->with('success', 'Módulo atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $module = PlgModule::findOrFail($id);
        $module->delete();

        return response()->json(['success' => true]);
    }
    
    /**
     * Mostra os módulos de um curso específico.
     */
    public function byCourse($courseId)
    {
        $course = PlgCourse::with(['modules' => function($query) {
            $query->orderBy('order');
        }])->findOrFail($courseId);
        
        if (request()->ajax()) {
            return response()->json([
                'modules' => $course->modules->map(function($module) {
                    return [
                        'id' => $module->id,
                        'title' => $module->title
                    ];
                })
            ]);
        }
        
        return view('panel.admin.modules.by_course', compact('course'));
    }
    
    /**
     * Reordena os módulos.
     */
    public function reorder(Request $request)
    {
        $modules = $request->input('modules', []);
        
        if (count($modules) > 0) {
            foreach ($modules as $item) {
                $module = PlgModule::findOrFail($item['id']);
                $module->update(['order' => $item['order']]);
            }
            
            return response()->json(['success' => true]);
        }
        
        return response()->json(['success' => false, 'message' => 'Nenhum módulo para reordenar'], 400);
    }

    public function duplicate($id)
    {
        $module = PlgModule::findOrFail($id);
        
        // Criar uma cópia do módulo
        $newModule = $module->replicate();
        
        // Atualizar campos únicos
        $newModule->title = $module->title . ' (Cópia)';
        $newModule->active = false; // Sempre começa como inativo
        
        $newModule->save();

        // Duplicar os conteúdos do módulo
        foreach ($module->contents as $content) {
            $newContent = $content->replicate();
            $newContent->module_id = $newModule->id;
            $newContent->save();
        }

        return redirect()->route('admin.modules.edit', $newModule->id)
            ->with('success', 'Módulo duplicado com sucesso! Você pode editar os detalhes agora.');
    }

    private function getColumns()
    {
        return [
            [
                'data' => 'id', 
                'name' => 'plg_modules.id', 
                'label' => 'ID'
            ],
            [
                'data' => 'title', 
                'name' => 'plg_modules.title', 
                'label' => 'Título'
            ],
            [
                'data' => 'course.title', 
                'name' => 'course.title', 
                'label' => 'Curso'
            ],
            [
                'data' => 'active', 
                'name' => 'plg_modules.active', 
                'label' => 'Status'
            ],
            [
                'data' => 'content_count', 
                'name' => 'content_count', 
                'label' => 'Conteúdos',
                'orderable' => false,
                'searchable' => false
            ],
            [
                'data' => 'actions', 
                'name' => 'actions', 
                'label' => 'Ações',
                'orderable' => false,
                'searchable' => false
            ],
        ];
    }

    private function getFilters()
    {
        $courses = PlgCourse::orderBy('title')
            ->get()
            ->map(fn($c) => ['value' => $c->title, 'label' => $c->title]);

        return [
            [
                'label' => 'Curso', 
                'column' => 'course.title', 
                'options' => $courses
            ],
            [
                'label' => 'Status', 
                'column' => 'active', 
                'options' => [
                    ['value' => '1', 'label' => 'Ativo'],
                    ['value' => '0', 'label' => 'Inativo'],
                ]
            ],
        ];
    }

    private function formData()
    {
        return [
            'courses' => PlgCourse::orderBy('title')->get(),
        ];
    }

    private function validateRequest(Request $request)
    {
        return $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'nullable|integer',
            'price' => 'nullable|numeric|min:0',
            'course_id' => 'required|exists:plg_courses,id',
            'thumbnail' => 'nullable|exists:media,id',
        ]);
    }

    private function getContentsColumns()
    {
        return [
            [
                'data' => 'order',
                'name' => 'plg_modules_contents.order',
                'label' => 'Ordem'
            ],
            [
                'data' => 'title',
                'name' => 'plg_modules_contents.title',
                'label' => 'Título'
            ],
            [
                'data' => 'content_type',
                'name' => 'plg_modules_contents.content_type',
                'label' => 'Tipo'
            ],
            [
                'data' => 'duration',
                'name' => 'plg_modules_contents.duration',
                'label' => 'Duração'
            ],
            [
                'data' => 'active',
                'name' => 'plg_modules_contents.active',
                'label' => 'Status'
            ],
            [
                'data' => 'actions',
                'name' => 'actions',
                'label' => 'Ações',
                'orderable' => false,
                'searchable' => false
            ],
        ];
    }

    private function getContentsFilters()
    {
        return [
            [
                'label' => 'Tipo',
                'column' => 'content_type',
                'options' => [
                    ['value' => 'text', 'label' => 'Texto'],
                    ['value' => 'video', 'label' => 'Vídeo'],
                    ['value' => 'quiz', 'label' => 'Quiz'],
                    ['value' => 'file', 'label' => 'Arquivo'],
                    ['value' => 'link', 'label' => 'Link'],
                ]
            ],
            [
                'label' => 'Status',
                'column' => 'active',
                'options' => [
                    ['value' => '1', 'label' => 'Ativo'],
                    ['value' => '0', 'label' => 'Inativo'],
                ]
            ],
        ];
    }
} 