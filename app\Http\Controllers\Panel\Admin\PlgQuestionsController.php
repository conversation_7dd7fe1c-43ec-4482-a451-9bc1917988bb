<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Models\PlgQuestion;
use Illuminate\Http\Request;
use App\Models\PlgCategories;
use App\Models\PlgQuestionAnswer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Str;

class PlgQuestionsController extends Controller
{
    // ====================================================
    //                  MÉTODOS PRINCIPAIS
    // ====================================================
    
    /**
     * Lista todas as questões com DataTables
     */
    public function index()
    {
        return view('panel.admin.questions.index', [
            'columns' => $this->getDataTableColumns(),
            'id' => 'questions-table',
            'ajaxUrl' => route('admin.questions.data'),
            'filters' => $this->getDataTableFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    /**
     * Fornece dados para o DataTables
     */
    public function getData()
    {
        $user = Auth::user();

        $query = PlgQuestion::with('categories')->select('plg_questions.*')->orderBy('id', 'desc');

        // Filtro simples: professores veem apenas suas questões
        if ($user->role === 'teacher') {
            $query->where('user_id', $user->id);
        }
        // super_admin vê tudo
        
        return DataTables::of($query)
            
            //->editColumn('question', fn($question) => $this->formatQuestionColumn($question))

            ->editColumn('question', function ($question) {
            $media = $question->getMedia('question_image')->first();


            return [
                'text' => Str::limit(strip_tags($question->question), 75),
                'thumbnail' => $media
                    ? route('media.serve', ['path' => $media->getDiskPath()]) . '?w=250&h=150&fit=crop&fm=webp'
                    : null,
            ];
            })

            ->addColumn('categories', fn($question) => $question->categories->pluck('title')->implode(', '))

            ->editColumn('free', fn($question) => view('panel.admin.questions.partials.free', compact('question'))->render())
            ->editColumn('imported', fn($question) => view('panel.admin.questions.partials.imported', compact('question'))->render())
            ->addColumn('actions', fn($question) => $this->getActionsColumn($question))
            ->filterColumn('categories', fn($query, $keyword) => $query->whereHas('categories', fn($q) => $q->where('title', 'like', "%{$keyword}%")))
            ->rawColumns(['actions', 'free', 'imported', 'question'])
            ->make(true);
    }

    /**
     * Exibe formulário de criação
     */
    public function create()
    {
        $categories = PlgCategories::orderBy('title')->get();
        return view('panel.admin.questions.create', compact('categories'));
    }
    
    /**
     * Armazena uma nova questão
     */
    public function store(Request $request)
    {

        $validated = $request->validate($this->getValidationRules($request->question_type));
        
        DB::beginTransaction();
        
        try {
            $question = $this->createQuestion($validated);
            $this->syncCategories($question, $validated['category_id']);
            
            if (in_array($validated['question_type'], ['single', 'multiple'])) {
                $this->saveAnswers($question, $validated);
            }
            
            $this->handleMediaAttachment($question, $validated);
            
            DB::commit();

            return redirect()
                ->route('admin.questions.index')
                ->with('success', 'Questão criada com sucesso!');

        } catch (\Exception $e) {
            DB::rollBack();

                dd([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'input' => $request->all()
        ]);


            return back()
                ->with('error', 'Erro ao criar questão: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Exibe uma questão específica
     */
    public function show($id)
    {
        $question = PlgQuestion::with(['answers', 'categories'])->findOrFail($id);
        return view('panel.admin.questions.show', compact('question'));
    }
    
    /**
     * Exibe formulário de edição
     */
    public function edit($id)
    {
        $question = PlgQuestion::with(['answers', 'categories'])->findOrFail($id);
        $categories = PlgCategories::orderBy('title')->get();
        $formData = $this->prepareFormData($question);
        
        return view('panel.admin.questions.create', array_merge(
            compact('question', 'categories'),
            $formData
        ));
    }
    
    /**
     * Atualiza uma questão existente
     */
    public function update(Request $request, $id)
    {
        // dd($request->all());

        $validated = $request->validate($this->getValidationRules($request->question_type));
        
        DB::beginTransaction();
        
        try {
            $question = PlgQuestion::findOrFail($id);
            
            $this->updateQuestion($question, $validated);
            $this->syncCategories($question, $validated['category_id']);
            
            if (in_array($validated['question_type'], ['single', 'multiple'])) {
                $this->updateAnswers($question, $validated);
            } else {
                $question->answers()->delete();
            }
            
            $this->handleMediaAttachment($question, $validated);
            
            DB::commit();
            
            return redirect()
                ->route('admin.questions.index')
                ->with('success', 'Questão atualizada com sucesso!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->with('error', 'Erro ao atualizar questão: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Remove uma questão
     */
    public function destroy($id)
    {
        $question = PlgQuestion::findOrFail($id);
        $question->delete();
        
        return response()->json(['success' => true]);
    }

    // ====================================================
    //                  MÉTODOS DE IMPORTAÇÃO
    // ====================================================

    /**
     * Exibe página de importação
     */
    public function import()
    {
        $categories = PlgCategories::orderBy('title')->get();
        return view('panel.admin.questions.import', compact('categories'));
    }

    /**
     * Preview do arquivo CSV
     */
    public function previewCsv(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
            'category_id' => 'required|exists:plg_categories,id',
        ]);

        $csvData = $this->processCsvFile($request->file('csv_file'));

        session([
            'csv_preview_data' => $csvData,
            'category_id' => $request->category_id
        ]);

        return redirect()->route('admin.questions.import');
    }

    /**
     * Importa questões do CSV
     */
    public function importCsv(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:plg_categories,id',
            'data' => 'required|array',
        ]);

        DB::beginTransaction();
        
        try {
            $category = PlgCategories::find($request->category_id);
            $importedQuestions = $this->processImportData($request->data, $request->category_id);

            DB::commit();

            session()->forget(['csv_preview_data', 'category_id']);
            session(['imported_questions' => $importedQuestions]);

            return redirect()->route('admin.questions.import')
                ->with('success', sprintf(
                    'Importação concluída! %d questões importadas para a categoria %s',
                    count($importedQuestions),
                    $category->title
                ));
                    
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('admin.questions.import')
                ->with('error', 'Erro ao importar questões: ' . $e->getMessage());
        }
    }

    // ====================================================
    //                  MÉTODOS AUXILIARES - QUESTÕES
    // ====================================================
    
    /**
     * Cria uma nova questão
     */
    private function createQuestion(array $data): PlgQuestion
    {
        $user = Auth::user();

        return PlgQuestion::create([
            'company_id' => $user->company_id,
            'user_id' => $user->id, // Auto-preenchimento do user_id
            'question' => $data['question'],
            'question_type' => $data['question_type'],
            'answer_format' => $data['answer_format'] ?? 'numeric',
            'essay_answer' => $data['question_type'] === 'essay' ? $data['essay_answer'] : null,
            'free' => $data['free'] ?? false,
            'imported' => 0,
        ]);
    }
    
    /**
     * Atualiza uma questão existente
     */
    private function updateQuestion(PlgQuestion $question, array $data): void
    {
        $question->update([
            'question' => $data['question'],
            'question_type' => $data['question_type'],
            'answer_format' => $data['answer_format'] ?? $question->answer_format ?? 'numeric',
            'essay_answer' => $data['question_type'] === 'essay' ? $data['essay_answer'] : null,
            'free' => $data['free'] ?? false,
        ]);
    }
    
    /**
     * Sincroniza categorias da questão
     */
    private function syncCategories(PlgQuestion $question, array $categoryIds): void
    {
        $question->categories()->detach();
        
        foreach ($categoryIds as $categoryId) {
            $question->categories()->attach($categoryId, ['company_id' => 1]);
        }
    }

    // ====================================================
    //                  MÉTODOS AUXILIARES - RESPOSTAS
    // ====================================================
    
    /**
     * Salva as respostas para uma nova questão
     */
    private function saveAnswers(PlgQuestion $question, array $data): void
    {
        
                
        foreach ($data['answers'] as $index => $answerData) {
            $answerNumber = $index + 1;
            $isCorrect = $this->isAnswerCorrect($data, $answerNumber);
            $explanation = $this->getExplanationForAnswer($data, $answerNumber, $isCorrect);
            //$explanation = $data['explanation-'.$answerNumber];
            
            // ✅ REMOVIDA variável $answer desnecessária - criar diretamente
            PlgQuestionAnswer::create([
                'question_id' => $question->id,
                'company_id' => 1,
                'answer_number' => $answerNumber,
                'answer' => $answerData['answer'] ?? '',
                'correct' => $isCorrect,
                'explanation' => $explanation,
                'caption' => $answerData['caption'] ?? null,
            ]);
        }
    }
    
    /**
     * Atualiza as respostas de uma questão existente
     */
    private function updateAnswers(PlgQuestion $question, array $data): void
    {
        // dd($data['answers']);

        // Remove respostas excedentes
        $maxAnswerNumber = count($data['answers']);
        $question->answers()->where('answer_number', '>', $maxAnswerNumber)->delete();

        foreach ($data['answers'] as $index => $answerData) {
            $answerNumber = $index + 1;
            $isCorrect = $this->isAnswerCorrect($data, $answerNumber);
            $explanation = $this->getExplanationForAnswer($data, $answerNumber, $isCorrect);





            // ✅ REMOVIDA variável $answer desnecessária - updateOrCreate diretamente
            PlgQuestionAnswer::updateOrCreate(
                [
                    'question_id' => $question->id,
                    'answer_number' => $answerNumber,
                ],
                [
                    'company_id' => 1,
                    'answer' => $answerData['answer'] ?? '',
                    'correct' => $isCorrect,
                    'explanation' => $explanation,
                    'caption' => $answerData['caption'] ?? null,
                ]
            );
        }
    }
    
    /**
     * Verifica se uma resposta é correta
     */
    private function isAnswerCorrect(array $data, int $answerNumber): bool
    {
        if ($data['question_type'] === 'single') {
            return (int)($data['correct_answer'] ?? 0) === $answerNumber;
        }
        
        $correctAnswers = $data['correct_answers'] ?? [];
        return in_array($answerNumber, $correctAnswers) || in_array((string)$answerNumber, $correctAnswers);
    }

    /**
     * Obtém a explicação para uma resposta
     */
    private function getExplanationForAnswer(array $data, int $answerNumber, bool $isCorrect): ?string
    {

        //dd($data);

        if (!$isCorrect) {
            return null;
        }

        /*if ($data['question_type'] === 'single') {
            return $data['explanation'] ?? null;
        }*/
        
        return $data['explanations'][$answerNumber] ?? null;
    }

    // ====================================================
    //                  MÉTODOS AUXILIARES - MÍDIA
    // ====================================================
    
    /**
     * Gerencia anexo de mídia usando Plank Mediables
     */
    private function handleMediaAttachment(PlgQuestion $question, array $data): void
    {

        if (!empty($data['question_image'])) {


            $media = \Plank\Mediable\Media::find($data['question_image']);
            if ($media) {
                $question->media()->detach();
                $question->media()->attach($media->id, [
                    'tag' => 'question_image',
                    'caption' => $data['question_image_caption'] ?? null,
                    'order'=>1
                ]);
            }
        } else {
            $question->detachMediaTags('question_image');
        }



        if (isset($data['answers'])) {
            foreach ($data['answers'] as $index => $answerData) {
                $answerNumber = $index + 1;
                $answer = $question->answers()->where('answer_number', $answerNumber)->first();

                if ($answer) {
                    if (!empty($answerData['image'])) {
                        $media = \Plank\Mediable\Media::find($answerData['image']);
                        if ($media) {
                            $answer->media()->detach();
                            $answer->media()->attach($media->id, [
                                'tag' => 'answer_image',
                                'caption' => $answerData['caption'] ?? null,
                                'order'=>1
                            ]);
                        }
                    } else {
                        $answer->detachMediaTags('answer_image');
                    }
                }
            }
        }


    }
    


    // ====================================================
    //                  MÉTODOS AUXILIARES - IMPORTAÇÃO
    // ====================================================
    
    /**
     * Processa arquivo CSV
     */
    private function processCsvFile($file): array
    {
        $csvData = [];
        
        if (($handle = fopen($file->getPathname(), 'r')) !== false) {
            fgetcsv($handle); // Pula cabeçalho

            while (($data = fgetcsv($handle, 1000, ",")) !== false) {
                if (count($data) < 7) continue;
                
                // Garantir índices existam
                $data[7] = $data[7] ?? '';
                $data[8] = $data[8] ?? 0;
                
                $csvData[] = $data;
            }
            fclose($handle);
        }
        
        return $csvData;
    }
    
    /**
     * Processa dados de importação
     */
    private function processImportData(array $data, int $categoryId): array
    {
        $importedQuestions = [];
        $category = PlgCategories::find($categoryId);
        
        foreach ($data as $row) {
            if (!isset($row[0], $row[6])) continue;

            $question = PlgQuestion::create([
                'company_id' => 1,
                'question' => $row[0],
                'question_type' => 'single',
                'free' => $row[8] ?? 0,
                'imported' => 1,
            ]);

            $question->categories()->attach($categoryId, ['company_id' => 1]);

            $answers = $this->createImportAnswers($question, $row);

            $importedQuestions[] = [
                'question' => $question,
                'answers' => $answers,
                'category' => $category
            ];
        }
        
        return $importedQuestions;
    }
    
    /**
     * Cria respostas para questão importada
     */
    private function createImportAnswers(PlgQuestion $question, array $row): array
    {
        $answers = [];
        $explanation = $row[7] ?? null;
        
        for ($i = 1; $i <= 5; $i++) {
            $answerText = $row[$i] ?? "Alternativa $i";
            $isCorrect = ((int)$row[6] === $i);
            
            // ✅ REMOVIDA variável desnecessária - adicionar diretamente ao array
            $answers[] = PlgQuestionAnswer::create([
                'question_id' => $question->id,
                'company_id' => 1,
                'answer_number' => $i,
                'answer' => $answerText,
                'correct' => $isCorrect,
                'explanation' => $isCorrect ? $explanation : null,
            ]);
        }
        
        return $answers;
    }

    // ====================================================
    //                  MÉTODOS AUXILIARES - VALIDAÇÃO
    // ====================================================
    
    /**
     * Retorna regras de validação baseadas no tipo de questão
     */
    private function getValidationRules(string $questionType): array
    {
        $rules = [
            'question' => 'required|string|max:12500',
            'free' => 'boolean',
            'category_id' => 'required|array',
            'category_id.*' => 'exists:plg_categories,id',
            'question_type' => 'required|in:single,multiple,essay',
            'answer_format' => 'nullable|string|in:numeric,alpha,roman,none',
            'question_image' => 'nullable|integer|exists:media,id',
            'question_image_caption' => 'nullable|string|max:255',
        ];

        if ($questionType === 'essay') {
            $rules['essay_answer'] = 'required|string';
        } else {
            $rules['answers'] = 'required|array|min:2';
            $rules['answers.*.answer'] = 'nullable|string|max:12500';
            $rules['answers.*.image'] = 'nullable|integer|exists:media,id';
            $rules['answers.*.caption'] = 'nullable|string|max:255';

            if ($questionType === 'single') {
                $rules['correct_answer'] = 'required|integer|min:1';
                $rules['explanations'] = 'nullable|array';
            } else {
                $rules['correct_answers'] = 'required|array|min:1';
                $rules['correct_answers.*'] = 'integer|min:1';
                $rules['explanations'] = 'nullable|array';
            }
        }

        //dd($rules);

        return $rules;

        
    }

    // ====================================================
    //                  MÉTODOS AUXILIARES - DATATABLE
    // ====================================================
    
    /**
     * Colunas do DataTable
     */
    private function getDataTableColumns(): array
    {
        return [
            ['data' => 'id', 'name' => 'plg_questions.id', 'label' => 'ID'],
            ['data' => 'question', 'name' => 'plg_questions.question', 'label' => 'Questão'],
            ['data' => 'categories', 'name' => 'categories', 'label' => 'Categorias'],
            ['data' => 'free', 'name' => 'plg_questions.free', 'label' => 'Gratuita'],
            ['data' => 'imported', 'name' => 'plg_questions.imported', 'label' => 'Origem'],
            ['data' => 'actions', 'name' => 'actions', 'label' => 'Ações', 'orderable' => false, 'searchable' => false],
        ];
    }
    
    /**
     * Filtros do DataTable
     */
    private function getDataTableFilters(): array
    {
        return [
            [
                'label' => 'Categoria', 
                'column' => 'categories', 
                'options' => PlgCategories::orderBy('title')->get()
                    ->map(fn($c) => ['value' => $c->title, 'label' => $c->title])
            ],
            [
                'label' => 'Gratuita', 
                'column' => 'free', 
                'options' => [
                    ['value' => '1', 'label' => 'Sim'],
                    ['value' => '0', 'label' => 'Não'],
                ]
            ],
            [
                'label' => 'Origem', 
                'column' => 'imported', 
                'options' => [
                    ['value' => '1', 'label' => 'Importada'],
                    ['value' => '0', 'label' => 'Manual'],
                ]
            ],
        ];
    }
    
    /**
     * Formata coluna da questão
     */
    private function formatQuestionColumn(PlgQuestion $question): string
    {
        $output = '<div class="flex items-start">';
        
        // ✅ Usar fallback para compatibilidade - primeiro tenta Mediables, depois campo image
        $imageUrl = null;
        
        // Tentar usar Plank Mediables
        try {
            if (method_exists($question, 'getFirstMediaUrl')) {
                $imageUrl = $question->getFirstMediaUrl('question_image');
            }
        } catch (\Exception $e) {
            // Silenciar erro e usar fallback
        }
        
        // Fallback para campo image direto
        if (empty($imageUrl) && !empty($question->image)) {
            $imageUrl = $question->image;
        }
        
        if ($imageUrl) {
            $output .= '<div class="flex-shrink-0 mr-3">';
            $output .= '<img src="' . $imageUrl . '" alt="Imagem da Questão" class="w-12 h-12 object-cover rounded">';
            $output .= '</div>';
        }
        
        $output .= '<div class="flex-grow">' . 
                   substr($question->question, 0, 100) . 
                   (strlen($question->question) > 100 ? '...' : '') . 
                   '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Coluna de ações
     */
    private function getActionsColumn(PlgQuestion $question): string
    {
        return view('panel.admin.includes.table-actions', [
            'actions' => [
                [
                    'type' => 'edit',
                    'route' => route('admin.questions.edit', $question->id),
                    'title' => 'Editar questão'
                ],
                [
                    'type' => 'show',
                    'route' => route('admin.questions.show', $question->id),
                    'title' => 'Visualizar questão'
                ],
                [
                    'type' => 'delete',
                    'route' => route('admin.questions.destroy', $question->id),
                    'title' => 'Excluir questão',
                    'confirm_message' => 'Tem certeza que deseja excluir esta questão?'
                ]
            ]
        ])->render();
    }

    // ====================================================
    //                  MÉTODOS AUXILIARES - FORMULÁRIO
    // ====================================================
    
    /**
     * Prepara dados para o formulário de edição
     */
    private function prepareFormData(PlgQuestion $question): array
    {
        $correctAnswerNumber = null;
        $correctAnswersArray = [];
        
        if ($question->question_type === 'single') {
            $correctAnswer = $question->answers->where('correct', 1)->first();
            if ($correctAnswer) {
                $correctAnswerNumber = $correctAnswer->answer_number;
            }
        } elseif ($question->question_type === 'multiple') {
            $correctAnswersArray = $question->answers
                ->where('correct', 1)
                ->pluck('answer_number')
                ->toArray();
        }
        
        return [
            'correctAnswerNumber' => $correctAnswerNumber,
            'correctAnswersArray' => $correctAnswersArray
        ];
    }
} 