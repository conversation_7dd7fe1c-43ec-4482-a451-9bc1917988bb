@props(['test', 'course', 'module', 'hasAccess' => true])

@php
    $student = auth('students')->user();
    $questionsCount = is_array($test->questions) ? count($test->questions) : 0;

    // Obter informações de matrícula do módulo
    $enrollment = $student ? $student->getModuleEnrollment($module) : null;

    // Usar o novo sistema de estados
    $quizStats = $student ? $student->getQuizStats($test) : null;
    $state = $quizStats['state'] ?? 'NEVER_STARTED';
    $incompleteAttempt = $quizStats['incomplete_attempt'] ?? null;
    $bestScore = $quizStats['best_score'] ?? 0;
    $hasPassed = $quizStats['has_passed'] ?? false;
    $passingScore = $test->passing_score ?? 70;

    // Definir cores baseado no tipo de teste
    $isQuiz = $test->test_type === 'quiz';
    $colorClass = $isQuiz ? 'red' : 'purple';
    $icon = $isQuiz ? 'fa-play' : 'fa-graduation-cap';

    // Classes CSS baseadas no acesso e tipo
    $borderClass = $hasAccess ? "border-{$colorClass}-200" : 'border-gray-200';
    $bgClass = $hasAccess ? "bg-{$colorClass}-50" : 'bg-gray-50';
    $textClass = $hasAccess ? "text-{$colorClass}-600" : 'text-gray-600';
    $buttonClass = $hasAccess ? "bg-{$colorClass}-600 hover:bg-{$colorClass}-700" : 'bg-gray-400';
@endphp

<article class="flex justify-between flex-col border {{ $borderClass }} dark:border-{{ $hasAccess ? $colorClass : 'gray' }}-800 {{ $bgClass }} dark:bg-{{ $hasAccess ? $colorClass : 'gray' }}-900/20 rounded-xl p-3 space-y-3">
    <header class="space-y-2">
        <div class="flex items-center gap-2 font-semibold {{ $textClass }} dark:text-{{ $hasAccess ? $colorClass : 'gray' }}-400 text-sm">
            @if($state === 'NEVER_STARTED' || $state === 'IN_PROGRESS')
                <i class="fas {{ $icon }} {{ $textClass }} text-lg mt-1"></i>
            @else
                @if($hasPassed)
                    <i class="fas fa-check-circle text-green-600 text-lg mt-1"></i>
                @else
                    <i class="fas fa-times-circle text-orange-600 text-lg mt-1"></i>
                @endif
            @endif
            {{ $test->title }}
            @if($state !== 'NEVER_STARTED' && $state !== 'IN_PROGRESS')
                @if($hasPassed)
                    <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Aprovado</span>
                    <span class="flex items-center gap-1 {{ $textClass }}">
                        <i class="fas fa-star text-xs"></i>
                        {{ $bestScore }}%
                    </span>
                @else
                    <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">Reprovado</span>
                    <span class="flex items-center gap-1 text-red-600">
                        <i class="fas fa-star text-xs"></i>
                        {{ $bestScore }}%
                    </span>
                @endif
            @endif
        </div>

        <!-- Informações do Teste -->
        <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <div class="flex items-center gap-4 flex-wrap">
                <span class="flex items-center gap-1">
                    <i class="fas fa-question-circle text-xs"></i>
                    {{ $questionsCount }} questões
                </span>
                @if($test->max_attempts > 0)
                    <span class="flex items-center gap-1">
                        <i class="fas fa-redo text-xs"></i>
                        {{ $test->max_attempts }} {{ $test->max_attempts == 1 ? 'tentativa' : 'tentativas' }}
                    </span>
                @else
                    <span class="flex items-center gap-1">
                        <i class="fas fa-infinity text-xs"></i>
                        ∞
                    </span>
                @endif
                @if(!$isQuiz && $test->passing_score)
                    <span class="flex items-center gap-1">
                        <i class="fas fa-target text-xs"></i>
                        Mín: {{ $test->passing_score }}%
                    </span>
                @endif
            </div>


        </div>
    </header>

    @if($hasAccess)
        <div class="flex space-x-2">
            @if($state === 'NEVER_STARTED' || $state === 'IN_PROGRESS')
                <!-- Nunca iniciado ou em andamento: Botão Iniciar (Verde) -->
                <a href="{{ $isQuiz ? route('student.quiz.start', [$course->slug, $module->slug, $test->slug]) : route('student.test.start', [$course->slug, $module->slug, $test->slug]) }}?restart=1"
                   @if($state === 'IN_PROGRESS') onclick="return confirm('Tem certeza que deseja iniciar este teste? Isso irá zerar sua tentativa atual.')" @endif
                   class="w-full inline-flex items-center justify-center gap-2 px-6 py-1.5 rounded-lg bg-green-600 hover:bg-green-700 text-white text-sm font-semibold transition-colors">
                    <i class="fas fa-play text-white text-lg mt-1"></i>
                    Iniciar
                </a>
            @else
                <!-- Concluído: Refazer (Laranja) -->
                <a href="{{ $isQuiz ? route('student.quiz.start', [$course->slug, $module->slug, $test->slug]) : route('student.test.start', [$course->slug, $module->slug, $test->slug]) }}?restart=1"
                   onclick="return confirm('Tem certeza que deseja refazer este teste? Isso irá zerar sua tentativa atual.')"
                   class="w-full inline-flex items-center justify-center gap-2 px-6 py-1.5 rounded-lg bg-orange-600 hover:bg-orange-700 text-white text-sm font-semibold transition-colors">
                    <i class="fas fa-redo text-white text-lg mt-1"></i>
                    Refazer
                </a>
            @endif
        </div>
    @else
        <button disabled
                class="w-full inline-flex items-center justify-center gap-2 px-6 py-1.5 rounded-lg bg-gray-400 text-white text-sm font-semibold cursor-not-allowed">
            <i class="fas fa-lock text-white text-lg mt-1"></i>
            Acesso Restrito
        </button>
    @endif
</article>
