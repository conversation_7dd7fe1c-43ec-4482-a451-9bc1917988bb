/* Customização do Choices.js para padronizar com os inputs */
.choices {
    width: 100% !important;
    margin-bottom: 0 !important;
}

.choices__inner {
    min-height: 42px !important;
    border: 1px solid #d1d5db !important; /* border-zinc-300 */
    border-radius: 0.375rem !important; /* rounded-md */
    background-color: #ffffff !important;
    padding: 8px 12px !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.15s ease-in-out !important;
    font-size: 1rem !important;
    line-height: 1 !important;
}

.choices:focus-within .choices__inner,
.choices.is-focused .choices__inner {
    border-color: rgba(239, 68, 68, 0.3) !important; /* focus:ring-trends-primary/30 */
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.choices__placeholder {
    color: #9ca3af !important; /* text-zinc-400 */
    opacity: 1 !important;
}

.choices[data-type*="select-one"] .choices__inner {
    padding-bottom: 8px !important;
    padding-top: 8px !important;
}

.choices__list--single .choices__item {
    color: #374151 !important; /* text-zinc-700 */
    padding: 0 !important;
    background-color: transparent !important;
}

/* Dropdown do Choices.js */
.choices__list--dropdown {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    background-color: #ffffff !important;
    z-index: 9999 !important;
}

.choices__list--dropdown .choices__item {
    padding: 8px 12px !important;
    color: #374151 !important;
    background-color: #ffffff !important;
    border-bottom: none !important;
}

.choices__list--dropdown .choices__item--highlighted {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
}

.choices__list--dropdown .choices__item[aria-selected="true"] {
    background-color: #ef4444 !important;
    color: #ffffff !important;
}

/* Seta do dropdown */
.choices[data-type*="select-one"]:after {
    border-color: #6b7280 transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 5px 4px 0 4px !important;
    content: "" !important;
    display: block !important;
    height: 0 !important;
    margin-top: -2px !important;
    pointer-events: none !important;
    position: absolute !important;
    right: 11.5px !important;
    top: 50% !important;
    width: 0 !important;
}

.choices[data-type*="select-one"].is-open:after {
    border-color: transparent transparent #6b7280 transparent !important;
    border-width: 0 4px 5px 4px !important;
    margin-top: -7px !important;
}

/* Multiple select styling */
.choices[data-type*="select-multiple"] .choices__inner {
    padding: 4px 8px !important;
}

.choices__list--multiple .choices__item {
    background-color: #ef4444 !important;
    border: 1px solid #ef4444 !important;
    border-radius: 0.25rem !important;
    color: #ffffff !important;
    font-size: 0.875rem !important;
    margin-bottom: 3px !important;
    margin-right: 3px !important;
    padding: 4px 8px !important;
}

.choices__list--multiple .choices__item.is-highlighted {
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
}

.choices__button {
    background-image: none !important;
    border-left: 1px solid rgba(255, 255, 255, 0.25) !important;
    color: #ffffff !important;
    font-size: 14px !important;
    margin-left: 4px !important;
    padding-left: 8px !important;
    padding-right: 4px !important;
}

.choices__button:hover,
.choices__button:focus {
    color: #ffffff !important;
    opacity: 0.8 !important;
}

/* Dark mode para Choices.js */
.dark .choices__inner {
    background-color: rgba(39, 39, 42, 0.5) !important; /* dark:bg-zinc-800/50 */
    border-color: #52525b !important; /* dark:border-zinc-700 */
    color: #e4e4e7 !important; /* dark:text-zinc-200 */
}

.dark .choices__list--single .choices__item {
    color: #e4e4e7 !important; /* dark:text-zinc-200 */
}

.dark .choices__placeholder {
    color: #71717a !important; /* dark:text-zinc-400 */
}

.dark .choices__list--dropdown {
    background-color: #27272a !important; /* dark:bg-zinc-800 */
    border-color: #52525b !important; /* dark:border-zinc-700 */
}

.dark .choices__list--dropdown .choices__item {
    background-color: #27272a !important;
    color: #e4e4e7 !important;
}

.dark .choices__list--dropdown .choices__item--highlighted {
    background-color: rgba(239, 68, 68, 0.2) !important;
    color: #ef4444 !important;
}

.dark .choices__list--dropdown .choices__item--selectable.is-highlighted {
    background-color: rgba(239, 68, 68, 0.2) !important;
    color: #ef4444 !important;
}

.dark .choices__list--dropdown .choices__item[aria-selected="true"] {
    background-color: #ef4444 !important;
    color: #ffffff !important;
}

/* Disabled state */
.choices.is-disabled .choices__inner {
    background-color: #f3f4f6 !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
}

.dark .choices.is-disabled .choices__inner {
    background-color: #374151 !important;
    color: #6b7280 !important;
}

/* Loading state */
.choices.is-loading:after {
    border: 2px solid #f3f3f3 !important;
    border-radius: 50% !important;
    border-top: 2px solid #ef4444 !important;
    width: 16px !important;
    height: 16px !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Ensure proper spacing */
.choices + .choices {
    margin-top: 0 !important;
}
