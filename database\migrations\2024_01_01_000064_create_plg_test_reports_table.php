<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_test_reports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->foreignId('test_id')->constrained('plg_tests')->onDelete('cascade');
            $table->foreignId('student_id')->constrained('plg_students')->onDelete('cascade');
            $table->foreignId('best_attempt_id')->nullable()->constrained('plg_test_attempts')->onDelete('set null');

            // Estatísticas consolidadas
            $table->decimal('best_score', 5, 2)->nullable();
            $table->decimal('average_score', 5, 2)->nullable();
            $table->integer('total_attempts')->default(0);
            $table->integer('completed_attempts')->default(0);
            $table->integer('abandoned_attempts')->default(0);

            // Datas importantes
            $table->timestamp('first_attempt_at')->nullable();
            $table->timestamp('last_attempt_at')->nullable();
            $table->timestamp('passed_at')->nullable(); // Quando passou pela primeira vez

            // Status final
            $table->boolean('passed')->default(false);
            $table->boolean('can_attempt')->default(true); // Se pode fazer nova tentativa
            $table->timestamp('next_attempt_available_at')->nullable(); // Quando pode tentar novamente

            $table->timestamps();

            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');

            // Índices para performance
            $table->index(['company_id', 'test_id']);
            $table->index(['student_id', 'test_id']);
            $table->index(['test_id', 'passed']);
            $table->index(['student_id', 'passed']);
            $table->index(['best_score']);
            $table->index(['can_attempt']);
            $table->index(['next_attempt_available_at']);
            $table->unique(['company_id', 'student_id', 'test_id'], 'plg_test_reports_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_test_reports');
    }
};
