@extends('layouts.panel')

@section('title', 'Conteúdos do Módulo: ' . $module->title)
@section('page_title', 'Conteúdos do Módulo')

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Conteúdos do Módulo: ' . $module->title,
            'description' => 'Gerencie todos os conteúdos disponíveis neste módulo.',
            'actions' => [
                [
                    'route' => route('admin.modules.contents.create', $module->id),
                    'text' => 'Novo Conteúdo',
                    'icon' => 'fas fa-plus',
                    'class' => 'bg-trends-primary text-white',
                    'hover_class' => 'bg-trends-primary/90'
                ],
                [
                    'route' => route('admin.modules.edit', $module->id),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ]
            ]
        ])

        @include('panel.admin.includes.datatable', [
            'id' => $id,
            'columns' => $columns,
            'ajaxUrl' => $ajaxUrl,
            'filters' => $filters ?? [],
            'clearFiltersBtn' => $clearFiltersBtn ?? false
        ])
    </div>
</div>
@endsection
