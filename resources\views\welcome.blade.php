<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trends QUIZ - Bem-vindo</title>
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
        }
        .card {
            backdrop-filter: blur(10px);
            background-color: rgba(30, 30, 30, 0.6);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(75, 75, 75, 0.3);
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="antialiased text-gray-200">
    <div class="relative flex items-center justify-center min-h-screen">
        <div class="absolute top-0 w-full flex justify-center p-6">
            <img src="{{ asset('images/Logo_Trends-QUIZ_branco-1024x361.png') }}" alt="Trends QUIZ Logo" class="h-16">
        </div>
        
        <div class="container px-4 py-8 mx-auto">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold mb-4">Bem-vindo ao Trends QUIZ</h1>
                <p class="text-xl">Escolha como deseja acessar a plataforma</p>
            </div>
            
            <div class="flex flex-col md:flex-row justify-center items-stretch gap-8">
                <!-- Cartão de Aluno -->
                <a href="{{ route('student.login') }}" class="card w-full md:w-80 p-8 rounded-xl text-center flex flex-col justify-between">
                    <div>
                        <div class="mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">Sou Aluno</h2>
                        <p class="text-gray-400 mb-6">Acesse seus cursos, faça testes e acompanhe seu progresso</p>
                    </div>
                    <div class="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        Entrar como Aluno
                    </div>
                </a>
                
                <!-- Cartão de Professor/Admin -->
                <a href="{{ route('admin.login') }}" class="card w-full md:w-80 p-8 rounded-xl text-center flex flex-col justify-between">
                    <div>
                        <div class="mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">Sou Professor/Admin</h2>
                        <p class="text-gray-400 mb-6">Gerencie cursos, crie conteúdos e acompanhe alunos</p>
                    </div>
                    <div class="inline-block px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                        Entrar como Professor/Admin
                    </div>
                </a>
            </div>
            
            <!-- Botão de Registro -->
            <div class="text-center mt-12">
                <p class="mb-4">Ainda não tem uma conta?</p>
                <a href="{{ route('student.login') }}#register" class="inline-block px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors">
                    Registrar-se como Aluno
                </a>
            </div>
        </div>
        
        <div class="absolute bottom-4 w-full text-center text-sm text-gray-500">
            &copy; {{ date('Y') }} Trends QUIZ. Todos os direitos reservados.
        </div>
    </div>
</body>
</html> 