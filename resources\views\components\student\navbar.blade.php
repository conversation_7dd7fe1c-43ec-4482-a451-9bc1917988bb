@props(['user'])

<!-- Navbar -->
<nav class="bg-gray-900 border-b border-gray-900 shadow">
    <div class="container mx-auto">
        <div class="flex justify-between h-16 relative px-3">
            <div class="flex">
                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center">
                    <a href="{{ route('student.dashboard') }}" class="flex items-center">
                        <img src="{{ asset('images/logo.png') }}" alt="Logo" class="h-8 w-auto">
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-8 sm:ml-10 sm:flex">
                    <x-student.nav-links />
                </div>
            </div>

            <div class="hidden sm:ml-6 sm:flex sm:items-center space-x-4">
                <!-- Dark Mode Toggle -->
                <button id="darkModeToggle" class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-800 text-gray-200 hover:bg-gray-700 transition-colors duration-200 dashboard-hide">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:inline"></i>
                </button>

                <!-- Profile dropdown -->
                <div class="relative" id="profileDropdown">
                    <button id="profileButton" class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-800 text-gray-200 hover:bg-gray-700 transition-colors duration-200">
                        <i class="fas fa-user text-sm"></i>
                    </button>
                    <div id="profileMenu" class="absolute right-0 top-full origin-top-right w-48 bg-gray-900 border border-gray-800 rounded-md shadow-lg py-1 hidden z-50">
                        <div class="px-4 py-3 border-b border-gray-800">
                            <p class="text-sm font-medium text-white">{{ $user->name }}</p>
                            <p class="text-xs text-gray-400 truncate">{{ $user->email }}</p>
                        </div>
                        <a href="{{ route('student.profile') }}" class="block px-4 py-2 text-sm text-gray-200 hover:bg-gray-800 transition-colors duration-200">
                            <i class="fas fa-user-circle mr-2"></i>Perfil
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="w-full text-left block px-4 py-2 text-sm text-gray-200 hover:bg-gray-800 transition-colors duration-200">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sair
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center sm:hidden">
                <button id="mobileMenuButton" class="inline-flex items-center justify-center p-2 rounded-md text-gray-200 hover:text-white hover:bg-gray-800 focus:outline-none focus:bg-gray-800 focus:text-white transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path id="menuIcon" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path id="closeIcon" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile navigation -->
    <div id="mobileMenu" class="sm:hidden bg-gray-900 border-b border-gray-900 hidden">
        <div class="pt-2 pb-3 space-y-1">
            <x-student.nav-links :mobile="true" />
        </div>
        <div class="pt-4 pb-3 border-t border-gray-800">
            <div class="flex items-center px-4">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-gray-800 flex items-center justify-center">
                        <i class="fas fa-user text-gray-300"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-base font-medium text-gray-200">{{ $user->name }}</div>
                    <div class="text-sm font-medium text-gray-400">{{ $user->email }}</div>
                </div>
            </div>
            <div class="mt-3 space-y-1">
                <button id="darkModeToggleMobile" class="flex w-full items-center pl-3 pr-4 py-2 border-l-4 border-transparent text-gray-300 hover:bg-gray-800 text-base font-medium dashboard-hide">
                    <i class="fas fa-moon mr-2 dark:hidden"></i>
                    <i class="fas fa-sun mr-2 hidden dark:inline"></i>
                    <span class="dark:hidden">Modo escuro</span>
                    <span class="hidden dark:inline">Modo claro</span>
                </button>
                <a href="{{ route('student.profile') }}" class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-gray-300 hover:bg-gray-800 text-base font-medium">
                    <i class="fas fa-user-circle mr-2"></i>Perfil
                </a>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="w-full text-left block pl-3 pr-4 py-2 border-l-4 border-transparent text-gray-300 hover:bg-gray-800 text-base font-medium">
                        <i class="fas fa-sign-out-alt mr-2"></i>Sair
                    </button>
                </form>
            </div>
        </div>
    </div>
</nav>

<!-- Navbar JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Profile Dropdown
        const profileButton = document.getElementById('profileButton');
        const profileMenu = document.getElementById('profileMenu');
        const profileDropdown = document.getElementById('profileDropdown');

        if (profileButton && profileMenu) {
            let isProfileOpen = false;
            let profileTimeout;

            // Função para mostrar o menu
            function showProfileMenu() {
                clearTimeout(profileTimeout);
                profileMenu.classList.remove('hidden');
                isProfileOpen = true;
            }

            // Função para esconder o menu
            function hideProfileMenu() {
                profileTimeout = setTimeout(() => {
                    profileMenu.classList.add('hidden');
                    isProfileOpen = false;
                }, 100); // Pequeno delay para permitir movimento do mouse
            }

            // Eventos do botão
            profileButton.addEventListener('mouseenter', showProfileMenu);
            profileButton.addEventListener('mouseleave', hideProfileMenu);
            profileButton.addEventListener('click', function(e) {
                e.preventDefault();
                if (isProfileOpen) {
                    hideProfileMenu();
                } else {
                    showProfileMenu();
                }
            });

            // Eventos do menu
            profileMenu.addEventListener('mouseenter', showProfileMenu);
            profileMenu.addEventListener('mouseleave', hideProfileMenu);

            // Fechar ao clicar fora
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileMenu.classList.add('hidden');
                    isProfileOpen = false;
                }
            });
        }

        // Mobile Menu
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const mobileMenu = document.getElementById('mobileMenu');
        const menuIcon = document.getElementById('menuIcon');
        const closeIcon = document.getElementById('closeIcon');

        if (mobileMenuButton && mobileMenu) {
            let isMobileOpen = false;

            mobileMenuButton.addEventListener('click', function() {
                isMobileOpen = !isMobileOpen;

                if (isMobileOpen) {
                    mobileMenu.classList.remove('hidden');
                    menuIcon.classList.add('hidden');
                    closeIcon.classList.remove('hidden');
                } else {
                    mobileMenu.classList.add('hidden');
                    menuIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');
                }
            });

            // Fechar menu mobile ao redimensionar para desktop
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 640) { // sm breakpoint
                    mobileMenu.classList.add('hidden');
                    menuIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');
                    isMobileOpen = false;
                }
            });
        }

        // Dark Mode Toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        const darkModeToggleMobile = document.getElementById('darkModeToggleMobile');

        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', function() {
                toggleDarkMode();
            });
        }

        if (darkModeToggleMobile) {
            darkModeToggleMobile.addEventListener('click', function() {
                toggleDarkMode();
            });
        }

        function toggleDarkMode() {
            // Se estiver no dashboard, não permite alternar o tema
            if (window.isDashboardPage) {
                // Opcional: mostrar uma mensagem informando que o tema é fixo no dashboard
                alert('O tema escuro é fixo na página inicial.');
                return;
            }

            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
            }
        }
    });
</script>
