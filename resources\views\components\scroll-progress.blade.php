<div id="scroll-progress" class="fixed top-0 left-0 w-full h-1 bg-red-600 origin-left z-50 transform scale-x-0 transition-transform duration-200"></div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const progressBar = document.getElementById('scroll-progress');
        
        if (progressBar) {
            // Função para atualizar a barra de progresso
            function updateProgressBar() {
                const scrollTop = window.scrollY;
                const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollProgress = Math.max(0, Math.min(1, scrollTop / scrollHeight));
                
                progressBar.style.transform = `scaleX(${scrollProgress})`;
            }
            
            // Atualizar imediatamente e depois em cada evento de scroll
            updateProgressBar();
            window.addEventListener('scroll', updateProgressBar);
            window.addEventListener('resize', updateProgressBar);
        } else {
            console.error('Elemento de barra de progresso não encontrado');
        }
    });
</script> 