// Função para alternar entre os temas dark e light
function toggleTheme() {
    const root = document.documentElement;
    const isDark = root.classList.contains('dark');
    const darkIcon = document.getElementById('theme-toggle-dark-icon');
    const lightIcon = document.getElementById('theme-toggle-light-icon');

    // Verificar se os ícones existem
    if (!darkIcon || !lightIcon) return;

    // Remove a classe theme-loaded temporariamente
    root.classList.remove('theme-loaded');

    // Pequeno delay para garantir que a transição seja suave
    setTimeout(() => {
        // Alterna a classe dark no HTML
        if (isDark) {
            root.classList.remove('dark');
            darkIcon.classList.remove('hidden');
            lightIcon.classList.add('hidden');
            localStorage.setItem('theme', 'light');
        } else {
            root.classList.add('dark');
            darkIcon.classList.add('hidden');
            lightIcon.classList.remove('hidden');
            localStorage.setItem('theme', 'dark');
        }

        // Reativa as transições
        setTimeout(() => {
            root.classList.add('theme-loaded');
        }, 50);
    }, 50);
}

// Inicializa o tema baseado na preferência salva
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'dark';
    const root = document.documentElement;
    const darkIcon = document.getElementById('theme-toggle-dark-icon');
    const lightIcon = document.getElementById('theme-toggle-light-icon');

    // Verificar se os ícones existem antes de tentar acessar suas propriedades
    if (darkIcon && lightIcon) {
        // Atualiza os ícones
        if (savedTheme === 'dark') {
            darkIcon.classList.add('hidden');
            lightIcon.classList.remove('hidden');
        } else {
            darkIcon.classList.remove('hidden');
            lightIcon.classList.add('hidden');
        }
    }
}

// Adiciona o evento de clique ao botão de toggle
document.addEventListener('DOMContentLoaded', () => {
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
    initTheme();
}); 