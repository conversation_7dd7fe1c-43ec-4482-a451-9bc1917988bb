@props(['module', 'course', 'currentContent' => null, 'progress' => collect(), 'progressPercentage' => 0])

<!-- Progress Header -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-4">
    <div class="p-4">
        <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-semibold text-gray-500 dark:gray-400">Progresso do Módulo</h3>
            <span class="text-xs text-gray-500 dark:text-gray-400" id="progress-counter">
                {{ $progress->where('status', 'completed')->count() }}/{{ $module->contents->count() }}
            </span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
            @php
                $completedCount = $progress->where('status', 'completed')->count();
                $totalContents = $module->contents->count();
                $isAlmostComplete = $totalContents > 0 && $completedCount === ($totalContents - 1);
                $isComplete = $progressPercentage >= 100;

                $progressBarClass = $isComplete ? 'bg-green-600' : ($isAlmostComplete ? 'bg-blue-600' : 'bg-gray-400');
            @endphp
            <div class="{{ $progressBarClass }} h-2 rounded-full transition-all duration-300"
                 id="progress-bar"
                 style="width: {{ $progressPercentage }}%"></div>
        </div>
    </div>
</div>

<!-- Content Navigation -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="border-b border-gray-200 dark:border-gray-700">
        <div class="p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Conteúdo do Módulo</h3>
        </div>
    </div>

    <!-- Content List -->
    <div class="p-4">
        <div class="space-y-3">
            @foreach($module->contents->sortBy('order') as $content)
                @php
                    $isCompleted = $progress->has($content->id) && $progress[$content->id]->status === 'completed';
                    $isActive = $currentContent && $currentContent->id === $content->id;
                    $iconClass = $isActive ? 'text-gray-400' : ($isCompleted ? 'text-green-600' : 'text-gray-400');
                @endphp

                <div class="bg-gray-50 dark:bg-gray-700 border {{ $isActive ? 'border-red-300 dark:border-red-600' : 'border-gray-200 dark:border-gray-600' }} rounded-lg p-4 cursor-pointer hover:shadow-sm content-item"
                     data-content-id="{{ $content->id }}"
                     onclick="navigateToContent({{ $content->id }})"
                     data-href="{{ route('student.module.content', [request()->route('courseSlug'), request()->route('moduleSlug'), $content->id]) }}">
                    <div class="flex items-start space-x-3">
                        @if($isCompleted)
                            <i class="fas fa-check-circle text-green-600 text-lg mt-1"></i>
                        @else
                            @switch($content->content_type)
                                @case('video')
                                    <i class="fas fa-video {{ $iconClass }} text-lg mt-1"></i>
                                    @break
                                @case('pdf')
                                    <i class="fas fa-file-pdf {{ $iconClass }} text-lg mt-1"></i>
                                    @break
                                @case('link')
                                    <i class="fas fa-external-link-alt {{ $iconClass }} text-lg mt-1"></i>
                                    @break
                                @case('word')
                                    <i class="fas fa-file-word {{ $iconClass }} text-lg mt-1"></i>
                                    @break
                                @default
                                    <i class="fas fa-file-text {{ $iconClass }} text-lg mt-1"></i>
                            @endswitch
                        @endif

                        <div class="flex-1">
                            <h4 class="text-sm font-semibold text-dark dark:text-gray-300 mb-1 {{ $isActive ? 'text-dark' : '' }}">
                                {{ $content->title }}
                            </h4>
                            <p class="text-xs text-gray-500">
                                {{ ucfirst($content->content_type) }}
                                @if($isCompleted)
                                    • <span class="text-green-600 font-medium">Concluído</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>

<script>

function navigateToContent(contentId) {
    // Buscar o elemento clicado para obter a URL correta
    const contentItem = document.querySelector(`[data-content-id="${contentId}"]`);
    if (contentItem && contentItem.dataset.href) {
        window.location.href = contentItem.dataset.href;
    } else {
        // Fallback: construir URL manualmente
        const currentPath = window.location.pathname;
        const pathParts = currentPath.split('/');

        // Extrair courseSlug e moduleSlug da URL atual
        const courseSlug = pathParts[2]; // /student/[courseSlug]/...
        const moduleSlug = pathParts[3]; // /student/courseSlug/[moduleSlug]/...

        // Navegar para o conteúdo
        window.location.href = `/student/${courseSlug}/${moduleSlug}/conteudo/${contentId}`;
    }
}

function startTest(testSlug, testType) {
    const currentPath = window.location.pathname;
    const pathParts = currentPath.split('/');

    // Extrair courseSlug e moduleSlug da URL atual
    const courseSlug = pathParts[2]; // /student/[courseSlug]/...
    const moduleSlug = pathParts[3]; // /student/courseSlug/[moduleSlug]/...

    // Navegar para o teste baseado no tipo
    if (testType === 'quiz') {
        window.location.href = `/student/${courseSlug}/${moduleSlug}/quiz/${testSlug}`;
    } else {
        window.location.href = `/student/${courseSlug}/${moduleSlug}/teste/${testSlug}`;
    }
}
</script>
