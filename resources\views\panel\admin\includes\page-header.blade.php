<div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
    {{-- Linha com título e botões desktop --}}
    <div class="flex w-full items-center justify-between">
        {{-- Título --}}
        <div class="flex items-center gap-2 relative pl-3">
            <div class="absolute left-0 top-0 bottom-0 w-1 bg-trends-primary rounded-full h-full"></div>
            <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">{{ $title }}</h1>
        </div>

        {{-- ✅ Botões Desktop --}}
        <div class="hidden md:flex items-center gap-2">
            @if(isset($actions) && is_array($actions))
                @foreach($actions as $action)
                    <a href="{{ $action['route'] }}"
                       class="inline-flex items-center gap-2 px-4 py-2 {{ $action['class'] ?? 'bg-trends-primary text-white' }} rounded-lg hover:{{ $action['hover_class'] ?? 'bg-trends-primary/90' }} transition-colors">
                        @if(isset($action['icon']))
                            <i class="{{ $action['icon'] }}"></i>
                        @endif
                        <span>{{ $action['text'] }}</span>
                    </a>
                @endforeach
            @elseif(isset($action_route))
                {{-- Compatibilidade com o formato antigo (um único botão) --}}
                <a href="{{ $action_route }}"
                   class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                    <i class="fas fa-plus"></i>
                    <span>{{ $action_text }}</span>
                </a>
            @endif
        </div>
    </div>

    {{-- Descrição --}}
    @if(isset($description))
        <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-2">
            {{ $description }}
        </p>
    @endif

    {{-- ✅ Botões Mobile --}}
    <div class="flex md:hidden mt-4 flex-col gap-2">
        @if(isset($actions) && is_array($actions))
            @foreach($actions as $action)
                <a href="{{ $action['route'] }}"
                   class="inline-flex items-center justify-center gap-2 px-4 py-2 w-full {{ $action['class'] ?? 'bg-trends-primary text-white' }} rounded-lg hover:{{ $action['hover_class'] ?? 'bg-trends-primary/90' }} transition-colors">
                    @if(isset($action['icon']))
                        <i class="{{ $action['icon'] }}"></i>
                    @endif
                    <span>{{ $action['text'] }}</span>
                </a>
            @endforeach
        @elseif(isset($action_route))
            {{-- Compatibilidade com o formato antigo (um único botão) --}}
            <a href="{{ $action_route }}"
               class="inline-flex items-center justify-center gap-2 px-4 py-2 w-full bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                <i class="fas fa-plus"></i>
                <span>{{ $action_text }}</span>
            </a>
        @endif
    </div>
</div>
