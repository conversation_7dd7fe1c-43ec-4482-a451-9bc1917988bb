@props(['course'])

<div class="bg-white mb-4 dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="p-6">
        <!-- Capa do Curso -->
        <div class="mb-6">
            @php
                $thumbnail = $course->getMedia('thumbnail')->first();
            @endphp
            @if ($thumbnail)
                <img src="{{ route('media.serve', ['path' => $thumbnail->getDiskPath()]) }}?w=400&fit=cover&fm=webp"
                     alt="{{ $course->title }}" 
                     class="w-full h-48 object-cover rounded-lg"
                     onerror="this.src='https://placehold.co/400x240?text={{ urlencode($course->title) }}'">
            @else
                <img src="https://placehold.co/400x240?text={{ urlencode($course->title) }}"
                     alt="{{ $course->title }}" 
                     class="w-full h-48 object-cover rounded-lg">
            @endif
        </div>

        <!-- <PERSON><PERSON><PERSON><PERSON> do Curso -->
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">{{ $course->title }}</h2>

        <!-- Informações do Curso -->
        <div class="space-y-4">
            <!-- Instrutor -->
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Instrutor:</p>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ $course->teacher->name ?? 'Não definido' }}
                    </p>
                </div>
            </div>

            <!-- Nível (se disponível) -->
            @if(isset($course->level) && $course->level)
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Nível:</p>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ ucfirst($course->level) }}</p>
                    </div>
                </div>
            @endif

            <!-- Duração -->
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Duração:</p>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                        @if($course->duration_minutes)
                            @if($course->duration_minutes >= 60)
                                {{ floor($course->duration_minutes / 60) }} horas
                                @if($course->duration_minutes % 60 > 0)
                                    e {{ $course->duration_minutes % 60 }} min
                                @endif
                            @else
                                {{ $course->duration_minutes }} min
                            @endif
                        @else
                            Não definido
                        @endif
                    </p>
                </div>
            </div>

            <!-- Módulos -->
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Módulos:</p>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $course->modules->count() }} módulos</p>
                </div>
            </div>
        </div>
    </div>
</div>
