<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Database\Factories\PlgCategoriesFactory;
use Plank\Mediable\Mediable;
use Plank\Mediable\MediableInterface;

class PlgCategories extends Model implements MediableInterface
{
    use HasFactory, SoftDeletes, Mediable;

    protected $table = 'plg_categories';
    
    protected $fillable = [
        'company_id', 'user_id', 'title', 'slug', 'description', 'icon', 'order', 'active'
    ];
    
    protected $casts = [
        'active' => 'boolean',
    ];

    protected $mediableCollections = ['thumbnail', 'gallery'];
    
    public function courses()
    {
        return $this->hasMany(PlgCourse::class, 'category_id');
    }
    
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->title);
            }
        });
    }

    protected static function newFactory()
    {
        return PlgCategoriesFactory::new();
    }
} 