<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\PlgCourse;
use App\Models\PlgQuestion;
use App\Models\PlgTest;
use App\Models\SysUser;

class DashboardController extends Controller
{
    /**
     * Construtor que aplica middleware de autenticação
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Mostra o dashboard do administrador/teacher
     */
    public function index()
    {
        $user = Auth::user();
        
        // Buscar estatísticas para o dashboard
        $stats = $this->getDashboardStats($user);
        
        return view('panel.admin.dashboard', compact('user', 'stats'));
    }
    
    /**
     * Obtém estatísticas para o dashboard
     */
    private function getDashboardStats($user)
    {
        $query = PlgCourse::query();
        
        // Se não for super_admin, filtrar apenas cursos do professor
        if (!in_array($user->role, ['super_admin', 'admin'])) {
            $query->where('user_id', $user->id);
        }
        
        return [
            'total_courses' => $query->count(),
            'published_courses' => $query->where('status', 'published')->count(),
            'total_questions' => PlgQuestion::count(),
            'total_tests' => PlgTest::where('active', true)->count(),
            'recent_courses' => $query->latest()->take(5)->get(),
        ];
    }
}
