@extends('layouts.panel')

@section('title', 'Importar <PERSON>õ<PERSON>')
@section('page_title', 'Importar Questõ<PERSON>')

@push('styles')
<style>
    .preview-table {
        display: none;
    }
    .preview-table.active {
        display: block;
    }
    .editable-cell {
        min-width: 200px;
        padding: 5px;
        border: 1px solid transparent;
    }
    .editable-cell:hover {
        border: 1px dashed #aaa;
        cursor: text;
    }
    .editable-cell.editing {
        border: 1px solid #4f46e5;
        padding: 0;
    }
    .editable-cell.editing textarea {
        width: 100%;
        min-height: 60px;
        padding: 5px;
        border: none;
        outline: none;
        resize: vertical;
    }


</style>
@endpush

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Importar Questões',
            'description' => 'Importe questões a partir de um arquivo CSV.',
            'actions' => [
                [
                    'route' => route('admin.questions.index'),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ]
            ]
        ])

    <div class="p-6">
            @includeIf('panel.admin.includes.alerts')

        <div id="upload-form" class="{{ session()->has('csv_preview_data') ? 'hidden' : 'block' }}">
                <form action="{{ route('admin.questions.previewCsv') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf
                
                    <div>
                        <label for="category_select" class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">
                            Categoria <span class="text-red-500">*</span>
                    </label>
                    <select name="category_id" 
                            id="category_select" 
                                class="w-full rounded-md border-0 py-2.5 px-3 bg-white dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100 shadow-sm ring-1 ring-inset ring-zinc-300 dark:ring-zinc-700 focus:ring-2 focus:ring-inset focus:ring-trends-primary sm:text-sm sm:leading-6"
                            required>
                        <option value="">Selecione uma categoria</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" 
                                {{ old('category_id', $course->category_id ?? '') == $category->id ? 'selected' : '' }}>
                                {{ $category->title }}
                            </option>
                        @endforeach
                    </select>
                </div>

                    <div>
                        <label for="csv_file" class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">
                            Arquivo CSV <span class="text-red-500">*</span>
                        </label>
                        <input type="file" 
                               name="csv_file" 
                               id="csv_file" 
                               required 
                               class="block w-full text-sm text-zinc-900 dark:text-zinc-100 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-trends-primary file:text-white hover:file:bg-trends-primary/90 border border-zinc-300 dark:border-zinc-700 rounded-md">
                        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
                            O arquivo CSV deve ter o formato: Pergunta, Resposta1, Resposta2, Resposta3, Resposta4, Resposta5, RespCorreta, Explicação, Grátis
                        </p>
                </div>

                    <div class="flex items-center justify-end gap-4">
                    <a href="{{ route('admin.questions.index') }}" 
                           class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                        Cancelar
                    </a>
                        <button type="submit" 
                                class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-trends-primary border border-transparent rounded-md hover:bg-trends-primary/90 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                        <i class="fas fa-eye mr-2"></i>
                        Visualizar Prévia
                    </button>
                </div>        
            </form>
        </div>

        <div id="preview-form" class="{{ session()->has('csv_preview_data') ? 'block' : 'hidden' }}">
            @if(session()->has('csv_preview_data'))
                    <div class="space-y-6">
                        <div class="flex justify-between items-center">
                        <div>
                                <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">Prévia das Questões</h3>
                                <p class="text-sm text-zinc-500 dark:text-zinc-400 mt-1">
                                    Revise as questões abaixo antes de importar. Clique em qualquer texto para editar.
                                </p>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="flex items-center gap-2">
                                    <button type="button" id="view-mode-grid" class="p-2 rounded-md text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700">
                                        <i class="fas fa-grip-horizontal"></i>
                                    </button>
                                    <button type="button" id="view-mode-list" class="p-2 rounded-md text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                                <button id="btn-back-to-upload" 
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Voltar
                            </button>
                        </div>
                    </div>

                    <form action="{{ route('admin.questions.importCsv') }}" method="POST" id="finalImportForm">
                        @csrf
                        <input type="hidden" name="category_id" value="{{ session('category_id') }}">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="questions-grid">
                                    @foreach(session('csv_preview_data') as $index => $row)
                                <div class="bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 rounded-lg shadow-sm relative question-row group" data-row="{{ $index }}">
                                    <!-- Cabeçalho do Card -->
                                    <div class="p-4 border-b border-zinc-200 dark:border-zinc-700 flex justify-between items-center">
                                        <span class="inline-flex items-center justify-center px-3 py-1 rounded-full text-sm font-medium bg-zinc-100 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100">
                                            Questão {{ $index + 1 }}
                                        </span>
                                        <div class="flex items-center gap-2">
                                            <button type="button" class="text-zinc-400 hover:text-zinc-600 dark:text-zinc-500 dark:hover:text-zinc-300 expand-question" data-row="{{ $index }}">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <button type="button" class="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-400 delete-row-btn" data-row="{{ $index }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Conteúdo do Card -->
                                    <div class="p-4 space-y-4">
                                        <!-- Pergunta -->
                                        <div>
                                            <label class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">Pergunta</label>
                                            <div class="editable-cell p-3 bg-zinc-50 dark:bg-zinc-800 rounded-lg text-sm line-clamp-3 hover:line-clamp-none transition-all duration-200" data-field="question" data-index="{{ $index }}">
                                                    {{ $row[0] }}
                                                </div>
                                                <input type="hidden" name="data[{{ $index }}][0]" value="{{ $row[0] }}">
                                        </div>

                                        <!-- Alternativas -->
                                        <div>
                                            <label class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">Alternativas</label>
                                            <div class="space-y-2">
                                            @for($i = 1; $i <= 5; $i++)
                                                    <div class="flex items-start gap-2">
                                                        <div class="flex-none">
                                                            <div class="inline-flex items-center justify-center w-6 h-6 rounded-full {{ (int)$row[6] === $i ? 'bg-green-500 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' }} text-sm">
                                                                {{ $i }}
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow">
                                                            <div class="editable-cell p-2 bg-zinc-50 dark:bg-zinc-800 rounded-lg text-sm line-clamp-2 hover:line-clamp-none transition-all duration-200" data-field="answer{{ $i }}" data-index="{{ $index }}">
                                                        {{ $row[$i] }}
                                                            </div>
                                                            <input type="hidden" name="data[{{ $index }}][{{ $i }}]" value="{{ $row[$i] }}">
                                                        </div>
                                                    </div>
                                            @endfor
                                            </div>
                                        </div>

                                        <!-- Controles -->
                                        <div class="grid grid-cols-2 gap-3">
                                            <div>
                                                <label class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">Resposta</label>
                                                <select name="data[{{ $index }}][6]" class="correct-answer-select w-full rounded-md border-0 py-1.5 px-2.5 bg-white dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100 shadow-sm ring-1 ring-inset ring-zinc-300 dark:ring-zinc-700 focus:ring-2 focus:ring-inset focus:ring-trends-primary text-sm">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <option value="{{ $i }}" {{ (int)$row[6] === $i ? 'selected' : '' }}>
                                                            Alt. {{ $i }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                </div>
                                            <div>
                                                <label class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">Gratuita</label>
                                                <select name="data[{{ $index }}][8]" class="free-select w-full rounded-md border-0 py-1.5 px-2.5 bg-white dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100 shadow-sm ring-1 ring-inset ring-zinc-300 dark:ring-zinc-700 focus:ring-2 focus:ring-inset focus:ring-trends-primary text-sm">
                                                    <option value="0" {{ !($row[8] ?? false) ? 'selected' : '' }}>Não</option>
                                                    <option value="1" {{ ($row[8] ?? false) ? 'selected' : '' }}>Sim</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Explicação (Colapsada por padrão) -->
                                        <div class="explanation-section hidden">
                                            <label class="block text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">Explicação</label>
                                            <div class="editable-cell p-3 bg-zinc-50 dark:bg-zinc-800 rounded-lg text-sm" data-field="explanation" data-index="{{ $index }}">
                                                {{ $row[7] ?? '' }}
                                            </div>
                                            <input type="hidden" name="data[{{ $index }}][7]" value="{{ $row[7] ?? '' }}">
                                        </div>
                                    </div>
                                </div>
                                    @endforeach
                        </div>
                        
                        <div class="flex justify-end space-x-4 mt-6">
                                <button type="button" id="btn-cancel-import" 
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                                Cancelar
                            </button>
                                <button type="submit" 
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-trends-primary border border-transparent rounded-md hover:bg-trends-primary/90 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                                <i class="fas fa-file-import mr-2"></i>
                                Importar {{ count(session('csv_preview_data')) }} Questões
                            </button>
                        </div>
                    </form>
                </div>
            @endif
        </div>

        @if(session()->has('imported_questions') && count(session('imported_questions')) > 0)
                <div class="mt-8 space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">Questões Importadas</h3>
                        <p class="text-sm text-zinc-500 dark:text-zinc-400 mt-1">
                            Lista de questões que foram importadas com sucesso.
                        </p>
                    </div>
                
                    <div class="overflow-x-auto border border-zinc-200 dark:border-zinc-700 rounded-lg">
                        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                            <thead class="bg-zinc-50 dark:bg-zinc-800">
                            <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                    Pergunta
                                </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                    Alternativas
                                </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                    Gratuita
                                </th>
                            </tr>
                        </thead>
                            <tbody class="bg-white dark:bg-zinc-900 divide-y divide-zinc-200 dark:divide-zinc-700">
                            @foreach(session('imported_questions') as $importedItem)
                                <tr>
                                        <td class="px-6 py-4 whitespace-normal text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $importedItem['question']->question }}
                                    </td>
                                        <td class="px-6 py-4 whitespace-normal text-sm text-zinc-500 dark:text-zinc-400">
                                        <ul class="list-disc pl-5">
                                            @foreach($importedItem['answers'] as $answer)
                                                <li class="{{ $answer->correct ? 'font-bold text-green-600 dark:text-green-400' : '' }}">
                                                    {{ $answer->answer }}
                                                    @if($answer->correct && $answer->explanation)
                                                            <div class="mt-1 text-sm text-zinc-600 dark:text-zinc-400 italic">
                                                            <strong>Explicação:</strong> {{ $answer->explanation }}
                                                        </div>
                                                    @endif
                                                </li>
                                            @endforeach
                                        </ul>
                                    </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-zinc-500 dark:text-zinc-400">
                                        {{ $importedItem['question']->free ? 'Sim' : 'Não' }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Aguardar o Choices estar disponível
        function initCategoryChoices() {
            if (typeof window.Choices === 'undefined') {
                setTimeout(initCategoryChoices, 100);
                return;
            }

            // Inicializar Choices.js para categoria
            const categorySelect = document.getElementById('category_select');
            if (categorySelect && !categorySelect.choicesInstance) {
                const choices = new window.Choices(categorySelect, {
                    searchEnabled: true,
                    searchChoices: true,
                    searchPlaceholderValue: 'Digite para buscar...',
                    noResultsText: 'Nenhum resultado encontrado',
                    noChoicesText: 'Nenhuma opção disponível',
                    itemSelectText: 'Clique para selecionar',
                    placeholder: true,
                    placeholderValue: 'Selecione uma categoria',
                    removeItemButton: true,
                    shouldSort: false,
                    allowHTML: true
                });
                categorySelect.choicesInstance = choices;
            }
        }

        initCategoryChoices();
        
        // Botões para voltar ao formulário de upload
        const backToUploadBtn = document.getElementById('btn-back-to-upload');
        const cancelImportBtn = document.getElementById('btn-cancel-import');
        const questionsGrid = document.getElementById('questions-grid');
        const viewModeGrid = document.getElementById('view-mode-grid');
        const viewModeList = document.getElementById('view-mode-list');
        
        if (backToUploadBtn) {
            backToUploadBtn.addEventListener('click', function() {
                document.getElementById('upload-form').classList.remove('hidden');
                document.getElementById('preview-form').classList.add('hidden');
            });
        }
        
        if (cancelImportBtn) {
            cancelImportBtn.addEventListener('click', function() {
                document.getElementById('upload-form').classList.remove('hidden');
                document.getElementById('preview-form').classList.add('hidden');
            });
        }

        // Alternar modo de visualização
        viewModeGrid.addEventListener('click', function() {
            questionsGrid.classList.remove('grid-cols-1');
            questionsGrid.classList.add('md:grid-cols-2', 'lg:grid-cols-3');
            viewModeGrid.classList.add('bg-zinc-100', 'dark:bg-zinc-700');
            viewModeList.classList.remove('bg-zinc-100', 'dark:bg-zinc-700');
        });

        viewModeList.addEventListener('click', function() {
            questionsGrid.classList.add('grid-cols-1');
            questionsGrid.classList.remove('md:grid-cols-2', 'lg:grid-cols-3');
            viewModeList.classList.add('bg-zinc-100', 'dark:bg-zinc-700');
            viewModeGrid.classList.remove('bg-zinc-100', 'dark:bg-zinc-700');
        });

        // Expandir/Colapsar explicação
        document.querySelectorAll('.expand-question').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.question-row');
                const explanationSection = card.querySelector('.explanation-section');
                explanationSection.classList.toggle('hidden');
                
                // Altera o ícone
                const icon = this.querySelector('i');
                if (explanationSection.classList.contains('hidden')) {
                    icon.classList.remove('fa-compress');
                    icon.classList.add('fa-expand');
                } else {
                    icon.classList.remove('fa-expand');
                    icon.classList.add('fa-compress');
                }
            });
        });
        
        // Edição in-line
        const editableCells = document.querySelectorAll('.editable-cell');
        
        editableCells.forEach(cell => {
            cell.addEventListener('click', function() {
                // Verifica se já está em modo de edição
                if (this.classList.contains('editing')) return;
                
                const currentText = this.innerText.trim();
                const dataField = this.getAttribute('data-field');
                const dataIndex = this.getAttribute('data-index');
                
                // Remove classes de truncamento
                this.classList.remove('line-clamp-2', 'line-clamp-3');
                
                // Criar textarea
                this.classList.add('editing');
                const textarea = document.createElement('textarea');
                textarea.value = currentText;
                textarea.classList.add('w-full', 'min-h-[60px]', 'p-2', 'rounded-lg', 'border-0', 'focus:ring-2', 'focus:ring-trends-primary', 'bg-white', 'dark:bg-zinc-900', 'text-sm');
                
                textarea.addEventListener('blur', function() {
                    const newText = textarea.value.trim();
                    cell.innerText = newText;
                    cell.classList.remove('editing');
                    
                    // Restaura classes de truncamento
                    if (dataField === 'question') {
                        cell.classList.add('line-clamp-3');
                    } else if (dataField.startsWith('answer')) {
                        cell.classList.add('line-clamp-2');
                    }
                    
                    // Atualiza o valor do campo hidden
                    const hiddenInput = document.querySelector(`input[name="data[${dataIndex}][${getFieldIndex(dataField)}]"]`);
                    if (hiddenInput) {
                        hiddenInput.value = newText;
                    }
                });
                
                this.innerHTML = '';
                this.appendChild(textarea);
                textarea.focus();
            });
        });
        
        // Função para mapear o nome do campo para o índice no array
        function getFieldIndex(field) {
            switch(field) {
                case 'question': return 0;
                case 'answer1': return 1;
                case 'answer2': return 2;
                case 'answer3': return 3;
                case 'answer4': return 4;
                case 'answer5': return 5;
                case 'explanation': return 7;
                default: return 0;
            }
        }
        
        // Botões de excluir linha
        const deleteButtons = document.querySelectorAll('.delete-row-btn');
        deleteButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('.question-row');
                    if (row) {
                        row.remove();
                    
                    // Atualiza o contador no botão de importar
                    const remainingRows = document.querySelectorAll('.question-row').length;
                    const submitButton = document.querySelector('#finalImportForm button[type="submit"]');
                    if (submitButton) {
                        submitButton.innerHTML = `<i class="fas fa-file-import mr-2"></i>Importar ${remainingRows} Questões`;
                    }
                }
            });
        });
    });
</script>
@endpush