
document.addEventListener('DOMContentLoaded', function() {
    // ====================================================
    //                  CONFIGURAÇÃO E ESTADO
    // ====================================================

    const QuestionManager = {
        // Cache de elementos DOM
        elements: {
            questionInput: document.getElementById('question'),
            questionTypeSelect: document.getElementById('question_type'),
            answerFormatSelect: document.getElementById('answer_format'),
            answersContainer: document.getElementById('answers-container'),
            addAnswerButton: document.getElementById('add-answer'),
            answersSection: document.getElementById('answers-section'),
            essaySection: document.getElementById('essay-answer-section'),
            previewQuestionText: document.getElementById('preview-question-text'),
            previewAnswersContainer: document.getElementById('preview-answers-container'),
            previewEssayContainer: document.getElementById('preview-essay-container'),
            form: document.getElementById('questionForm')
        },

        // Estado da aplicação
        state: {
            previousQuestionType: null,
            isEditing: () => !!document.querySelector('input[name="_method"][value="PUT"]'),
            answerTemplate: document.getElementById('answer-template')
        },

        // Configurações
        config: {
            minAnswers: 2,
            markers: {
                numeric: (i) => `${i + 1})`,
                alpha: (i) => `${String.fromCharCode(65 + i)})`,
                roman: (i) => {
                    const romans = ['i', 'ii', 'iii', 'iv', 'v', 'vi', 'vii', 'viii', 'ix', 'x'];
                    return `${romans[i] || (i + 1)}.`;
                }
            }
        },

        // ====================================================
        //                  UTILITÁRIOS DOM
        // ====================================================

        // Criar elemento com atributos
        createElement(tag, attributes = {}, content = '') {
            const element = document.createElement(tag);
            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'className') element.className = value;
                else element.setAttribute(key, value);
            });
            if (content) element.innerHTML = content;
            return element;
        },

        // Buscar elementos com cache
        $(selector, context = document) {
            return context.querySelector(selector);
        },

        $$(selector, context = document) {
            return Array.from(context.querySelectorAll(selector));
        },

        // ====================================================
        //                  GERENCIAMENTO DE ALTERNATIVAS
        // ====================================================

        AnswerManager: {
            // Adicionar nova alternativa
            add() {
                if (!QuestionManager.state.answerTemplate) return;

                const answerNumber = this.getNextNumber();
                const answerItem = this.createAnswerItem(answerNumber);

                QuestionManager.elements.answersContainer.appendChild(answerItem);
                QuestionManager.QuestionType.updateAnswerInputTypes();
                QuestionManager.Preview.update();
            },

            // Remover alternativa
            remove(answerItem) {
                const allItems = QuestionManager.$$('.answer-item');
                if (allItems.length <= QuestionManager.config.minAnswers) {
                    alert('É necessário ter pelo menos 2 alternativas.');
                    return;
                }
                answerItem.remove();
                this.renumberAll();
                QuestionManager.Preview.update();
            },

            // Criar item de alternativa
            createAnswerItem(answerNumber) {
                const template = QuestionManager.state.answerTemplate.content.cloneNode(true);
                const answerItem = template.querySelector('.answer-item');
                const index = answerNumber - 1;

                // Configurar todos os elementos de uma vez
                this.setupAnswerElements(answerItem, answerNumber, index);
                return template;
            },

            // Configurar elementos da alternativa
            setupAnswerElements(answerItem, answerNumber, index) {
                const configs = [
                    { selector: '.answer-text', props: { name: `answers[${index}][answer]` }},
                    { selector: '.answer-image-input', props: { name: `answers[${index}][image]`, id: `answer_image_${answerNumber}` }},
                    { selector: '.answer-image-caption-input', props: { name: `answers[${index}][caption]`, id: `answer_caption_${answerNumber}` }},
                    { selector: '.answer-image-button', props: { 'data-target': `answer_image_${answerNumber}` }},
                    { selector: '.media-preview', props: { id: `answer_image_${answerNumber}-preview` }},
                    { selector: '.answer-radio', props: { value: answerNumber }},
                    { selector: '.answer-checkbox', props: { value: answerNumber }},
                    { selector: '.answer-label', content: `Alternativa ${answerNumber}` }
                ];

                answerItem.setAttribute('data-answer-index', index);

                configs.forEach(({ selector, props, content }) => {
                    const element = answerItem.querySelector(selector);
                    if (element) {
                        if (props) Object.entries(props).forEach(([key, value]) => element.setAttribute(key, value));
                        if (content) element.textContent = content;
                    }
                });
            },

            // Renumerar todas as alternativas
            renumberAll() {
                QuestionManager.$$('.answer-item').forEach((item, index) => {
                    this.setupAnswerElements(item, index + 1, index);
                });
            },

            // Obter próximo número
            getNextNumber() {
                return QuestionManager.$$('.answer-item').length + 1;
            }
        },

        // ====================================================
        //                  TIPOS DE QUESTÃO
        // ====================================================

        QuestionType: {
            typeNames: {
                'single': 'Escolha Única',
                'multiple': 'Múltipla Escolha',
                'essay': 'Dissertativa'
            },

            // Atualizar tipo de questão
            update() {
                const newType = QuestionManager.elements.questionTypeSelect.value;
                const oldType = QuestionManager.state.previousQuestionType;

                if (oldType === null) {
                    QuestionManager.state.previousQuestionType = newType;
                    this.apply(newType);
                    return;
                }

                if (newType === oldType) return;

                if (QuestionManager.Explanations.hasExisting()) {
                    this.showChangeConfirmation(oldType, newType);
                } else {
                    this.apply(newType);
                    QuestionManager.state.previousQuestionType = newType;
                }
            },

            // Aplicar mudança de tipo
            apply(type) {
                const { answersSection, essaySection } = QuestionManager.elements;

                if (type === 'essay') {
                    answersSection.style.display = 'none';
                    essaySection.style.display = 'block';
                } else {
                    answersSection.style.display = 'block';
                    essaySection.style.display = 'none';
                    this.updateAnswerInputTypes();
                }
                QuestionManager.Preview.update();
            },

            // Mostrar confirmação de mudança
            showChangeConfirmation(oldType, newType) {
                const message = `Ao mudar de "${this.typeNames[oldType]}" para "${this.typeNames[newType]}", todas as explicações existentes serão excluídas. Deseja continuar?`;

                if (confirm(message)) {
                    QuestionManager.Explanations.clearAll();
                    this.apply(newType);
                    QuestionManager.state.previousQuestionType = newType;
                } else {
                    QuestionManager.elements.questionTypeSelect.value = oldType;
                }
            },

            // Atualizar tipos de input (radio/checkbox)
            updateAnswerInputTypes() {
                const type = QuestionManager.elements.questionTypeSelect.value;
                const radios = QuestionManager.$$('.answer-radio');
                const checkboxes = QuestionManager.$$('.answer-checkbox');

                radios.forEach(radio => radio.classList.toggle('hidden', type !== 'single'));
                checkboxes.forEach(checkbox => checkbox.classList.toggle('hidden', type !== 'multiple'));
            }
        },

        // ====================================================
        //                  SISTEMA DE EXPLICAÇÕES
        // ====================================================

        Explanations: {
            // Toggle explicação
            toggle(input) {
                const answerItem = input.closest('.answer-item');
                if (!answerItem) return;

                if (input.checked) {
                    this.show(answerItem);
                    if (input.type === 'radio') this.hideOthers(answerItem);
                } else {
                    this.hide(answerItem);
                }
            },

            // Mostrar campo de explicação
            show(answerItem) {
                let field = answerItem.querySelector('.explanation-field');
                if (field) {
                    field.classList.remove('hidden');
                    return;
                }

                const answerNumber = answerItem.querySelector('.answer-radio, .answer-checkbox').value;
                const placeholder = answerItem.querySelector('.explanation-field-placeholder');
                if (!placeholder) return;

                field = this.createField(answerNumber);
                placeholder.replaceWith(field);

                // Restaurar valor preservado
                const preserved = this.getPreserved(answerNumber);
                if (preserved) this.setValue(field, preserved);

                setTimeout(() => QuestionManager.initializeQuillEditors(), 100);
            },

            // Criar campo de explicação
            createField(answerNumber) {
                return QuestionManager.createElement('div', {
                    className: 'explanation-field mt-2 ml-2 pl-4 border-l-2 border-trends-primary'
                }, `
                    <label class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1 block">
                        Explicação da alternativa
                    </label>
                    <div id="explanation-editor-${answerNumber}"
                         data-input="explanation-${answerNumber}"
                         data-answer="${answerNumber}"
                         class="explanation-textarea editor w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 mt-1"
                         data-placeholder="Digite a explicação para esta alternativa...">
                    </div>
                    <textarea name="explanations[${answerNumber}]"
                              id="explanation-${answerNumber}"
                              rows="6"
                              data-answer="${answerNumber}"
                              class="explanation-textarea hidden w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                              placeholder="Digite uma explicação para esta alternativa..."></textarea>
                `);
            },

            // Esconder campo
            hide(answerItem) {
                const field = answerItem.querySelector('.explanation-field');
                if (!field) return;

                if (!QuestionManager.state.isEditing()) {
                    const placeholder = QuestionManager.createElement('div', { className: 'explanation-field-placeholder' });
                    field.replaceWith(placeholder);
                } else {
                    field.classList.add('hidden');
                }
            },

            // Esconder outras explicações (para radio)
            hideOthers(currentItem) {
                QuestionManager.$$('.answer-item').forEach(item => {
                    if (item === currentItem) return;

                    const field = item.querySelector('.explanation-field');
                    if (field) {
                        const content = this.getContent(field);
                        if (content.hasContent) {
                            this.preserve(content.answerNumber, content.value);
                        }
                        this.hide(item);
                    }
                });
            },

            // Obter conteúdo do campo
            getContent(field) {
                const quillDiv = field.querySelector('div.explanation-textarea');
                const textarea = field.querySelector('textarea.explanation-textarea');

                if (quillDiv) {
                    const content = quillDiv.innerHTML.trim();
                    if (content && content !== '<p><br></p>') {
                        return {
                            hasContent: true,
                            answerNumber: quillDiv.getAttribute('data-answer'),
                            value: content
                        };
                    }
                }

                if (textarea && textarea.value.trim()) {
                    return {
                        hasContent: true,
                        answerNumber: textarea.getAttribute('data-answer'),
                        value: textarea.value.trim()
                    };
                }

                return { hasContent: false };
            },

            // Preservar explicação
            preserve(answerNumber, value) {
                let input = QuestionManager.$(`input.explanation-preserved[data-answer="${answerNumber}"]`);
                if (!input) {
                    input = QuestionManager.createElement('input', {
                        type: 'hidden',
                        className: 'explanation-preserved',
                        'data-answer': answerNumber
                    });
                    document.body.appendChild(input);
                }
                input.value = value;
            },

            // Obter explicação preservada
            getPreserved(answerNumber) {
                const input = QuestionManager.$(`input.explanation-preserved[data-answer="${answerNumber}"], input.explanation-hidden-value[data-answer="${answerNumber}"]`);
                return input ? input.value : '';
            },

            // Definir valor no campo
            setValue(field, value) {
                const quillDiv = field.querySelector('div.explanation-textarea');
                const textarea = field.querySelector('textarea.explanation-textarea');
                if (quillDiv) quillDiv.innerHTML = value;
                if (textarea) textarea.value = value;
            },

            // Verificar se há explicações existentes
            hasExisting() {
                return QuestionManager.$$('.explanation-field:not(.hidden)').some(field => {
                    const content = this.getContent(field);
                    return content.hasContent;
                });
            },

            // Limpar todas as explicações
            clearAll() {
                QuestionManager.$$('.explanation-field').forEach(field => {
                    const placeholder = QuestionManager.createElement('div', { className: 'explanation-field-placeholder' });
                    field.replaceWith(placeholder);
                });

                QuestionManager.$$('input.explanation-preserved').forEach(input => input.remove());
                QuestionManager.$$('.answer-radio, .answer-checkbox').forEach(input => input.checked = false);
            }
        },

        // ====================================================
        //                  SISTEMA DE PREVIEW
        // ====================================================

        Preview: {
            // Atualizar todo o preview
            update() {
                this.updateQuestion();
                this.updateAnswers();
            },

            // Atualizar preview da questão
            updateQuestion() {
                const { previewQuestionText, questionInput } = QuestionManager.elements;
                if (!previewQuestionText) return;

                const text = questionInput.value || 'Digite a questão para visualizar o preview...';

                if (text.includes('<') && text.includes('>')) {
                    previewQuestionText.innerHTML = text;
                } else {
                    previewQuestionText.textContent = text;
                }
            },

            // Atualizar preview das alternativas
            updateAnswers() {
                const { previewAnswersContainer, previewEssayContainer, questionTypeSelect, answerFormatSelect } = QuestionManager.elements;
                if (!previewAnswersContainer) return;

                const type = questionTypeSelect.value;
                const format = answerFormatSelect.value;

                if (type === 'essay') {
                    previewAnswersContainer.classList.add('hidden');
                    previewEssayContainer?.classList.remove('hidden');
                    return;
                }

                previewAnswersContainer.classList.remove('hidden');
                previewEssayContainer?.classList.add('hidden');

                const items = QuestionManager.$$('.answer-item');
                const previewItems = items.map((item, index) => this.createAnswerItem(item, index, type, format)).filter(Boolean);

                previewAnswersContainer.innerHTML = previewItems.length
                    ? previewItems.map(item => item.outerHTML).join('')
                    : '<p class="text-zinc-500">Adicione alternativas para ver o preview</p>';
            },

            // Criar item de preview
            createAnswerItem(item, index, type, format) {
                const text = item.querySelector('.answer-text').value;
                const isChecked = item.querySelector('.answer-radio:checked, .answer-checkbox:checked');
                const imageUrl = item.querySelector('.media-preview img')?.src;

                if (!text && !imageUrl) return null;

                const marker = QuestionManager.config.markers[format]?.(index) || '';
                const inputType = type === 'single' ? 'rounded-full' : 'rounded';
                const borderColor = isChecked ? 'border-trends-primary bg-trends-primary/20' : 'border-zinc-300';

                return QuestionManager.createElement('div', {
                    className: `question-option ${isChecked ? 'selected' : ''}`
                }, `
                    <div class="flex-shrink-0">
                        <div class="h-5 w-5 border-2 ${inputType} ${borderColor}"></div>
                    </div>
                    <div class="flex-1">
                        ${marker ? `<span class="mr-2 text-zinc-700 dark:text-zinc-300">${marker}</span>` : ''}
                        ${text ? `<div class="ml-3">${text}</div>` : ''}
                        ${imageUrl ? `<div class="${text ? 'mt-2' : 'ml-3'}"><img src="${imageUrl}" alt="Imagem da alternativa" class="max-h-32 rounded-md"></div>` : ''}
                    </div>
                `);
            }
        },

        // ====================================================
        //                  VALIDAÇÃO E LIMPEZA
        // ====================================================

        Validator: {
            // Validar formulário principal
            validate(e) {
                const type = QuestionManager.elements.questionTypeSelect.value;

                if (!this.validateQuestion(e)) return false;
                if (!this.validateCategories(e)) return false;

                return type === 'essay' ? this.validateEssay(e) : this.validateAnswers(e, type);
            },

            // Validar questão
            validateQuestion(e) {
                if (!QuestionManager.elements.questionInput.value.trim()) {
                    e.preventDefault();
                    alert('Por favor, digite o enunciado da questão.');
                    return false;
                }
                return true;
            },

            // Validar categorias
            validateCategories(e) {
                const selected = QuestionManager.$$('#categories_select option:checked');
                if (selected.length === 0) {
                    e.preventDefault();
                    alert('Por favor, selecione pelo menos uma categoria.');
                    return false;
                }
                return true;
            },

            // Validar questão dissertativa
            validateEssay(e) {
                const essayAnswer = QuestionManager.$('#essay_answer');
                if (!essayAnswer?.value.trim()) {
                    e.preventDefault();
                    alert('Por favor, insira uma resposta esperada para a questão dissertativa.');
                    return false;
                }
                return true;
            },

            // Validar alternativas
            validateAnswers(e, type) {
                const items = QuestionManager.$$('.answer-item');

                if (items.length < QuestionManager.config.minAnswers) {
                    e.preventDefault();
                    alert('É necessário ter pelo menos 2 alternativas.');
                    return false;
                }

                const selector = type === 'single' ? '.answer-radio:checked' : '.answer-checkbox:checked';
                if (!QuestionManager.$(selector)) {
                    e.preventDefault();
                    const message = type === 'single'
                        ? 'Por favor, selecione uma alternativa correta.'
                        : 'Por favor, selecione pelo menos uma alternativa correta.';
                    alert(message);
                    return false;
                }

                const emptyAnswers = QuestionManager.$$('.answer-text').filter(input => !input.value.trim());
                if (emptyAnswers.length > 0) {
                    e.preventDefault();
                    alert('Por favor, preencha o texto de todas as alternativas.');
                    return false;
                }

                return true;
            },

            // Limpar formulário antes do submit
            cleanup() {
                const type = QuestionManager.elements.questionTypeSelect.value;
                const cleanupRules = [
                    // Explicações vazias ou não relacionadas a corretas
                    {
                        selector: 'textarea[name^="explanations["], input[name^="explanations["]',
                        condition: (field) => {
                            const answerNumber = field.getAttribute('data-answer') || field.name.match(/\[(\d+)\]/)?.[1];
                            if (!answerNumber) return true;
                            const isCorrect = QuestionManager.$(`input[name="correct_answer"][value="${answerNumber}"]:checked, input[name="correct_answers[]"][value="${answerNumber}"]:checked`);
                            return !isCorrect || !field.value?.trim();
                        }
                    },
                    // Campos genéricos vazios
                    {
                        selector: 'input[name="explanation"], textarea[name="explanation"]',
                        condition: (field) => !field.value?.trim()
                    },
                    // Essay answer para não-dissertativas
                    {
                        selector: 'input[name="essay_answer"], textarea[name="essay_answer"]',
                        condition: () => type !== 'essay'
                    },
                    // Correct answer para múltipla escolha
                    {
                        selector: 'input[name="correct_answer"]',
                        condition: () => type === 'multiple'
                    },
                    // Correct answers para única escolha
                    {
                        selector: 'input[name="correct_answers[]"]',
                        condition: () => type === 'single'
                    },
                    // Campos de mídia vazios
                    {
                        selector: 'input[name*="[image]"], input[name*="[caption]"], input[name="category_id[]"], select[name="category_id[]"]',
                        condition: (field) => !field.value?.trim()
                    }
                ];

                cleanupRules.forEach(({ selector, condition }) => {
                    QuestionManager.$$(selector).forEach(field => {
                        if (condition(field)) field.remove();
                    });
                });

                // Limpeza especial para question_image
                const questionImage = QuestionManager.$('#question_image');
                if (questionImage && !questionImage.value?.trim()) {
                    questionImage.remove();
                }
            }
        },

        // ====================================================
        //                  GERENCIAMENTO DE MÍDIA
        // ====================================================

        MediaManager: {
            // Configurar listeners de mídia
            setupListeners() {
                document.addEventListener('mediaSelected', this.handleSelection.bind(this));
                window.addEventListener('mediaSelected', this.handleSelection.bind(this));

                const questionImageInput = QuestionManager.$('#question_image');
                if (questionImageInput) {
                    questionImageInput.addEventListener('mediaSelected', (e) => {
                        this.updateQuestionPreview(e.detail.media);
                    });
                }

                document.addEventListener('input', (e) => {
                    if (e.target.classList.contains('media-caption-input')) {
                        this.handleCaptionInput(e.target);
                    }
                });
            },

            // Manipular seleção de mídia
            handleSelection(e) {
                const { target, media } = e.detail || {};
                if (target?.startsWith('answer_image_')) {
                    this.updateAnswerPreview(target, media);
                    setTimeout(() => QuestionManager.Preview.update(), 100);
                }
            },

            // Atualizar preview da questão
            updateQuestionPreview(media) {
                const preview = QuestionManager.$('#question_image-preview');
                const input = QuestionManager.$('#question_image');
                if (!preview || !input) return;

                if (media?.url && media?.id) {
                    input.value = media.id;
                    const imageUrl = this.getOptimizedUrl(media, 'w=250&h=150&fit=crop&fm=webp');
                    const filename = media.filename || media.original_filename || 'Sem nome';

                    preview.innerHTML = this.createPreviewHTML(imageUrl, filename, 'h-32', 'Imagem da questão');


                } else {
                    input.value = '';
                    preview.innerHTML = '';
                }
            },

            // Atualizar preview da alternativa
            updateAnswerPreview(target, media) {
                const preview = QuestionManager.$(`#${target}-preview`);
                const input = QuestionManager.$(`#${target}`);
                if (!preview || !input) return;

                if (media?.url) {
                    const imageUrl = this.getOptimizedUrl(media, 'w=200&h=150&fit=crop&fm=webp');
                    const filename = media.filename || media.original_filename || 'Sem nome';

                    preview.innerHTML = this.createPreviewHTML(imageUrl, filename, 'h-24', 'Imagem da alternativa');
                } else {
                    preview.innerHTML = '';
                }
            },

            // Obter URL otimizada
            getOptimizedUrl(media, params) {
                let url = media.thumbnail_url || media.url;
                if (url && !url.includes('?w=') && !url.includes('&w=')) {
                    url += (url.includes('?') ? '&' : '?') + params;
                }
                return url;
            },

            // Criar HTML do preview
            createPreviewHTML(imageUrl, filename, heightClass, altText) {
                return `
                    <div class="relative">
                        <img src="${imageUrl}"
                             alt="${altText}"
                             class="w-full ${heightClass} object-cover rounded-lg"
                             onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\\'flex items-center justify-center w-full ${heightClass} bg-zinc-100 rounded-lg\\'><i class=\\'fas fa-image text-red-400 text-2xl\\'></i></div>';">
                        <button type="button"
                                class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600"
                                onclick="removeMediaPreview(this)">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                    <p class="text-sm text-zinc-500 mt-2 truncate">${filename}</p>
                `;
            },

            // Manipular input de legenda
            handleCaptionInput(input) {
                const answerItem = input.closest('.answer-item');
                const captionInput = answerItem?.querySelector('.answer-image-caption-input');
                if (captionInput) {
                    captionInput.value = input.value;
                }
            }
        },

        // ====================================================
        //                  INICIALIZAÇÃO E EVENTOS
        // ====================================================

        // Configurar event listeners
        setupEventListeners() {
            const { addAnswerButton, questionTypeSelect, answerFormatSelect, questionInput, form } = this.elements;

            // Event listeners principais
            addAnswerButton?.addEventListener('click', (e) => {
                e.preventDefault();
                this.AnswerManager.add();
            });

            // Delegação de eventos
            document.addEventListener('click', (e) => {
                if (e.target.closest('.remove-answer')) {
                    e.preventDefault();
                    const answerItem = e.target.closest('.answer-item');
                    if (answerItem) this.AnswerManager.remove(answerItem);
                }
            });

            document.addEventListener('change', (e) => {
                if (e.target.matches('.answer-radio, .answer-checkbox')) {
                    this.Explanations.toggle(e.target);
                    this.Preview.update();
                }
            });

            document.addEventListener('input', (e) => {
                if (e.target.matches('.answer-text')) {
                    this.Preview.update();
                }
            });

            // Event listeners específicos
            questionTypeSelect?.addEventListener('change', () => this.QuestionType.update());
            answerFormatSelect?.addEventListener('change', () => this.Preview.update());

            if (questionInput) {
                ['input', 'change'].forEach(event => {
                    questionInput.addEventListener(event, () => this.Preview.updateQuestion());
                });

                // Observer para mudanças no campo
                new MutationObserver(() => this.Preview.updateQuestion())
                    .observe(questionInput, { attributes: true, attributeFilter: ['value'] });
            }

            // Validação do formulário
            form?.addEventListener('submit', (e) => {
                this.Validator.cleanup();
                this.Validator.validate(e);
            });
        },

        // Inicializar Quill editors
        initializeQuillEditors() {
            if (typeof window.initializeQuillEditors === 'function') {
                window.initializeQuillEditors();
            }
        },

        // Inicializar estado
        initialize() {
            this.QuestionType.update();
            this.Preview.update();

            // Configurar explicações para modo edição
            setTimeout(() => {
                this.$$('.answer-radio:checked, .answer-checkbox:checked').forEach(input => {
                    const answerItem = input.closest('.answer-item');
                    if (answerItem) this.Explanations.show(answerItem);
                });
                this.initializeQuillEditors();
            }, 200);
        }
    };

    // ====================================================
    //                  FUNÇÕES GLOBAIS E INICIALIZAÇÃO
    // ====================================================

    // Expor funções globais necessárias
    window.updatePreview = () => QuestionManager.Preview.update();

    // Função global para remover preview de mídia
    window.removeMediaPreview = function(button) {
        const container = button.closest('.media-preview');
        const answerItem = button.closest('.answer-item');

        if (answerItem) {
            const imageInput = answerItem.querySelector('.answer-image-input');
            if (imageInput) imageInput.value = '';
        } else {
            const questionImage = QuestionManager.$('#question_image');
            if (questionImage) questionImage.value = '';
        }

        if (container) container.innerHTML = '';
    };

    // Inicializar tudo
    QuestionManager.setupEventListeners();
    QuestionManager.MediaManager.setupListeners();
    QuestionManager.initialize();
});