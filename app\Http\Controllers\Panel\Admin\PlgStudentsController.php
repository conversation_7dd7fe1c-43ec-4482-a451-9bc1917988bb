<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlgStudent;
use App\Models\PlgStudentAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Yajra\DataTables\Facades\DataTables;

class PlgStudentsController extends Controller
{
    public function index()
    {
        return view('panel.admin.students.index', [
            'columns' => $this->getColumns(),
            'id' => 'students-table',
            'ajaxUrl' => route('admin.students.data'),
            'filters' => $this->getFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    public function getData()
    {
        $user = Auth::user();

        $query = PlgStudent::select('plg_students.*')->orderBy('id', 'desc');

        // Filtro: professores veem apenas estudantes matriculados nos seus cursos/módulos
        if ($user->role === 'teacher') {
            $query->whereHas('enrollments', function($q) use ($user) {
                // Estudantes matriculados em módulos de cursos do professor
                $q->whereHas('module.course', function($subQ) use ($user) {
                    $subQ->where('user_id', $user->id);
                });
            });
        }
        // super_admin vê todos os estudantes
        return DataTables::of($query)
            ->editColumn('active', fn($student) =>
             view('panel.admin.students.partials.status', compact('student'))->render()
            )
            ->editColumn('birth_date', function ($student) {
                return $student->birth_date
                    ? \Carbon\Carbon::parse($student->birth_date)->format('d/m/Y')
                    : '-';
            })
            ->editColumn('phone1', function ($student) {
                $phones = [];
                if ($student->phone1) {
                    $phones[] = $student->phone1 . ($student->is_phone1_whatsapp ? ' (WhatsApp)' : '');
                }
                if ($student->phone2) {
                    $phones[] = $student->phone2 . ($student->is_phone2_whatsapp ? ' (WhatsApp)' : '');
                }
                return !empty($phones) ? implode('<br>', $phones) : '-';
            })
            ->addColumn('actions', fn($student) => view('panel.admin.includes.table-actions', [
                'actions' => [
                    ['type' => 'edit', 'route' => route('admin.students.edit', $student->id), 'title' => 'Editar estudante'],
                    ['type' => 'delete', 'route' => route('admin.students.destroy', $student->id), 'title' => 'Excluir estudante', 'confirm_message' => 'Tem certeza que deseja excluir este estudante?']
                ]
            ])->render())
            ->rawColumns(['actions', 'active', 'phone1'])
            ->make(true);
    }

    public function create()
    {
        return view('panel.admin.students.create');
    }

    public function store(Request $request)
    {
        $validated = $this->validateRequest($request);
        $validated['company_id'] = Auth::user()->company_id;
        $validated['password'] = Hash::make($validated['password']);

        // Cria o aluno
        $student = PlgStudent::create($validated);
        
    

         // Cria o endereço
        PlgStudentAddress::create([
            'company_id'  => Auth::user()->company_id,
            'student_id'  => $student->id,
            'country'     => $validated['country'] ?? 'Brasil',
            'cep'         => $validated['cep'] ?? null,
            'street'      => $validated['street'] ?? null,
            'number'      => $validated['number'] ?? null,
            'neighborhood'=> $validated['neighborhood'] ?? null,
            'city'        => $validated['city'] ?? null,
            'state'       => $validated['state'] ?? null,
        ]);



        return redirect()->route('admin.students.index')->with('success', 'Estudante criado com sucesso!');
    }

    public function edit($id)
    {
        $student = PlgStudent::with('address')->findOrFail($id);
        return view('panel.admin.students.create', compact('student'));
    }

    public function update(Request $request, $id)
    {
        $student = PlgStudent::findOrFail($id);
        $validated = $this->validateRequest($request, $id);

        if ($request->filled('password')) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $student->update($validated);



        // Atualiza ou cria o endereço relacionado
        $student->address()->updateOrCreate(
            ['student_id' => $student->id], // Condição para encontrar o endereço existente
            [
                'company_id' => auth()->user()->company_id,
                'cep' => $request->input('cep'),
                'street' => $request->input('street'),
                'number' => $request->input('number'),
                'neighborhood' => $request->input('neighborhood'),
                'city' => $request->input('city'),
                'state' => $request->input('state')
            ]
        );




        return redirect()->route('admin.students.index')->with('success', 'Estudante atualizado com sucesso!');
    }

    public function destroy($id)
    {
        $student = PlgStudent::findOrFail($id);
        $student->delete();

        return redirect()->route('admin.students.index')->with('success', 'Estudante deletado com sucesso!');
    }

    private function getColumns()
    {
        return [
            ['data' => 'id', 'name' => 'plg_students.id', 'label' => 'ID'],
            ['data' => 'name', 'name' => 'plg_students.name', 'label' => 'Nome'],
            ['data' => 'email', 'name' => 'plg_students.email', 'label' => 'Email'],
            ['data' => 'phone1', 'name' => 'plg_students.phone1', 'label' => 'Telefone'],
            ['data' => 'birth_date', 'name' => 'plg_students.birth_date', 'label' => 'Nascimento'],
            ['data' => 'active', 'name' => 'plg_students.active', 'label' => 'Status'],
            ['data' => 'actions', 'name' => 'actions', 'label' => 'Ações', 'orderable' => false, 'searchable' => false],
        ];
    }

    private function getFilters()
    {
        return [
            ['label' => 'Status', 'column' => 'active', 'options' => [
                ['value' => '1', 'label' => 'Ativo'],
                ['value' => '0', 'label' => 'Inativo'],
            ]],
        ];
    }

    private function validateRequest(Request $request, $id = null)
    {
        return $request->validate([
            'name' => 'required|string|max:255',
            'apelido' => 'nullable|string|max:255',
            'email' => 'required|email|unique:plg_students,email,' . $id,
            'password' => $id ? 'nullable|min:6' : 'required|min:6',
            'document' => 'nullable|string|max:20',
            'crm' => 'nullable|string|max:20',
            'phone1' => 'nullable|string|max:20',
            'is_phone1_whatsapp' => 'boolean',
            'phone2' => 'nullable|string|max:20',
            'is_phone2_whatsapp' => 'boolean',
            'birth_date' => 'nullable|date',
            'registration_status' => 'nullable|in:pending,approved,rejected,blocked',

            // Endereço
            'country' => 'nullable|string|max:50',
            'cep' => 'nullable|string|max:10',
            'street' => 'nullable|string|max:100',
            'number' => 'nullable|string|max:20',
            'neighborhood' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:50',
            'complement' => 'nullable|string|max:100',
            'is_primary' => 'boolean',
        ]);
    }

}
