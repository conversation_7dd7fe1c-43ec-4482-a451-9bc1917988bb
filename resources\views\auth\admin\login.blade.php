<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Login - Administrador | Trends Quiz</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex flex-col md:flex-row">
        <!-- Forms Section -->
        <div class="w-full md:w-1/2 flex items-center justify-center p-4 md:p-8 bg-white">
            <div class="w-full max-w-[440px] px-4 md:px-0 py-8">
                <!-- Logo -->
                <div class="flex items-center gap-2 mb-8 justify-center md:justify-start">
                    <i class="fas fa-chalkboard-teacher text-2xl text-blue-600"></i>
                    <span class="text-xl font-semibold">Trends Quiz</span>
                </div>

                <!-- Login Form -->
                <div id="loginForm" class="form-section active">
                    <h1 class="text-2xl font-semibold mb-6 text-center md:text-left">Painel Administrativo</h1>

                    @if(session('status'))
                        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                            {{ session('status') }}
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            <ul class="list-disc list-inside">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.login.post') }}" class="space-y-5">
                        @csrf
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="far fa-envelope text-gray-400"></i>
                                </div>
                                <input id="email" name="email" type="email" value="{{ old('email') }}" required 
                                    class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm text-sm
                                    focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Digite seu email">
                            </div>
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Senha</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input id="password" name="password" type="password" required 
                                    class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg shadow-sm text-sm
                                    focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Digite sua senha">
                                <button type="button" class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="far fa-eye text-gray-400"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input id="remember" name="remember" type="checkbox" 
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="remember" class="ml-2 block text-sm text-gray-700">Lembrar-me</label>
                            </div>
                            <button type="button" class="text-sm font-medium text-blue-600 hover:text-blue-500" onclick="showForm('resetPasswordForm')">
                                Esqueceu a senha?
                            </button>
                        </div>

                        <button type="submit" 
                            class="w-full flex justify-center items-center gap-2 py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-trends-primary hover:bg-trends-primary/60 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-sign-in-alt"></i>
                            Acessar Painel
                        </button>
                    </form>



                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-500">
                            É estudante? 
                            <a href="{{ route('student.login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                                Acessar área do estudante
                            </a>
                        </p>
                    </div>
                </div>

                <!-- Reset Password Form -->
                <div id="resetPasswordForm" class="form-section hidden">
                    <div class="mb-6">
                        <button type="button" class="flex items-center text-sm text-gray-500 hover:text-gray-700" onclick="showForm('loginForm')">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Voltar para login
                        </button>
                    </div>

                    <h1 class="text-2xl font-semibold mb-2">Recuperar Senha</h1>
                    <p class="text-gray-500 text-sm mb-8">Digite seu email para receber as instruções de recuperação</p>

                    <form method="POST" action="{{ route('admin.password.email') }}" class="space-y-5">
                        @csrf
                        <div>
                            <label for="reset_email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="far fa-envelope text-gray-400"></i>
                                </div>
                                <input id="reset_email" name="email" type="email" required 
                                    class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm text-sm
                                    focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Digite seu email">
                            </div>
                        </div>

                        <button type="submit" 
                            class="w-full flex justify-center items-center gap-2 py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-paper-plane"></i>
                            Enviar instruções
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Image Section -->
        <div class="w-full md:w-1/2 md:h-screen bg-trends-secondary">
            <div class="h-full flex items-center justify-center p-4 md:p-8">
                <div class="text-center text-white max-w-lg mx-auto">
                    <h2 class="text-2xl md:text-3xl font-bold mb-2 md:mb-4">Bem-vindo ao Trends Quiz</h2>
                    <p class="text-base md:text-lg mb-4 md:mb-8">A melhor plataforma de cursos médicos do Brasil</p>
                    <img src="{{ asset('images/Logo_Trends-QUIZ_branco-1024x361.png') }}" alt="Trends Quiz" class="w-48 md:w-96 mx-auto">
                </div>
            </div>
        </div>
    </div>

    <style>
        .form-section {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease-in-out;
            display: none;
        }
        .form-section.active {
            opacity: 1;
            transform: translateY(0);
            display: block;
        }
    </style>

    <script>

        // Form switching functionality
        function showForm(formId) {
            document.querySelectorAll('.form-section').forEach(form => {
                form.classList.remove('active');
                form.style.display = 'none';
            });

            const selectedForm = document.getElementById(formId);
            selectedForm.style.display = 'block';
            
            // Limpa mensagens de erro ao trocar de formulário
            clearMessages();
            
            setTimeout(() => {
                selectedForm.classList.add('active');
            }, 10);
        }

        // Função para mostrar mensagens
        function showMessage(message, type = 'success') {
            clearMessages();
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `mb-4 p-4 rounded ${type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'}`;
            
            if (typeof message === 'string') {
                alertDiv.textContent = message;
            } else if (Array.isArray(message)) {
                const ul = document.createElement('ul');
                ul.className = 'list-disc list-inside';
                message.forEach(msg => {
                    const li = document.createElement('li');
                    li.textContent = msg;
                    ul.appendChild(li);
                });
                alertDiv.appendChild(ul);
            }

            const activeForm = document.querySelector('.form-section.active');
            const firstChild = activeForm.firstChild;
            activeForm.insertBefore(alertDiv, firstChild);
        }

        // Função para limpar mensagens
        function clearMessages() {
            document.querySelectorAll('.bg-green-100, .bg-red-100').forEach(el => el.remove());
        }

        // Form submissions
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                clearMessages();
                
                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
                
                try {
                    const response = await fetch(this.action, {
                        method: 'POST',
                        body: new FormData(this),
                        credentials: 'same-origin',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    });

                    // Verifica se a resposta é JSON
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        
                        if (response.ok) {
                            if (data.redirect) {
                                console.log(data.redirect);
                                showMessage(data.message || 'Login realizado com sucesso!');
                                setTimeout(() => {
                                    window.location.href = data.redirect;
                                }, 1000);
                            } else {
                                showMessage(data.message);
                                if (this.id !== 'loginForm') {
                                    this.reset();
                                    setTimeout(() => {
                                        showForm('loginForm');
                                    }, 2000);
                                }
                            }
                        } else if (data.errors) {
                            const errorMessages = Object.values(data.errors).flat();
                            showMessage(errorMessages, 'error');
                        } else if (data.message) {
                            showMessage(data.message, 'error');
                        }
                    } else {
                        // Se não for JSON, significa que houve um redirecionamento ou outro tipo de resposta
                        if (response.redirected) {
                            window.location.href = response.url;
                        } else if (response.ok) {
                            // Enviar o formulário tradicionalmente, sem AJAX
                            form.removeEventListener('submit', arguments.callee);
                            form.submit();
                        } else {
                            showMessage('Erro ao processar solicitação. Por favor, tente novamente.', 'error');
                        }
                    }
                } catch (error) {
                    console.error('Erro:', error);
                    showMessage('Ocorreu um erro ao processar sua solicitação.', 'error');
                } finally {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }
            });
        });

        // Initialize showing login form
        document.addEventListener('DOMContentLoaded', function() {
            showForm('loginForm');
        });
    </script>
</body>
</html> 