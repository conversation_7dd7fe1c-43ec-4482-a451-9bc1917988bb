import { notify } from '../notifications.js';

/**
 * Extensão para controles de paginação e ordenação do Media Manager
 */
export class MediaManagerControls {
    constructor(mediaManager) {
        this.mediaManager = mediaManager;
        this.perPage = 18;
        this.sortOrder = 'newest';
        this.init();
    }

    init() {
        this.initializePerPageControl();
        this.initializeSortControl();
    }

    /**
     * Inicializa o controle de itens por página
     */
    initializePerPageControl() {
        const perPageSelect = document.getElementById('mediaPerPage');
        if (!perPageSelect) return;

        perPageSelect.addEventListener('change', (e) => {
            this.perPage = e.target.value;
            this.applyFiltersAndReload();
        });
    }

    /**
     * Inicializa o controle de ordenação
     */
    initializeSortControl() {
        const sortSelect = document.getElementById('mediaSort');
        if (!sortSelect) return;

        sortSelect.addEventListener('change', (e) => {
            this.sortOrder = e.target.value;
            this.applyFiltersAndReload();
        });
    }

    /**
     * Aplica os filtros atuais e recarrega as mídias
     */
    async applyFiltersAndReload() {
        // Mostrar indicador de carregamento
        this.showLoading();

        try {
            // Obter filtros atuais do gerenciador de mídia
            const currentFilters = { ...this.mediaManager.currentFilters };
            
            // Adicionar novos parâmetros de controle
            const filters = {
                ...currentFilters,
                per_page: this.perPage,
                sort: this.sortOrder
            };

            // Reiniciar para página 1
            this.mediaManager.currentPage = 1;
            
            // Limpar seleção atual
            this.mediaManager.clearSelection();

            // Carregar dados com novos filtros
            const data = await this.mediaManager.loadMedia(1, filters);
            
            // Atualizar a interface
            await this.updateMediaDisplay(data);
            
        } catch (error) {
            console.error('Erro ao aplicar filtros:', error);
            notify.error('Erro ao atualizar lista de mídias');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Atualiza a exibição das mídias
     */
    async updateMediaDisplay(data) {
        // Buscar o container do grid
        const gridContainer = document.querySelector('#mediaGrid, #modalMediaGrid');
        if (!gridContainer) return;

        // Importar o MediaRenderer dinamicamente para evitar dependência circular
        const { MediaRenderer } = await import('./MediaRenderer.js');
        
        // Renderizar novos itens (usar renderMediaGrid que é o método correto)
        const selectedIds = new Set(this.mediaManager.selectedMedia.keys());
        MediaRenderer.renderMediaGrid(gridContainer, data.items, selectedIds);

        // Atualizar paginação
        MediaRenderer.renderPagination(gridContainer, data.pagination, (page) => {
            this.mediaManager.loadMedia(page, this.mediaManager.currentFilters);
        });

        // Atualizar contadores e controles de seleção
        this.updateSelectionControls(data);
        
        // Re-adicionar eventos aos itens (isso normalmente é feito nos MediaManagers)
        this.bindMediaItemEvents(gridContainer);
    }

    /**
     * Atualiza os controles de seleção
     */
    updateSelectionControls(data) {
        const allowMultiple = this.mediaManager.isMultipleAllowed();
        const selectAllBtn = document.getElementById('selectAllBtn');
        const selectAllPageBtn = document.getElementById('selectAllPageBtn');

        if (allowMultiple && data.items.length > 0) {
            // Mostrar botão de seleção da página atual
            if (selectAllPageBtn) {
                selectAllPageBtn.classList.remove('hidden');
                selectAllPageBtn.textContent = `Selecionar ${data.items.length} da página`;
            }

            // Mostrar botão de seleção de todos se houver múltiplas páginas
            if (selectAllBtn && data.pagination.total > data.items.length) {
                selectAllBtn.classList.remove('hidden');
                selectAllBtn.textContent = `Todos os ${data.pagination.total} itens`;
            } else if (selectAllBtn) {
                selectAllBtn.classList.add('hidden');
            }
        } else {
            // Esconder controles se não for múltiplo
            if (selectAllBtn) selectAllBtn.classList.add('hidden');
            if (selectAllPageBtn) selectAllPageBtn.classList.add('hidden');
        }
    }

    /**
     * Mostra indicador de carregamento
     */
    showLoading() {
        const gridContainer = document.querySelector('#mediaGrid, #modalMediaGrid');
        if (!gridContainer) return;

        gridContainer.innerHTML = `
            <div class="col-span-full flex items-center justify-center py-12">
                <div class="flex items-center gap-3 text-zinc-500">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-trends-primary"></div>
                    <span>Carregando mídias...</span>
                </div>
            </div>
        `;
    }

    /**
     * Esconde indicador de carregamento
     */
    hideLoading() {
        // O loading será removido quando renderizarmos os novos itens
    }

    /**
     * Obtém os parâmetros atuais dos controles
     */
    getCurrentControlParams() {
        return {
            per_page: this.perPage,
            sort: this.sortOrder
        };
    }

    /**
     * Define os valores dos controles programaticamente
     */
    setControlValues(perPage, sortOrder) {
        if (perPage) {
            this.perPage = perPage;
            const perPageSelect = document.getElementById('mediaPerPage');
            if (perPageSelect) perPageSelect.value = perPage;
        }

        if (sortOrder) {
            this.sortOrder = sortOrder;
            const sortSelect = document.getElementById('mediaSort');
            if (sortSelect) sortSelect.value = sortOrder;
        }
    }

    /**
     * Re-adiciona eventos aos itens de mídia após renderização
     */
    bindMediaItemEvents(container) {
        container.querySelectorAll('.media-item').forEach(item => {
            const mediaId = parseInt(item.dataset.id);
            const media = this.mediaManager.allMediaItems.get(mediaId);
            
            if (media) {
                // Evento de seleção
                item.addEventListener('click', (e) => {
                    if (e.target.closest('.media-actions')) return;
                    this.mediaManager.toggleItemSelection(mediaId, media);
                    this.updateUIAfterSelection();
                });

                // Eventos dos botões de ação
                item.querySelector('.view-btn')?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.openPreview(media);
                });

                item.querySelector('.delete-btn')?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.deleteMedia(media);
                });
            }
        });
    }

    /**
     * Atualiza a UI após seleção
     */
    updateUIAfterSelection() {
        // Atualizar seleção visual
        const gridContainer = document.querySelector('#mediaGrid, #modalMediaGrid');
        if (gridContainer) {
            gridContainer.querySelectorAll('.media-item').forEach(item => {
                const mediaId = parseInt(item.dataset.id);
                const isSelected = this.mediaManager.isSelected(mediaId);
                
                // Atualizar classes do item
                if (isSelected) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }

                // Atualizar checkbox
                const checkbox = item.querySelector('.absolute.top-2.left-2 div');
                if (checkbox) {
                    if (isSelected) {
                        checkbox.className = 'w-5 h-5 rounded border-2 bg-trends-primary border-trends-primary text-white flex items-center justify-center transition-all duration-200 backdrop-blur-sm';
                        checkbox.innerHTML = '<i class="fas fa-check text-xs"></i>';
                    } else {
                        checkbox.className = 'w-5 h-5 rounded border-2 bg-white/80 border-zinc-300 group-hover:border-trends-primary flex items-center justify-center transition-all duration-200 backdrop-blur-sm';
                        checkbox.innerHTML = '';
                    }
                }
            });
        }

        // Notificar mudança de seleção
        this.mediaManager.notifySelectionChange();
    }

    /**
     * Abre preview da mídia
     */
    async openPreview(media) {
        const { MediaRenderer } = await import('./MediaRenderer.js');
        MediaRenderer.openPreview(media, this.mediaManager.getFileIcon.bind(this.mediaManager));
    }

    /**
     * Deleta uma mídia específica
     */
    async deleteMedia(media) {
        try {
            const success = await this.mediaManager.deleteMedia(media);
            if (success) {
                // Recarregar dados
                await this.applyFiltersAndReload();
            }
        } catch (error) {
            console.error('Erro ao excluir mídia:', error);
            notify.error('Erro ao excluir mídia');
        }
    }
} 