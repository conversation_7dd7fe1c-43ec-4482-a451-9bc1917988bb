<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_modules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id')->nullable(); // Quem criou o módulo
            $table->foreignId('course_id')->constrained('plg_courses')->onDelete('cascade');
            
            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description')->nullable();
            $table->integer('order')->default(0);
            $table->boolean('active')->default(true);
            
            $table->unsignedTinyInteger('duration_months')->nullable(); // Duração em meses (NULL = vitalício)
            
            // Sistema de preços e marketing
            $table->decimal('price', 10, 2)->default(0.00); // Preço atual
            $table->decimal('price_old', 10, 2)->nullable(); // Preço antigo (para mostrar desconto)
            $table->boolean('is_free')->default(true); // Módulo gratuito
            $table->boolean('is_on_sale')->default(false); // Em promoção
            $table->boolean('is_new_release')->default(false); // Lançamento
            $table->boolean('is_featured')->default(false); // Destaque
            $table->boolean('is_bestseller')->default(false); // Mais vendido
            $table->timestamp('sale_starts_at')->nullable(); // Início da promoção
            $table->timestamp('sale_ends_at')->nullable(); // Fim da promoção
            $table->string('currency', 3)->default('BRL'); // Moeda
            $table->text('marketing_description')->nullable(); // Descrição de marketing
            $table->json('marketing_tags')->nullable(); // Tags de marketing ['novo', 'popular', etc]

            $table->timestamps();
            $table->softDeletes();

            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');
            
            // Índices para performance
            $table->index('slug');
            $table->index(['company_id', 'is_free']); // Multi-tenancy + gratuito
            $table->index(['is_free', 'price']);
            $table->index(['course_id', 'active']);
            $table->index(['active', 'order']);
            $table->index(['company_id', 'course_id']); // Multi-tenancy + curso
            
            // Índices para marketing
            $table->index(['is_on_sale', 'sale_ends_at']); // Promoções ativas
            $table->index(['is_featured', 'active']); // Módulos em destaque
            $table->index(['is_new_release', 'active']); // Lançamentos
            $table->index(['is_bestseller', 'active']); // Mais vendidos
            $table->index(['company_id', 'is_featured']); // Multi-tenancy + destaque
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_modules');
    }
};
