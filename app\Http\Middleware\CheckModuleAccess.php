<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\PlgModule;
use App\Models\PlgEnrollment;
use Symfony\Component\HttpFoundation\Response;

class CheckModuleAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verificar se o usuário está autenticado como estudante
        if (!Auth::guard('students')->check()) {
            return redirect()->route('student.login')
                ->with('error', 'Você precisa estar logado para acessar este conteúdo.');
        }

        $student = Auth::guard('students')->user();
        
        // Extrair o slug do módulo da rota
        $moduleSlug = $request->route('moduleSlug');
        
        if (!$moduleSlug) {
            return $next($request);
        }

        // Buscar o módulo
        $module = PlgModule::where('slug', $moduleSlug)->first();
        
        if (!$module) {
            abort(404, 'Módulo não encontrado');
        }

        // Verificar acesso ao módulo
        $accessStatus = $student->getModuleAccessStatus($module);

        // Se o módulo é gratuito, auto-matricular se necessário
        if ($module->is_free) {
            $enrollment = $student->getModuleEnrollment($module);
            if (!$enrollment) {
                try {
                    $student->autoEnrollInFreeModule($module);
                } catch (\Exception $e) {
                    Log::warning('Failed to auto-enroll in free module', [
                        'student_id' => $student->id,
                        'module_id' => $module->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } else {
            // Para módulos pagos, verificar se tem acesso
            if (!$accessStatus['has_access']) {
                return redirect()->route('student.dashboard')
                    ->with('error', 'Você não tem acesso a este módulo. ' . $accessStatus['message']);
            }
        }

        return $next($request);


    }
}
