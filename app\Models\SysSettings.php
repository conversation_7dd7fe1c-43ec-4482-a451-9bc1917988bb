<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SysSettings extends Model
{
    use HasFactory;

    protected $table = 'sys_settings';

    protected $fillable = [
        'company_id',
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
        'is_editable',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_editable' => 'boolean',
    ];

    /**
     * Relacionamento com a empresa.
     */
    public function company()
    {
        return $this->belongsTo(SysCompany::class, 'company_id');
    }

    /**
     * Obter valor de uma configuração.
     */
    public static function getValue($key, $companyId = 1, $default = null)
    {
        $setting = static::where('key', $key)
            ->where('company_id', $companyId)
            ->first();

        if (!$setting) {
            return $default;
        }

        // Converter valor baseado no tipo
        return match ($setting->type) {
            'boolean' => filter_var($setting->value, FILTER_VALIDATE_BOOLEAN),
            'integer' => (int) $setting->value,
            'float' => (float) $setting->value,
            'json' => json_decode($setting->value, true),
            default => $setting->value,
        };
    }

    /**
     * Definir valor de uma configuração.
     */
    public static function setValue($key, $value, $companyId = 1, $type = 'string')
    {
        // Converter valor para string baseado no tipo
        $stringValue = match ($type) {
            'boolean' => $value ? 'true' : 'false',
            'json' => json_encode($value),
            default => (string) $value,
        };

        return static::updateOrCreate(
            ['key' => $key, 'company_id' => $companyId],
            ['value' => $stringValue, 'type' => $type]
        );
    }

    /**
     * Obter configurações por grupo.
     */
    public static function getByGroup($group, $companyId = 1)
    {
        return static::where('group', $group)
            ->where('company_id', $companyId)
            ->get()
            ->keyBy('key');
    }

    /**
     * Obter configurações públicas.
     */
    public static function getPublicSettings($companyId = 1)
    {
        return static::where('is_public', true)
            ->where('company_id', $companyId)
            ->get()
            ->keyBy('key');
    }
}
