<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plg_enrollments', function (Blueprint $table) {
            // Adicionar campo para professor atri<PERSON><PERSON><PERSON> (quando super_admin atribui para um professor especí<PERSON><PERSON>)
            $table->unsignedBigInteger('assigned_teacher_id')->nullable()->after('approved_by');

            // Atualizar enum de enrollment_method para incluir novos valores
            $table->dropColumn('enrollment_method');
        });

        // Recriar a coluna com os novos valores
        Schema::table('plg_enrollments', function (Blueprint $table) {
            $table->enum('enrollment_method', ['self_registration', 'admin_created', 'teacher_assigned', 'teacher_created'])
                ->default('admin_created')
                ->after('status');

            // Adicionar foreign key para assigned_teacher_id
            $table->foreign('assigned_teacher_id')->references('id')->on('sys_users')->onDelete('set null');

            // Adicionar índice
            $table->index(['assigned_teacher_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plg_enrollments', function (Blueprint $table) {
            // Remover foreign key e campo
            $table->dropForeign(['assigned_teacher_id']);
            $table->dropColumn('assigned_teacher_id');

            // Reverter enum
            $table->dropColumn('enrollment_method');
        });

        Schema::table('plg_enrollments', function (Blueprint $table) {
            $table->enum('enrollment_method', ['self_registration', 'admin_created'])
                ->default('admin_created')
                ->after('status');
        });
    }
};
