<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlgTestAttempt extends Model
{
    use HasFactory;

    protected $table = 'plg_test_attempts';

    protected $fillable = [
        'company_id',
        'test_id',
        'student_id',
        'attempt_number',
        'answers',
        'score',
        'correct_count',
        'total_questions',
        'passed',
        'started_at',
        'finished_at',
        'status',
    ];

    protected $casts = [
        'answers' => 'array',
        'score' => 'decimal:2',
        'correct_count' => 'integer',
        'total_questions' => 'integer',
        'passed' => 'boolean',
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'status' => 'string',
    ];

    /**
     * Relacionamento com o teste.
     */
    public function test(): BelongsTo
    {
        return $this->belongsTo(PlgTest::class, 'test_id');
    }

    /**
     * Relacionamento com o estudante.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(PlgStudent::class, 'student_id');
    }

    /**
     * Calcula o score baseado nas respostas.
     */
    public function calculateScore(): void
    {
        if (!$this->answers || !is_array($this->answers)) {
            $this->score = 0;
            $this->correct_count = 0;
            $this->passed = false;
            return;
        }

        $correctCount = 0;
        $totalQuestions = count($this->answers);

        foreach ($this->answers as $answer) {
            if (isset($answer['is_correct']) && $answer['is_correct']) {
                $correctCount++;
            }
        }

        $this->correct_count = $correctCount;
        $this->total_questions = $totalQuestions;
        $this->score = $totalQuestions > 0 ? round(($correctCount / $totalQuestions) * 100) : 0;
        $this->passed = $this->score >= $this->test->passing_score;
    }

    /**
     * Finaliza a tentativa.
     */
    public function complete(): void
    {
        $this->calculateScore();
        $this->finished_at = now();
        $this->status = 'completed';
        $this->save();

        // Atualizar relatório do estudante
        $this->updateStudentReport();
    }

    /**
     * Abandona a tentativa.
     */
    public function abandon(): void
    {
        $this->status = 'abandoned';
        $this->finished_at = now();
        $this->save();
    }

    /**
     * Atualiza o relatório do estudante.
     */
    private function updateStudentReport(): void
    {
        $report = PlgTestReport::firstOrCreate([
            'company_id' => $this->company_id,
            'test_id' => $this->test_id,
            'student_id' => $this->student_id,
        ]);

        // Recalcular total_attempts baseado apenas em tentativas completadas
        $completedAttempts = PlgTestAttempt::where('test_id', $this->test_id)
            ->where('student_id', $this->student_id)
            ->where('status', 'completed')
            ->count();

        $report->total_attempts = $completedAttempts;
        $report->last_attempt_at = $this->finished_at;

        if ($this->score > ($report->best_score ?? 0)) {
            $report->best_score = $this->score;
        }

        if ($this->passed) {
            $report->passed = true;
        }

        $report->save();
    }

    /**
     * Verifica se a tentativa está em progresso.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Verifica se a tentativa foi completada.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Verifica se a tentativa foi abandonada.
     */
    public function isAbandoned(): bool
    {
        return $this->status === 'abandoned';
    }

    /**
     * Calcula o progresso da tentativa.
     */
    public function getProgress(): array
    {
        $answeredCount = 0;

        if (is_array($this->answers)) {
            $answeredCount = count(array_filter($this->answers, function($answer) {
                return !is_null($answer) && $answer !== '';
            }));
        }

        $totalQuestions = $this->total_questions ?? 0;
        $percentage = $totalQuestions > 0 ? round(($answeredCount / $totalQuestions) * 100) : 0;

        return [
            'answered' => $answeredCount,
            'total' => $totalQuestions,
            'percentage' => $percentage,
            'current_question' => $answeredCount + 1 <= $totalQuestions ? $answeredCount + 1 : $totalQuestions
        ];
    }
}
