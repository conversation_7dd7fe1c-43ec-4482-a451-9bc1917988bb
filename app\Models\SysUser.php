<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Database\Factories\UserFactory;

class SysUser extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes;

    protected $table = 'sys_users';
    
    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return UserFactory::new();
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name', 'email', 'password', 'role', 'bio', 'profile_image', 'active', 'company_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'active' => 'boolean',
    ];

    public function courses()
    {
        return $this->belongsToMany(PlgCourse::class, 'plg_enrollments', 'student_id', 'course_id')
                    ->withTimestamps()
                    ->withPivot(['status', 'enrolled_at', 'expires_at']);
    }
    
    public function isSuperAdmin()
    {
        return $this->role === 'super_admin';
    }

    public function teacherCourses()
    {
        return $this->hasMany(PlgCourse::class, 'user_id');
    }
} 