// Toggle password visibility
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');

            if (input && icon) {
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }
        });
    });
});


    
// Aguardar jQuery estar disponível
function initJQueryFunctions() {
    if (typeof window.$ === 'undefined') {
        setTimeout(initJQueryFunctions, 100);
        return;
    }

    // usa jQuery via window.$ ou window.jQuery
    window.$(function() {
        const btn = document.getElementById('mobile-menu-button');
        const overlay = document.getElementById('mobile-overlay');
        const sidebar = document.getElementById('mobile-sidebar');
        const closeBtn = document.getElementById('close-menu-button');

        // Só inicializar se os elementos existirem (não estamos no layout do quiz)
        if (btn && overlay && sidebar) {
            function openMenu() {
                overlay.classList.remove('hidden');
                sidebar.classList.remove('hidden');
            }

            function closeMenu() {
                overlay.classList.add('hidden');
                sidebar.classList.add('hidden');
            }

            btn.addEventListener('click', openMenu);
            overlay.addEventListener('click', closeMenu);

            if (closeBtn) {
                closeBtn.addEventListener('click', closeMenu);
            }
        }
    });
}

// Inicializar funções jQuery
initJQueryFunctions();


/*CHOICES.JS*/
document.addEventListener('DOMContentLoaded', function() {
    // Aguardar o Choices estar disponível
    function initChoices() {
        if (typeof window.Choices === 'undefined') {
            setTimeout(initChoices, 100);
            return;
        }

        const selectElements = document.querySelectorAll('.choices-select');

        if (selectElements.length > 0) {
            selectElements.forEach(function(element) {
                if (element.choicesInstance) {
                    return; // Já foi inicializado
                }

            const placeholder = element.getAttribute('data-placeholder') || 'Selecione uma opção';

            const choices = new window.Choices(element, {
                searchEnabled: true,
                searchChoices: true,
                searchPlaceholderValue: 'Digite para buscar...',
                noResultsText: 'Nenhum resultado encontrado',
                noChoicesText: 'Nenhuma opção disponível',
                itemSelectText: 'Clique para selecionar',
                placeholder: true,
                placeholderValue: placeholder,
                removeItemButton: false,
                shouldSort: false,
                allowHTML: true
            });

            // Armazenar a instância para referência futura
            element.choicesInstance = choices;
            });
        }
    }

    initChoices();
});


/**
 * Plugin global para atualização de previews dinâmicos com base em campos de formulário
 * Suporte a: texto, selects, choices.js, imagens, mediaSelected
 * Requer: Choices.js
 */

(function () {
    function initLivePreviews() {
        const previewContainers = document.querySelectorAll('.live-preview');

        if (previewContainers.length > 0) {
            previewContainers.forEach(container => {
            const elements = container.querySelectorAll('[data-preview]');

            elements.forEach(el => {
                const field = el.dataset.preview;
                const type = el.dataset.previewType || 'text';

                const input = document.querySelector(`[name="${field}"], #${field}`);
                if (!input) return;

                const update = () => {
                    if (type === 'image') {
             

                            const file = input?.files?.[0];
                            if (file) {
                                const reader = new FileReader();
                                reader.onload = e => {
                                    el.innerHTML = `<img src="${e.target.result}" class="w-full object-cover rounded-md">`;
                                    el.classList.remove('hidden');
                                };
                                reader.readAsDataURL(file);
                            } else {
                                const defaultUrl = el.dataset.default;
                                if (defaultUrl) {
                                    el.innerHTML = `<img src="${defaultUrl}" class="w-full object-cover rounded-md">`;
                                    el.classList.remove('hidden');
                                } else {
                                    el.classList.add('hidden');
                                }
                            }



                    } else if (input.tagName === 'SELECT') {
                        let selectedText = '';
                        if (input.choicesInstance) {
                            // Para Choices.js
                            const selectedValue = input.choicesInstance.getValue();
                            if (Array.isArray(selectedValue)) {
                                selectedText = selectedValue.map(item => item?.label || item?.value || '').join(', ');
                            } else if (selectedValue) {
                                selectedText = selectedValue.label || selectedValue.value || '';
                            }
                        } else {
                            selectedText = input.options[input.selectedIndex]?.text || '';
                        }
                        el.textContent = selectedText || el.dataset.default || '';
                    } else {
                        el.textContent = input.value || el.dataset.default || '';
                    }
                };

                // Eventos
                if (input.tagName === 'SELECT') {
                    if (input.choicesInstance) {
                        // Para Choices.js
                        input.addEventListener('choice', update);
                        input.addEventListener('removeItem', update);
                    } else {
                        input.addEventListener('change', update);
                    }
                } else if (type === 'image') {
                    input.addEventListener('change', update);
                } else {
                    input.addEventListener('input', update);
                    input.addEventListener('change', update);
                }

                // Inicializa
                update();
            });
            });
        }
    }

    // Executa ao carregar DOM
    document.addEventListener('DOMContentLoaded', initLivePreviews);

    // Suporte ao evento customizado de seleção de mídia (ex: media manager personalizado)
    document.addEventListener('mediaSelected', function (e) {
        const field = e.detail?.field || 'thumbnail';
        const media = e.detail?.media;
        const el = document.querySelector(`[data-preview="${field}"]`);

        if (el && media?.url) {
            el.innerHTML = `<img src="${media.url}" class="w-full object-cover rounded-md">`;
            el.classList.remove('hidden');
        }
    });
})();








/*
PARA USAR EM QUALQUER FORMULÁRIO COM 1 MEDIA PREVIEW
Atenção para a CLASS
.media-input
.media-preview
.preview-remove-btn
*/

 document.addEventListener('DOMContentLoaded', () => {
  // Liga evento mediaSelected a todos os inputs .media-input
  document.querySelectorAll('.media-input').forEach(input => {
    input.addEventListener('mediaSelected', e => {
      const media = e.detail?.media;
      const previewId = input.dataset.previewId;
      if (!previewId || !media || !media.url) return;

      const previewEl = document.getElementById(previewId);
      if (!previewEl) return;

      const ext = media.extension?.toLowerCase() || '';
      let previewHTML = '';

      //console.log('Media selecionada:', media);

if (['pdf'].includes(ext)) {
        previewHTML = `
          <div class="relative bg-red-100 p-4 rounded-lg">
            <div class="flex items-center gap-3">
              <i class="fas fa-file-pdf text-red-500 text-2xl"></i>
              <div class="flex-1">
                <p class="font-medium text-red-800">${media.filename}</p>
                <p class="text-sm text-red-600">Documento PDF</p>
              </div>
              <button type="button" class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 preview-remove-btn">
                <i class="fas fa-times text-xs"></i>
              </button>
            </div>
          </div>
        `;
      } else if (['mp3'].includes(ext)) {
        previewHTML = `
          <div class="relative bg-blue-100 p-4 rounded-lg">
            <div class="flex items-center gap-3">
              <i class="fas fa-music text-blue-500 text-2xl"></i>
              <div class="flex-1">
                <p class="font-medium text-blue-800">${media.filename}</p>
                <p class="text-sm text-blue-600">Áudio MP3</p>
              </div>
              <button type="button" class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-blue-600 preview-remove-btn">
                <i class="fas fa-times text-xs"></i>
              </button>
            </div>
          </div>
        `;
      } else {
        // Imagem (default)
        previewHTML = `
          <div class="relative w-64">
            <img src="${media.url}" alt="Preview" class="w-full h-32 object-cover rounded-lg" />
            <button type="button" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 preview-remove-btn" aria-label="Remover midia">
              <i class="fas fa-times text-xs"></i>
            </button>
          </div>
          <p class="text-sm text-zinc-500 mt-2 truncate">${media.filename || ''}</p>
        `;

      }


      previewEl.innerHTML = previewHTML;
      previewEl.classList.remove('hidden');


      // Esconde o botão .open-media-modal correspondente
      const openBtn = document.querySelector(`.open-media-modal[data-preview-id="${previewId}"]`);
      if (openBtn) openBtn.classList.add('hidden');

    });
  });


  // Delegação de evento para remover preview ao clicar no botão
  document.body.addEventListener('click', event => {
    if (!event.target.closest('.preview-remove-btn')) return;

    const btn = event.target.closest('.preview-remove-btn');
    const previewEl = btn.closest('.media-preview');
    if (!previewEl) return;

    // Descobre qual input corresponde a esse preview
    const previewId = previewEl.id;
    const input = document.querySelector(`.media-input[data-preview-id="${previewId}"]`);
    if (input) input.value = '';

    previewEl.classList.add('hidden');
    previewEl.innerHTML = '';

     // Reativa o botão .open-media-modal correspondente (se houver)
    const openBtn = document.querySelector(`.open-media-modal[data-preview-id="${previewId}"]`);
    if (openBtn) openBtn.classList.remove('hidden');
  });







    /*MASCARA DINHEIRO*/
    document.querySelectorAll('.money').forEach(input => {
        input.addEventListener('input', e => {
            const caret = input.selectionStart;
            const raw = input.value;

            // Reformatando
            const formatted = formatarMoedaBRL(raw);
            input.value = formatted;

            // Coloca o cursor no final para evitar bagunça
            input.setSelectionRange(input.value.length, input.value.length);
        });

        // Evita colar letras, símbolos etc.
        input.addEventListener('keypress', e => {
            if (!/[0-9]/.test(e.key)) {
                e.preventDefault();
            }
        });
    });




    /* MÁSCARA DE CEP */
    document.querySelectorAll('.cep').forEach(input => {
        input.addEventListener('input', () => {
            const raw = input.value.replace(/\D/g, '').slice(0, 8); // Limita a 8 dígitos
            let formatted = raw;

            if (raw.length > 5) {
                formatted = raw.slice(0, 5) + '-' + raw.slice(5);
            }

            input.value = formatted;
        });

        // Bloqueia teclas que não são números
        input.addEventListener('keypress', e => {
            if (!/[0-9]/.test(e.key)) {
                e.preventDefault();
            }
        });
    });



    /* MÁSCARA DE CEP */
    document.querySelectorAll('.cep').forEach(input => {
        input.addEventListener('input', () => {
            const raw = input.value.replace(/\D/g, '').slice(0, 8); // Limita a 8 dígitos
            let formatted = raw;

            if (raw.length > 5) {
                formatted = raw.slice(0, 5) + '-' + raw.slice(5);
            }

            input.value = formatted;
        });

        // Bloqueia teclas que não são números
        input.addEventListener('keypress', e => {
            if (!/[0-9]/.test(e.key)) {
                e.preventDefault();
            }
        });
    });




    /* MÁSCARA DE TELEFONE */
    document.querySelectorAll('.phone').forEach(input => {
        input.addEventListener('input', () => {
            let raw = input.value.replace(/\D/g, '').slice(0, 11); // Limita a 11 dígitos
            let formatted = raw;

            if (raw.length >= 2 && raw.length <= 6) {
                formatted = `(${raw.slice(0, 2)}) ${raw.slice(2)}`;
            } else if (raw.length > 6 && raw.length <= 10) {
                formatted = `(${raw.slice(0, 2)}) ${raw.slice(2, 6)}-${raw.slice(6)}`;
            } else if (raw.length === 11) {
                formatted = `(${raw.slice(0, 2)}) ${raw.slice(2, 7)}-${raw.slice(7)}`;
            }

            input.value = formatted;
        });

        input.addEventListener('keypress', e => {
            if (!/[0-9]/.test(e.key)) {
                e.preventDefault();
            }
        });
    });




 });





function formatarMoedaBRL(value) {
    const clean = value.replace(/\D/g, ''); // Remove tudo que não for número
    const number = parseFloat(clean) / 100;
    if (isNaN(number)) return '';
    return number.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL',
        minimumFractionDigits: 2
    });
}