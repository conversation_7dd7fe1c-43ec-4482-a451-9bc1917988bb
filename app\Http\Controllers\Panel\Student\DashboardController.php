<?php

namespace App\Http\Controllers\Panel\Student;

use App\Http\Controllers\Controller;
use App\Models\PlgCourse;
use App\Models\PlgModule;
use App\Models\PlgTestAttempt;
use App\Models\StudentContentProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Construtor que aplica middleware de autenticação
     */
    public function __construct()
    {
        $this->middleware('auth:students');
    }
    
    public function index()
    {
        $user = Auth::guard('students')->user();
        $courses = PlgCourse::with(['teacher', 'modules', 'media'])
            ->where('status', 'published')
            ->get();

        // Adicionar informações de status de matrícula para cada módulo
        foreach ($courses as $course) {
            foreach ($course->modules as $module) {
                $module->access_status = $user->getModuleAccessStatus($module);
                $module->enrollment = $user->getModuleEnrollment($module);
            }
        }

        return view('panel.student.dashboard', compact('user', 'courses'));
    }
    
    public function courses()
    {
        $user = Auth::guard('students')->user();

        // Buscar apenas cursos onde o aluno tem pelo menos um módulo matriculado
        $courses = PlgCourse::with(['teacher', 'modules', 'media'])
            ->whereHas('modules.enrollments', function($query) use ($user) {
                $query->where('student_id', $user->id)
                      ->where('status', 'active');
            })
            ->where('status', 'published')
            ->get();

        // Adicionar informações de status de matrícula para cada módulo
        foreach ($courses as $course) {
            foreach ($course->modules as $module) {
                $module->access_status = $user->getModuleAccessStatus($module);
                $module->enrollment = $user->getModuleEnrollment($module);
            }
        }

        return view('panel.student.courses', compact('user', 'courses'));
    }
    
    public function show($courseSlug)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::with(['teacher', 'modules.contents', 'modules.media', 'modules.tests' => function($query) {
            $query->where('active', true);
        }, 'media'])
            ->where('slug', $courseSlug)
            ->firstOrFail();

        // Adicionar informações de status de matrícula para cada módulo
        foreach ($course->modules as $module) {
            $module->access_status = $user->getModuleAccessStatus($module);
            $module->enrollment = $user->getModuleEnrollment($module);
        }

        return view('panel.student.courses.show', compact('user', 'course'));
    }
    
    public function quizzes()
    {
        $user = Auth::guard('students')->user();
        return view('panel.student.quizzes', compact('user'));
    }
    
    public function takeQuiz($id)
    {
        $student = Auth::guard('students')->user();

        // Buscar o teste pelo ID
        $test = \App\Models\PlgTest::findOrFail($id);
        $module = $test->module;
        $course = $module->course;

        // Verificar informações sobre tentativas
        $report = $test->getStudentReport($student->id);
        $canAttempt = !$report || $report->canMakeNewAttempt();
        $attemptsInfo = [
            'total_attempts' => $report ? $report->total_attempts : 0,
            'max_attempts' => $test->max_attempts,
            'can_attempt' => $canAttempt,
            'is_unlimited' => $test->allowsUnlimitedAttempts()
        ];

        // Ação padrão
        $action = [
            'type' => 'start',
            'message' => 'Iniciar novo quiz'
        ];

        return view('panel.student.quiz.take', compact('student', 'course', 'module', 'test', 'action', 'attemptsInfo'));
    }
    
    public function simulated()
    {
        $user = Auth::guard('students')->user();
        return view('panel.student.simulated', compact('user'));
    }

    public function profile()
    {
        $user = Auth::guard('students')->user();
        return view('panel.student.profile', compact('user'));
    }

    /**
     * Exibe a tela de detalhes do módulo (hub)
     */
    public function showModuleDetails($courseSlug, $moduleSlug)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->with(['contents', 'tests' => function($query) {
            $query->where('active', true);
        }])->firstOrFail();

        // Verificar acesso ao módulo
        $accessStatus = $user->getModuleAccessStatus($module);

        if (!$accessStatus['has_access']) {
            return redirect()->route('student.dashboard')
                ->with('error', $accessStatus['message']);
        }

        // Buscar progresso do usuário para este módulo
        $progress = StudentContentProgress::getModuleProgress($user->id, $module->id);
        $totalContents = $module->contents->count();
        $progressPercentage = StudentContentProgress::calculateModuleProgress($user->id, $module->id, $totalContents);

        // Verificar se todos os conteúdos estão concluídos
        $contentIds = $module->contents->pluck('id');
        $completedContents = StudentContentProgress::where('student_id', $user->id)
            ->whereIn('content_id', $contentIds)
            ->where('status', 'completed')
            ->count();

        // Estatísticas do módulo
        $quizzes = $module->tests->where('test_type', 'quiz')->where('active', true);
        $exams = $module->tests->where('test_type', 'exam')->where('active', true);
        $totalQuizzes = $quizzes->count() + $exams->count();

        // Calcular quizzes aprovados
        $approvedQuizzes = 0;
        $completedQuizzes = 0;

        foreach ($module->tests->where('active', true) as $test) {
            // Usar o novo modelo PlgTestAttempt para verificar tentativas
            $completedAttempts = PlgTestAttempt::where('student_id', $user->id)
                ->where('test_id', $test->id)
                ->where('status', 'completed')
                ->count();

            if ($completedAttempts > 0) {
                $completedQuizzes++;

                // Verificar se passou no teste
                $bestAttempt = PlgTestAttempt::where('student_id', $user->id)
                    ->where('test_id', $test->id)
                    ->where('status', 'completed')
                    ->orderBy('score', 'desc')
                    ->first();

                if ($bestAttempt && $bestAttempt->score >= $test->passing_score) {
                    $approvedQuizzes++;
                }
            }
        }

        $stats = [
            'quizzes' => $totalQuizzes,
            'materials' => $totalContents,
            'approved' => $approvedQuizzes,
            'completed' => $completedQuizzes,
            'progress' => $progressPercentage
        ];

        // Obter estatísticas detalhadas do módulo para o estudante
        $moduleStats = $user->getModuleStats($module);

        // Buscar todos os testes ativos (sem distinção de tipo)
        $quizzes = $module->tests->where('active', true);
        $exams = collect(); // Coleção vazia já que não temos mais distinção de tipos
        $hasAccess = true; // Já verificado acesso acima

        return view('panel.student.modules.details', compact(
            'user',
            'course',
            'module',
            'progress',
            'progressPercentage',
            'completedContents',
            'totalContents',
            'stats',
            'moduleStats',
            'quizzes',
            'exams',
            'hasAccess'
        ));
    }

    /**
     * Exibe a tela de materiais do módulo (antiga showModule)
     */
    public function showModule($courseSlug, $moduleSlug)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->with(['contents', 'tests' => function($query) {
            $query->where('active', true);
        }])->firstOrFail();

        // Verificar acesso ao módulo
        $accessStatus = $user->getModuleAccessStatus($module);

        if (!$accessStatus['has_access']) {
            return redirect()->route('student.dashboard')
                ->with('error', $accessStatus['message']);
        }

        // Buscar progresso do usuário para este módulo
        $progress = StudentContentProgress::getModuleProgress($user->id, $module->id);
        $totalContents = $module->contents->count();
        $progressPercentage = StudentContentProgress::calculateModuleProgress($user->id, $module->id, $totalContents);

        // Verificar se todos os conteúdos estão concluídos
        $contentIds = $module->contents->pluck('id');
        $completedContents = StudentContentProgress::where('student_id', $user->id)
            ->whereIn('content_id', $contentIds)
            ->where('status', 'completed')
            ->count();
        $allContentsCompleted = $totalContents > 0 && $completedContents === $totalContents;

        // Se todos os conteúdos estão concluídos, redirecionar para página de conclusão
        if ($allContentsCompleted) {
            return redirect()->route('student.module.conclusion', [$courseSlug, $moduleSlug]);
        }

        return view('panel.student.modules.show', compact('user', 'course', 'module', 'progress', 'progressPercentage', 'allContentsCompleted'));
    }

    /**
     * Exibe a tela de quizzes do módulo
     */
    public function showModuleQuizzes($courseSlug, $moduleSlug)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->with(['tests' => function($query) {
            $query->where('active', true);
        }])->firstOrFail();

        // Verificar acesso ao módulo
        $accessStatus = $user->getModuleAccessStatus($module);

        if (!$accessStatus['has_access']) {
            return redirect()->route('student.dashboard')
                ->with('error', $accessStatus['message']);
        }

        // Separar quizzes e exames
        $quizzes = $module->tests->where('test_type', 'quiz');
        $exams = $module->tests->where('test_type', 'exam');

        return view('panel.student.modules.quizzes', compact('user', 'course', 'module', 'quizzes', 'exams'));
    }

    public function showModuleConclusion($courseSlug, $moduleSlug)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->with(['contents', 'tests' => function($query) {
            $query->where('active', true);
        }])->firstOrFail();

        // Verificar acesso ao módulo
        $accessStatus = $user->getModuleAccessStatus($module);

        if (!$accessStatus['has_access']) {
            return redirect()->route('student.dashboard')
                ->with('error', $accessStatus['message']);
        }

        // Buscar progresso do usuário para este módulo
        $progress = StudentContentProgress::getModuleProgress($user->id, $module->id);
        $totalContents = $module->contents->count();
        $progressPercentage = StudentContentProgress::calculateModuleProgress($user->id, $module->id, $totalContents);

        // Verificar se todos os conteúdos estão concluídos
        $contentIds = $module->contents->pluck('id');
        $completedContents = StudentContentProgress::where('student_id', $user->id)
            ->whereIn('content_id', $contentIds)
            ->where('status', 'completed')
            ->count();
        $allContentsCompleted = $totalContents > 0 && $completedContents === $totalContents;

        // Se não completou todos os conteúdos, redirecionar de volta para o módulo
        if (!$allContentsCompleted) {
            return redirect()->route('student.module.show', [$courseSlug, $moduleSlug]);
        }

        return view('panel.student.modules.conclusion', compact('user', 'course', 'module', 'progress', 'progressPercentage', 'allContentsCompleted'));
    }

    public function showModuleContent($courseSlug, $moduleSlug, $contentId)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->with(['contents', 'tests'])->firstOrFail();

        // Verificar acesso ao módulo
        $accessStatus = $user->getModuleAccessStatus($module);

        if (!$accessStatus['has_access']) {
            return redirect()->route('student.dashboard')
                ->with('error', $accessStatus['message']);
        }

        $content = $module->contents()->where('id', $contentId)->firstOrFail();

        // Determinar conteúdo anterior e próximo
        $contents = $module->contents()->orderBy('order')->get();
        $currentIndex = $contents->search(function($item) use ($content) {
            return $item->id === $content->id;
        });

        $previousContent = $currentIndex > 0 ? $contents[$currentIndex - 1] : null;
        $nextContent = $currentIndex < $contents->count() - 1 ? $contents[$currentIndex + 1] : null;

        // Buscar progresso do usuário para este módulo
        $progress = StudentContentProgress::getModuleProgress($user->id, $module->id);
        $totalContents = $module->contents->count();
        $progressPercentage = StudentContentProgress::calculateModuleProgress($user->id, $module->id, $totalContents);
        $isCompleted = StudentContentProgress::isCompleted($user->id, $content->id);

        return view('panel.student.modules.content', compact(
            'user', 'course', 'module', 'content', 'previousContent', 'nextContent',
            'progress', 'progressPercentage', 'isCompleted', 'currentIndex', 'totalContents'
        ));
    }

    /**
     * Marcar conteúdo como concluído
     */
    public function markContentAsCompleted(Request $request, $courseSlug, $moduleSlug, $contentId)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->firstOrFail();
        $content = $module->contents()->where('id', $contentId)->firstOrFail();

        StudentContentProgress::markAsCompleted($user->id, $content->id);

        // Recalcular progresso
        $totalContents = $module->contents->count();
        $progressPercentage = StudentContentProgress::calculateModuleProgress($user->id, $module->id, $totalContents);
        $contentIds = $module->contents->pluck('id');
        $completedCount = StudentContentProgress::where('student_id', $user->id)
            ->whereIn('content_id', $contentIds)
            ->where('status', 'completed')
            ->count();

        // Verificar se todos os conteúdos estão concluídos
        $allContentsCompleted = $totalContents > 0 && $completedCount === $totalContents;

        return response()->json([
            'success' => true,
            'completed' => true,
            'progress_percentage' => $progressPercentage,
            'completed_count' => $completedCount,
            'total_contents' => $totalContents,
            'all_contents_completed' => $allContentsCompleted,
            'redirect_url' => $allContentsCompleted ? route('student.module.conclusion', [$courseSlug, $moduleSlug]) : null,
        ]);
    }

    /**
     * Marcar conteúdo como não concluído
     */
    public function markContentAsIncomplete(Request $request, $courseSlug, $moduleSlug, $contentId)
    {
        $user = Auth::guard('students')->user();
        $course = PlgCourse::where('slug', $courseSlug)->firstOrFail();
        $module = $course->modules()->where('slug', $moduleSlug)->firstOrFail();
        $content = $module->contents()->where('id', $contentId)->firstOrFail();

        StudentContentProgress::markAsIncomplete($user->id, $content->id);

        // Recalcular progresso
        $totalContents = $module->contents->count();
        $progressPercentage = StudentContentProgress::calculateModuleProgress($user->id, $module->id, $totalContents);
        $contentIds = $module->contents->pluck('id');
        $completedCount = StudentContentProgress::where('student_id', $user->id)
            ->whereIn('content_id', $contentIds)
            ->where('status', 'completed')
            ->count();

        return response()->json([
            'success' => true,
            'completed' => false,
            'progress_percentage' => $progressPercentage,
            'completed_count' => $completedCount,
            'total_contents' => $totalContents,
        ]);
    }

    /**
     * Auto-matricular estudante em módulo gratuito e redirecionar
     */
    public function enrollInFreeModule(PlgModule $module)
    {
        try {
            $user = Auth::guard('students')->user();

            // Carregar o relacionamento com o curso
            $module->load('course');

            // Verificar se o módulo é gratuito
            if (!$module->is_free) {
                return redirect()->back()->with('error', 'Este módulo não é gratuito');
            }

            // Auto-matricular
            $enrollment = $user->autoEnrollInFreeModule($module);

            // Redirecionar para o módulo
            return redirect()->route('student.module.show', [
                'courseSlug' => $module->course->slug,
                'moduleSlug' => $module->slug
            ])->with('success', 'Matrícula realizada com sucesso!');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao realizar matrícula: ' . $e->getMessage());
        }
    }
}