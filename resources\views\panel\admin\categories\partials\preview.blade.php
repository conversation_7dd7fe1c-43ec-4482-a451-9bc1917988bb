<!-- <PERSON><PERSON> da direita - Preview da categoria -->
<div class="live-preview lg:col-span-1">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg sticky">
        <div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
            <div class="flex items-center gap-2">
                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                <h2 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">Preview</h2>
            </div>
            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                Visualização da categoria
            </p>
        </div>

        <div class="p-6">
            <div class="space-y-4">
                <!-- Thumbnail da categoria -->
                <div data-preview="thumbnail" data-preview-type="image" data-default="{{ @$thumbnailUrl }}" class="mb-4 {{ isset($category) && $category->hasMedia('thumbnail') ? '' : 'hidden' }}">
                    @if (!empty($thumbnailUrl))
                        <img src="{{ $thumbnailUrl }}" alt="Thumbnail da Categoria" class="w-full rounded-md object-cover h-40">
                    @endif
                </div>

                <!-- Ícone e Título -->
                <div class="flex items-center gap-3">
                    <div class="w-12 h-12 bg-trends-primary/10 flex items-center justify-center bg-zinc-100 dark:bg-zinc-800 rounded-full">
                        <i id="preview-icon" class="{{ isset($category) && $category->icon ? $category->icon : 'fas fa-book' }} text-zinc-800 dark:text-zinc-100 text-2xl"></i>
                    </div>
                    <div>
                        <h3 id="preview-title" data-preview="title" data-default="{{ $category->title ?? 'Nome da Categoria' }}" class="font-semibold text-zinc-800 dark:text-zinc-100">
                            {{ $category->title ?? 'Nome da Categoria' }}
                        </h3>
                        <p class="text-sm text-zinc-500 dark:text-zinc-400">
                            Ordem: <span id="preview-order" data-preview="order" data-default="{{@$category->order}}">{{ $category->order ?? 0 }}</span>
                        </p>
                    </div>
                </div>

                <!-- Descrição -->
                <div>
                    <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Descrição</h4>
                    <p id="preview-description" data-preview="description" data-default="{{ $category->description ?? 'Descrição da categoria aparecerá aqui...' }}" class="text-sm text-zinc-600 dark:text-zinc-400">
                        {{ $category->description ?? 'Descrição da categoria aparecerá aqui...' }}
                    </p>
                </div>

                <!-- Status -->
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-zinc-700 dark:text-zinc-300">Status:</span>
                    <span id="preview-status" data-preview="active" data-default="" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ isset($category) && $category->active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        <i class="fas {{ isset($category) && $category->active ? 'fa-check-circle' : 'fa-times-circle' }} mr-1"></i>
                        {{ isset($category) && $category->active ? 'Ativa' : 'Inativa' }}
                    </span>
                </div>

                <!-- Informações adicionais -->
                <div class="pt-4 border-t border-zinc-200 dark:border-zinc-700">
                    <div class="space-y-2">
                        <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                            <i class="fas fa-calendar text-xs"></i>
                            <span class="text-xs">Criada: {{ isset($category) ? $category->created_at->format('d/m/Y') : 'N/A' }}</span>
                        </div>
                        @if(isset($category) && $category->updated_at != $category->created_at)
                        <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                            <i class="fas fa-edit text-xs"></i>
                            <span class="text-xs">Atualizada: {{ $category->updated_at->format('d/m/Y') }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
/*

ACHO QUE NAO VAMOS MAIS PRECISA DISSO ABAIXO, FICOU TUDO DINAMICO COM data-preview


document.addEventListener('DOMContentLoaded', function() {
    // ===== ELEMENTOS DO PREVIEW =====
    const previewThumbnail = document.getElementById('preview-category-thumbnail');
    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const previewIcon = document.getElementById('preview-icon');
    const previewOrder = document.getElementById('preview-order');
    const previewStatus = document.getElementById('preview-status');

    // ===== ELEMENTOS DO FORMULÁRIO =====
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const iconInput = document.getElementById('icon');
    const orderInput = document.getElementById('order');
    const activeCheckbox = document.querySelector('input[name="active"]');
    const thumbnailInput = document.getElementById('thumbnail');

    // ===== EVENTOS DE PREVIEW EM TEMPO REAL =====
    
    // Preview do título em tempo real
    if (titleInput && previewTitle) {
        titleInput.addEventListener('input', function() {
            previewTitle.textContent = this.value || 'Nome da Categoria';
        });
    }

    // Preview da descrição em tempo real
    if (descriptionInput && previewDescription) {
        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || 'Descrição da categoria aparecerá aqui...';
        });
    }

    // Preview do ícone em tempo real
    if (iconInput && previewIcon) {
        iconInput.addEventListener('input', function() {
            let iconClass = this.value.trim();

            // Se não começar com 'fa', adicionar 'fas'
            if (iconClass && !iconClass.startsWith('fa')) {
                iconClass = 'fas fa-' + iconClass;
            }

            previewIcon.className = (iconClass || 'fas fa-book') + ' text-zinc-800 dark:text-zinc-100 text-2xl';
        });
    }

    // Preview da ordem em tempo real
    if (orderInput && previewOrder) {
        orderInput.addEventListener('input', function() {
            previewOrder.textContent = this.value || '0';
        });
    }

    // Preview do status em tempo real
    if (activeCheckbox && previewStatus) {
        activeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                previewStatus.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
                previewStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i>Ativa';
            } else {
                previewStatus.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800';
                previewStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i>Inativa';
            }
        });
    }

    // Preview do thumbnail quando mídia é selecionada
    if (thumbnailInput) {
        thumbnailInput.addEventListener('mediaSelected', (e) => {
            const media = e.detail.media;
            updateThumbnailPreview(media);
        });
    }

    // ===== FUNÇÕES DE PREVIEW =====
    

    function updateThumbnailPreview(media) {
        if (!previewThumbnail) return;

        if (media && media.url) {
            const img = previewThumbnail.querySelector('img');
            if (img) {
                img.src = media.url;
                img.alt = media.filename || 'Thumbnail da categoria';
            } else {
                previewThumbnail.innerHTML = `<img src="${media.url}" alt="${media.filename || 'Thumbnail da categoria'}" class="w-full h-32 object-cover rounded-lg mb-4">`;
            }
            previewThumbnail.classList.remove('hidden');
        } else {
            previewThumbnail.classList.add('hidden');
        }
    }

    // ===== INICIALIZAÇÃO DO PREVIEW =====
    
    // Disparar eventos iniciais para sincronizar o preview com os valores atuais
    if (titleInput) titleInput.dispatchEvent(new Event('input'));
    if (descriptionInput) descriptionInput.dispatchEvent(new Event('input'));
    if (iconInput) iconInput.dispatchEvent(new Event('input'));
    if (orderInput) orderInput.dispatchEvent(new Event('input'));
    if (activeCheckbox) activeCheckbox.dispatchEvent(new Event('change'));
});*/
</script>
@endpush