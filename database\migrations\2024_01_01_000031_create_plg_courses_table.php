<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_courses', function (Blueprint $table) {
            $table->id();
            
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->foreignId('user_id')->constrained('sys_users')->onDelete('cascade'); // Professor responsável
            $table->foreignId('category_id')->constrained('plg_categories')->onDelete('cascade');
            $table->boolean('featured')->default(false);
            $table->integer('order')->default(0);
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');

            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();

            $table->timestamps();
            $table->softDeletes();
            
            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            
            // Índices para performance
            $table->index('slug');
            $table->index(['company_id', 'status']); // Multi-tenancy + status
            $table->index(['status', 'featured']); // Status + destaque
            $table->index(['category_id', 'status']); // Categoria + status
            $table->index(['user_id', 'status']); // Professor + status
            $table->index(['featured', 'order']); // Destaque + ordem
            $table->index(['company_id', 'featured']); // Multi-tenancy + destaque
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_courses');
    }
};
