@props([
    'course' => null,
    'module' => null,
    'content' => null,
    'test' => null
])

@php
// Construir breadcrumb de forma performática
$items = [
    [
        'url' => route('student.dashboard'),
        'text' => 'Início',
        'icon' => 'fas fa-home'
    ]
];

// Adicionar curso
if ($course) {
    $items[] = [
        'url' => route('student.course.show', $course->slug),
        'text' => $course->title,
        'icon' => null
    ];
}

// Adicionar módulo
if ($module && $course) {
    $items[] = [
        'url' => route('student.module.details', [$course->slug, $module->slug]),
        'text' => $module->title,
        'icon' => null
    ];
}

// Detectar página atual e adicionar item específico
$currentRoute = request()->route()->getName();

if ($module && $course) {
    switch ($currentRoute) {
        case 'student.module.show':
            $items[] = ['url' => null, 'text' => 'Materiais', 'icon' => 'fas fa-book-open'];
            break;
        case 'student.module.quizzes':
            $items[] = ['url' => null, 'text' => 'Quizzes', 'icon' => 'fas fa-question-circle'];
            break;
    }

    // Verificar por URL específicas
    if (str_contains(request()->url(), '/conclusao')) {
        $items[] = ['url' => null, 'text' => 'Módulo Concluído', 'icon' => 'fas fa-check-circle'];
    }
}

// Adicionar conteúdo específico
if ($content && $module && $course) {
    // Se não estamos na página de materiais, adicionar link para materiais
    if ($currentRoute !== 'student.module.show') {
        $items[] = [
            'url' => route('student.module.show', [$course->slug, $module->slug]),
            'text' => 'Materiais',
            'icon' => 'fas fa-book-open'
        ];
    }
    $items[] = ['url' => null, 'text' => $content->title, 'icon' => null];
}

// Adicionar teste específico
if ($test && $module && $course) {
    // Se não estamos na página de quizzes, adicionar link para quizzes
    if ($currentRoute !== 'student.module.quizzes') {
        $items[] = [
            'url' => route('student.module.quizzes', [$course->slug, $module->slug]),
            'text' => 'Quizzes',
            'icon' => 'fas fa-question-circle'
        ];
    }

    // Determinar texto do teste baseado na rota
    $testText = match($currentRoute) {
        'student.quiz.history', 'student.test.history' => 'Histórico - ' . $test->name,
        'student.quiz.results' => 'Resultado - ' . $test->name,
        default => $test->name
    };

    $items[] = [
        'url' => null,
        'text' => $testText,
        'icon' => ($test->test_type ?? 'quiz') === 'quiz' ? 'fas fa-question-circle' : 'fas fa-graduation-cap'
    ];
}
@endphp

<div class="bg-gray-100 dark:bg-gray-800 mb-6">
    <div class="container mx-auto py-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                @foreach($items as $index => $item)
                    <li class="inline-flex items-center">
                        @if($index > 0)
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        @endif

                        @if($item['url'] && $index < count($items) - 1)
                            <a href="{{ $item['url'] }}"
                                class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white transition-colors">
                                @if($item['icon'])
                                    <i class="{{ $item['icon'] }} mr-2"></i>
                                @endif
                                {{ $item['text'] }}
                            </a>
                        @else
                            <span class="inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400" aria-current="page">
                                @if($item['icon'])
                                    <i class="{{ $item['icon'] }} mr-2"></i>
                                @endif
                                {{ $item['text'] }}
                            </span>
                        @endif
                    </li>
                @endforeach
            </ol>
        </nav>
    </div>
</div>