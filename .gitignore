# -------------------------------------------------------------------
# PHPUnit / Testes
# -------------------------------------------------------------------
.phpunit.cache
.phpunit.result.cache
coverage/

# -------------------------------------------------------------------
# Dependências
# -------------------------------------------------------------------
/vendor/
/node_modules

# -------------------------------------------------------------------
# Build / Public
# -------------------------------------------------------------------
/public/build
/public/hot
/public/storage

# -------------------------------------------------------------------
# Environment
# -------------------------------------------------------------------
.env
.env.backup
.env.production
/auth.json

# -------------------------------------------------------------------
# Laravel storage / cache / logs
# -------------------------------------------------------------------
/storage/*.key
/storage/pail
/storage/framework/views/
/storage/logs/
/storage/debugbar/
/storage/framework/cache/data/
/storage/framework/sessions/
/storage/framework/testing/

# -------------------------------------------------------------------
# PHP Tooling
# -------------------------------------------------------------------
.phpactor.json
.php-cs-fixer.cache
.php_cs.cache

# -------------------------------------------------------------------
# Homestead / Vagrant
# -------------------------------------------------------------------
Homestead.yaml
Homestead.json
.vagrant/

# -------------------------------------------------------------------
# NPM/Yarn debug logs
# -------------------------------------------------------------------
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# -------------------------------------------------------------------
# IDEs / Editores
# -------------------------------------------------------------------
/.idea/
/.vscode/
/fleet/
/.nova/
/.zed
*.swp
*~
.DS_Store
Thumbs.db
