<x-layouts.student title="{{ $course->name }}">
    <!-- Breadcrumb -->
    <x-student.breadcrumb :course="$course" />
    
    <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
    <main class="course-page bg-gray-50 dark:bg-gray-900">

        <!-- Page Shell -->
        <div class="container mx-auto mb-10">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
                <!-- COLUNA PRINCIPAL -->
                <section class="lg:col-span-9 course-content space-y-8">

                    <!-- Toolbar de Módulos -->
                    <x-student.course.modules-toolbar />

                    <!-- G<PERSON> de Módulos -->
                    <x-student.course.modules-grid :modules="$course->modules" :course="$course" />
                </section>

                <!-- SIDEBAR -->
                <aside class="lg:col-span-3 course-sidebar space-y-6">
                    <div class="sticky top-4">
                        <!-- Card de Informações do Curso -->
                        <x-student.course.info-card :course="$course" />

                        <!-- Card de Pontuação do Aluno -->
                        {{-- <x-student.course.student-score-card :course="$course" :student="auth('students')->user()" /> --}}
                    </div>
                </aside>
            </div>
        </div>
    </main>
</x-layouts.student>
