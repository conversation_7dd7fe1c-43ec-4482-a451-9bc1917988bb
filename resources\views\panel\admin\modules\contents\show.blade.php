@extends('layouts.panel')

@section('title', $content->title)
@section('page_title', 'Visualizar Conteúdo')

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Visualizar Conteúdo: ' . $content->title,
            'description' => 'Detalhes completos deste conteúdo.',
            'actions' => [
                [
                    'route' => route('admin.modules.edit', $module->id),
                    'text' => 'Voltar ao Módulo',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ],
                [
                    'route' => route('admin.modules.contents.edit', ['moduleId' => $module->id, 'content' => $content->id]),
                    'text' => 'Editar Conteúdo',
                    'icon' => 'fas fa-edit',
                    'class' => 'bg-blue-600 text-white',
                    'hover_class' => 'bg-blue-700'
                ]
            ]
        ])

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Detalhes do conteúdo -->
                <div class="bg-white dark:bg-zinc-800 p-6 rounded-lg border border-zinc-200 dark:border-zinc-700 shadow-sm">
                    <h3 class="text-lg font-semibold mb-4 text-zinc-900 dark:text-zinc-100">Informações do Conteúdo</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Módulo</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $module->title }}</p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Curso</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $module->course->title }}</p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Tipo de Conteúdo</h4>
                        <p>
                            @if($content->content_type == 'media')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                        Mídia
                                    </span>
                            @elseif($content->content_type == 'video')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                        Vídeo
                                    </span>
                            @elseif($content->content_type == 'link')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                        Link Externo
                                    </span>
                            @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                                        {{ $content->content_type }}
                                    </span>
                            @endif
                        </p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Duração</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $content->duration ? $content->duration . ' minutos' : 'Não especificada' }}</p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Ordem</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $content->order }}</p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Status</h4>
                        <p>
                            @if($content->active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                        Ativo
                                    </span>
                            @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                        Inativo
                                    </span>
                            @endif
                        </p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Criado em</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $content->created_at->format('d/m/Y H:i') }}</p>
                    </div>
                    
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Atualizado em</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $content->updated_at->format('d/m/Y H:i') }}</p>
                    </div>
                    </div>
                </div>
                
                <!-- Visualização do conteúdo -->
                <div class="bg-white dark:bg-zinc-800 p-6 rounded-lg border border-zinc-200 dark:border-zinc-700 shadow-sm lg:col-span-2">
                    <h3 class="text-lg font-semibold mb-4 text-zinc-900 dark:text-zinc-100">Conteúdo</h3>
                    
                    <div>
                    @if($content->content_type == 'media')
                            <div class="text-center p-8">
                                @php
                                    $media = \Plank\Mediable\Media::find($content->content);
                                @endphp
                                @if($media)
                                    <div class="max-w-md mx-auto">
                                        @if(in_array($media->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp']))
                                            <img src="{{ route('media.serve', ['id' => $media->id, 'width' => 400, 'height' => 300]) }}" 
                                                 alt="{{ $media->alt_text ?? $media->filename }}" 
                                                 class="w-full h-auto rounded-lg shadow-lg">
                                        @elseif($media->mime_type == 'application/pdf')
                                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                                                <i class="fas fa-file-pdf text-red-500 text-2xl mb-2"></i>
                                                <p class="text-red-700 dark:text-red-400">{{ $media->filename }}</p>
                                                <a href="{{ route('media.serve', ['id' => $media->id]) }}" target="_blank" 
                                                   class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 mt-2">
                                                    <i class="fas fa-download mr-2"></i> Baixar PDF
                                                </a>
                                            </div>
                                        @elseif(str_starts_with($media->mime_type, 'audio/'))
                                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                                                <i class="fas fa-music text-blue-500 text-2xl mb-2"></i>
                                                <p class="text-blue-700 dark:text-blue-400">{{ $media->filename }}</p>
                                                <audio controls class="w-full mt-2">
                                                    <source src="{{ route('media.serve', ['id' => $media->id]) }}" type="{{ $media->mime_type }}">
                                                    Seu navegador não suporta o elemento de áudio.
                                                </audio>
                                            </div>
                                        @else
                                            <div class="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <i class="fas fa-file text-gray-500 text-2xl mb-2"></i>
                                                <p class="text-gray-700 dark:text-gray-400">{{ $media->filename }}</p>
                                                <a href="{{ route('media.serve', ['id' => $media->id]) }}" target="_blank" 
                                                   class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mt-2">
                                                    <i class="fas fa-download mr-2"></i> Baixar Arquivo
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                @else
                                    <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                                        <p class="text-red-700 dark:text-red-400">Mídia não encontrada.</p>
                                    </div>
                                @endif
                        </div>
                    @elseif($content->content_type == 'video')
                            <div class="aspect-w-16 aspect-h-9">
                                <iframe class="w-full h-full rounded-lg" src="{{ $content->content }}" allowfullscreen></iframe>
                        </div>
                    @elseif($content->content_type == 'link')
                            <div class="text-center p-8">
                                <a href="{{ $content->content }}" target="_blank" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-external-link-alt mr-2"></i> Acessar Link
                            </a>
                                <p class="mt-3 text-zinc-500 dark:text-zinc-400 break-all">{{ $content->content }}</p>
                        </div>
                    @else
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 mt-0.5">
                                        <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700 dark:text-yellow-400">
                            Este tipo de conteúdo não possui visualização específica.
                                        </p>
                                    </div>
                                </div>
                        </div>
                    @endif
                    </div>
                    
                    <div class="mt-8 flex justify-end">
                        <button type="button" 
                                class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                onclick="document.getElementById('modal-delete').classList.remove('hidden')">
                            <i class="fas fa-trash mr-2"></i> Excluir Conteúdo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão -->
<div id="modal-delete" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-zinc-800 rounded-lg max-w-md w-full mx-4">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">Confirmar Exclusão</h3>
            <p class="mb-6 text-zinc-700 dark:text-zinc-300">Tem certeza que deseja excluir este conteúdo? Esta ação não pode ser desfeita.</p>
            
            <div class="flex justify-end gap-3">
                <button type="button" 
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-trends-primary/30"
                        onclick="document.getElementById('modal-delete').classList.add('hidden')">
                    Cancelar
                </button>
                
                <form action="{{ route('admin.modules.contents.destroy', ['moduleId' => $module->id, 'content' => $content->id]) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        Excluir
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection 