<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use Plank\Mediable\Media;
use Illuminate\Http\Request;
use App\Services\MediaService;
use App\Http\Resources\MediaResource;
use App\Http\Requests\MediaUploadRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use League\Glide\ServerFactory;

class MediaController extends Controller
{
    protected MediaService $mediaService;

    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    /**
     * Serve protected media files with authentication
     */
    public function serve(Request $request, $path)
    {
        // Verificar se o usuário está autenticado em qualquer guard
        $user = null;
        
        // Tentar autenticação no guard padrão (admin/teacher)
        if (Auth::check()) {
            $user = Auth::user();
        }
        
        // Tentar autenticação no guard de estudantes
        elseif (Auth::guard('students')->check()) {
            $user = Auth::guard('students')->user();
        }

        if (!$user) {
            abort(401, 'Acesso não autorizado');
        }

        // Verificar se o usuário tem permissão
        if ($user instanceof \App\Models\PlgStudent) {
            // Estudantes têm acesso
        } elseif ($user instanceof \App\Models\SysUser) {
            if (!in_array($user->role, ['super_admin', 'admin', 'teacher'])) {
                abort(403, 'Acesso negado');
            }
        } else {
            abort(403, 'Acesso negado');
        }

        // Sanitizar o caminho para evitar travessia de diretório
        $path = $this->sanitizePath($path);
        if ($path === false) {
            abort(400, 'Caminho inválido');
        }

        // Verificar se o arquivo existe
        $fullPath = storage_path('app/public/' . $path);
        
        // Validar se o caminho final está dentro do diretório permitido
        $realPath = realpath($fullPath);
        $storagePath = realpath(storage_path('app/public'));
        
        if (!$realPath || !str_starts_with($realPath, $storagePath)) {
            abort(403, 'Acesso negado - Tentativa de acesso a diretório não permitido');
        }

        if (!file_exists($fullPath)) {
            abort(404, 'Arquivo não encontrado');
        }

        // Verificar se é uma imagem e se há parâmetros de manipulação
        $isImage = str_starts_with(mime_content_type($fullPath), 'image/');
        $hasImageParams = $request->hasAny(['w', 'h', 'fit', 'fm', 'q']);

        if ($isImage && $hasImageParams) {
            return $this->serveProcessedImage($request, $path);
        }

        // Servir arquivo original
        return $this->serveOriginalFile($fullPath, $path);
    }

    /**
     * Display the media manager page
     */
    public function index()
    {
        return view('panel.admin.media.index');
    }

    /**
     * Load media items via AJAX
     */
    public function loadMore(Request $request)
    {
        try {
            $filters = $request->only(['search', 'type', 'date_from', 'date_to', 'orphaned', 'per_page', 'sort']);
            $page = $request->input('page', 1);
            
            $mediaList = $this->mediaService->getMediaWithFilters($filters, $page);
            
            return response()->json([
                'items' => MediaResource::collection($mediaList->items()),
                'pagination' => $this->formatPagination($mediaList)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao carregar mídias'], 500);
        }
    }

    /**
     * Store a new media file
     */
    public function store(MediaUploadRequest $request)
    {
        try {
            // Verificar se o arquivo foi enviado
            if (!$request->hasFile('file')) {
                return response()->json(['error' => 'Nenhum arquivo foi enviado'], 400);
            }

            $file = $request->file('file');

            // Verificar se o arquivo é válido
            if (!$file->isValid()) {
                return response()->json(['error' => 'Arquivo inválido'], 400);
            }

            $options = $request->only(['alt_text', 'caption', 'mediable_type', 'tag']);

            \Log::info('Iniciando upload', [
                'filename' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType(),
                'options' => $options
            ]);

            $media = $this->mediaService->uploadMedia($file, $options);

            return response()->json(new MediaResource($media), 201);

        } catch (\Exception $e) {
            \Log::error('Erro no upload', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $request->hasFile('file') ? $request->file('file')->getClientOriginalName() : null,
                'user' => auth()->id()
            ]);

            return response()->json([
                'error' => 'Erro no upload: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get media info
     */
    public function info(Media $media)
    {
        return response()->json(new MediaResource($media));
    }

    /**
     * Update media information
     */
    public function update(Request $request, Media $media)
    {
        $request->validate([
            'filename' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'caption' => 'nullable|string|max:500',
        ]);

        try {
            $updatedMedia = $this->mediaService->updateMedia($media, $request->all());
            
            return response()->json([
                'success' => true,
                'message' => 'Mídia atualizada com sucesso',
                'media' => new MediaResource($updatedMedia)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao atualizar'], 500);
        }
    }

    /**
     * Replace media file
     */
    public function replace(MediaUploadRequest $request, Media $media)
    {
        try {
            $updatedMedia = $this->mediaService->replaceMediaFile($media, $request->file('file'));

            return response()->json([
                'success' => true,
                'message' => 'Arquivo substituído com sucesso',
                'media' => new MediaResource($updatedMedia)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao substituir arquivo'], 500);
        }
    }

    /**
     * Delete a media file
     */
    public function destroy(Media $media)
    {
        try {
            $this->mediaService->deleteMedia($media);
            
            return response()->json([
                'success' => true,
                'message' => 'Mídia excluída com sucesso'
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao excluir'], 500);
        }
    }

    /**
     * Delete multiple media files
     */
    public function deleteMultiple(Request $request)
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:media,id'
        ]);

        try {
            $results = $this->mediaService->deleteMultipleMedia($request->input('ids'));

            return response()->json([
                'success' => true,
                'deleted' => $results['deleted'],
                'message' => "{$results['deleted']} mídia(s) excluída(s) com sucesso."
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao excluir mídias'], 500);
        }
    }

    /**
     * Get media statistics
     */
    public function stats()
    {
        try {
            $stats = $this->mediaService->getMediaStats();
            return response()->json($stats);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao obter estatísticas'], 500);
        }
    }

    /**
     * Clean orphaned media (database records without physical files)
     */
    public function cleanOrphaned()
    {
        try {
            $results = $this->mediaService->cleanOrphanedMedia();
            
            return response()->json([
                'success' => true,
                'deleted' => $results['deleted'],
                'errors' => $results['errors'],
                'message' => "{$results['deleted']} mídia(s) órfã(s) removida(s) com sucesso."
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao limpar mídias órfãs'], 500);
        }
    }

    /**
     * Serve processed image using Glide
     */
    private function serveProcessedImage(Request $request, $path)
    {
        try {
            // Verificar se o arquivo existe
            $fullPath = storage_path('app/public/' . $path);
            if (!file_exists($fullPath)) {
                throw new \Exception("Arquivo não encontrado: {$path}");
            }

            // Criar diretório de cache se não existir
            $cachePath = storage_path('app/public/.glide-cache');
            if (!file_exists($cachePath)) {
                mkdir($cachePath, 0755, true);
            }

            // Configurar o servidor Glide
            $server = ServerFactory::create([
                'source' => Storage::disk('public')->getDriver(),
                'cache' => Storage::disk('public')->getDriver(),
                'cache_path_prefix' => '.glide-cache',
                'base_url' => 'media',
            ]);

            // Gerar o caminho do cache para a imagem processada
            $cachePath = $server->getCachePath($path, $request->all());

            // Verificar se a imagem já está no cache
            if (!$server->cacheFileExists($path, $request->all())) {
                // Se não estiver no cache, processar a imagem
                $server->makeImage($path, $request->all());
            }

            // Servir a imagem do cache
            $cachedImagePath = storage_path('app/public/' . $cachePath);

            // Determinar o tipo MIME baseado no formato solicitado
            $format = $request->get('fm', pathinfo($path, PATHINFO_EXTENSION));
            $mimeType = $this->getMimeTypeForFormat($format);

            return response()->file($cachedImagePath, [
                'Content-Type' => $mimeType,
                'Cache-Control' => 'public, max-age=31536000',
            ]);

        } catch (\Exception $e) {
            \Log::error('Erro ao processar imagem com Glide', [
                'path' => $path,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Se falhar, tentar servir o arquivo original
            $fullPath = storage_path('app/public/' . $path);
            if (file_exists($fullPath)) {
                return $this->serveOriginalFile($fullPath, $path);
            }

            // Se o arquivo não existir, retornar 404
            abort(404, 'Imagem não encontrada');
        }
    }
    
    /**
     * Obtém o tipo MIME para um formato de imagem
     */
    private function getMimeTypeForFormat(string $format): string
    {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'avif' => 'image/avif',
        ];
        
        return $mimeTypes[strtolower($format)] ?? 'image/jpeg';
    }

    /**
     * Serve original file
     */
    private function serveOriginalFile($fullPath, $path)
    {
        $mimeType = mime_content_type($fullPath);
        $fileName = basename($path);

        return Response::file($fullPath, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $fileName . '"',
            'Cache-Control' => 'public, max-age=31536000', // 1 ano
        ]);
    }

    /**
     * Check if file is an image
     */
    private function isImage($filePath)
    {
        $mimeType = mime_content_type($filePath);
        return str_starts_with($mimeType, 'image/');
    }

    /**
     * Format pagination data
     */
    private function formatPagination($paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
            'has_more_pages' => $paginator->hasMorePages(),
        ];
    }

    /**
     * Get available media types for filtering
     */
    public function getMediaTypes()
    {
        try {
            $types = $this->mediaService->getAvailableMediaTypes();
            return response()->json($types);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao obter tipos de mídia'], 500);
        }
    }

    /**
     * Sanitiza o caminho para evitar travessia de diretório
     * 
     * @param string $path
     * @return string|false Caminho sanitizado ou false se inválido
     */
    private function sanitizePath($path)
    {
        // Remover caracteres de controle e espaços
        $path = trim($path);
        
        // Verificar se o caminho contém sequências suspeitas
        $dangerousPatterns = [
            '../', // Travessia de diretório para cima
            '..\\', // Travessia de diretório para cima (Windows)
            '~/', // Diretório home
            '/./', // Diretório atual
            '/.\\', // Diretório atual (Windows)
            '//', // Caminhos duplicados
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (strpos($path, $pattern) !== false) {
                return false;
            }
        }
        
        // Normalizar separadores de diretório
        $path = str_replace('\\', '/', $path);
        
        // Remover múltiplos separadores
        $path = preg_replace('#/+#', '/', $path);
        
        // Remover qualquer / inicial para evitar caminhos absolutos
        $path = ltrim($path, '/');
        
        return $path;
    }
} 