<?php

namespace Database\Factories;

use App\Models\PlgCategories;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PlgCategoriesFactory extends Factory
{
    protected $model = PlgCategories::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->words(rand(1, 3), true);
        return [
            'title' => ucwords($name),
            'slug' => Str::slug($name),
            'description' => $this->faker->paragraph(),
            'icon' => $this->faker->randomElement(['fa-book', 'fa-microscope', 'fa-heartbeat', 'fa-brain', 'fa-dna', 'fa-pills', 'fa-hospital', 'fa-stethoscope']),
            'order' => $this->faker->numberBetween(1, 100),
            'company_id' => 1
        ];
    }
} 