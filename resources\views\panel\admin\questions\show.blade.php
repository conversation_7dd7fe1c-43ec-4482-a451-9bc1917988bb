@extends('layouts.panel')

@section('title', 'Detal<PERSON> da Questão')
@section('page_title', 'Detal<PERSON> da Questão')

@section('content')
<div class="bg-white dark:bg-zinc-800 shadow-sm rounded-lg overflow-hidden mb-6">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">
                Questão #{{ $question->id }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.questions.edit', $question->id) }}" 
                   class="px-4 py-2 text-sm font-medium text-white bg-yellow-500 rounded-md hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    <i class="fas fa-edit mr-2"></i> Editar
                </a>
                <a href="{{ route('admin.questions.index') }}" 
                   class="px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-trends-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Voltar
                </a>
            </div>
        </div>

        <div class="space-y-6">
            <!-- Enunciado da Questão -->
            <div class="border-b border-zinc-200 dark:border-zinc-700 pb-4">
                <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2">Enunciado</h3>
                <p class="text-zinc-700 dark:text-zinc-300 whitespace-pre-line">{{ $question->question }}</p>
                
                @if($question->hasMedia('question_image'))
                <div class="mt-4">
                    <h4 class="text-md font-medium text-zinc-900 dark:text-zinc-100 mb-2">Imagem da Questão</h4>
                    @php
                        $media = $question->firstMedia('question_image');
                        $imageUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=400&h=300&fit=crop&fm=webp';
                    @endphp
                    <img src="{{ $imageUrl }}" alt="Imagem da Questão" class="max-w-full max-h-64 rounded-lg">
                </div>
                @endif
            </div>

            <!-- Categorias -->
            <div class="border-b border-zinc-200 dark:border-zinc-700 pb-4">
                <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2">Categorias</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach($question->categories as $category)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-trends-primary/10 text-trends-primary">
                            {{ $category->title }}
                        </span>
                    @endforeach
                </div>
            </div>

            <!-- Alternativas -->
            <div class="border-b border-zinc-200 dark:border-zinc-700 pb-4">
                <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2">Alternativas</h3>
                <div class="space-y-3">
                    @foreach($question->answers->sortBy('answer_number') as $answer)
                        <div class="p-3 rounded-md {{ $answer->correct ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-zinc-50 dark:bg-zinc-800/50 border border-zinc-200 dark:border-zinc-700' }}">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 pt-0.5">
                                    <span class="inline-block h-6 w-6 rounded-full {{ $answer->correct ? 'bg-green-500' : 'bg-zinc-400' }} text-white text-center font-medium">
                                        {{ $answer->answer_number }}
                                    </span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-zinc-700 dark:text-zinc-300">
                                        {{ $answer->answer }}
                                    </p>
                                    @if($answer->hasMedia('answer_image'))
                                    <div class="mt-2">
                                        @php
                                            $media = $answer->firstMedia('answer_image');
                                            $imageUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=300&h=200&fit=crop&fm=webp';
                                        @endphp
                                        <img src="{{ $imageUrl }}" alt="Imagem da Alternativa {{ $answer->answer_number }}" class="max-w-full max-h-48 rounded-lg">
                                    </div>
                                    @endif
                                    @if($answer->correct && $answer->explanation)
                                        <div class="mt-2 text-sm text-green-700 dark:text-green-400 italic bg-green-50 dark:bg-green-900/20 p-2 rounded border border-green-100 dark:border-green-800">
                                            <strong>Explicação:</strong> {{ $answer->explanation }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Informações Adicionais -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h3 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Questão Gratuita</h3>
                    <p class="mt-1 text-zinc-800 dark:text-zinc-200">
                        @if($question->free)
                            <span class="text-green-500"><i class="fas fa-check-circle mr-1"></i> Sim</span>
                        @else
                            <span class="text-red-500"><i class="fas fa-times-circle mr-1"></i> Não</span>
                        @endif
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Origem</h3>
                    <p class="mt-1 text-zinc-800 dark:text-zinc-200">
                        @if($question->imported)
                            <span class="text-blue-500"><i class="fas fa-file-import mr-1"></i> Importada via CSV</span>
                        @else
                            <span class="text-green-500"><i class="fas fa-pencil-alt mr-1"></i> Criada manualmente</span>
                        @endif
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Data de Criação</h3>
                    <p class="mt-1 text-zinc-800 dark:text-zinc-200">
                        {{ $question->created_at->format('d/m/Y H:i:s') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 