<?php

namespace Database\Factories;

use App\Models\PlgCategories;
use App\Models\PlgCourse;
use App\Models\SysCompany;
use App\Models\SysUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PlgCourseFactory extends Factory
{
    protected $model = PlgCourse::class;

    public function definition()
    {
        $title = $this->faker->sentence(3);
        $slug = Str::slug($title);

        // Get random category ID or create one if none exists
        $categoryIds = PlgCategories::pluck('id')->toArray();
        if (empty($categoryIds)) {
            $category = PlgCategories::factory()->create();
            $categoryIds = [$category->id];
        }

        // Get random teacher ID or create one if none exists
        $teacherIds = SysUser::where('role', 'teacher')->pluck('id')->toArray();
        if (empty($teacherIds)) {
            $teacher = SysUser::factory()->create(['role' => 'teacher']);
            $teacherIds = [$teacher->id];
        }

        // Get random company ID or create one if none exists
        $companyIds = SysCompany::pluck('id')->toArray();
        if (empty($companyIds)) {
            $company = SysCompany::factory()->create();
            $companyIds = [$company->id];
        }

        return [
            'title' => $title,
            'slug' => $slug,
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->paragraph(),
            'category_id' => $this->faker->randomElement($categoryIds),
            'user_id' => $this->faker->randomElement($teacherIds),
            'company_id' => $this->faker->randomElement($companyIds),
            'status' => $this->faker->randomElement(['draft', 'published']),
            'featured' => $this->faker->boolean(20),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
} 