export class MediaRenderer {
    // ==================== RENDERIZAÇÃO PRINCIPAL ====================

    static renderMediaGrid(container, items, selectedIds, options = {}) {
        if (!container) return;
        
        container.innerHTML = '';
        
        if (!items.length) {
            container.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="mx-auto w-16 h-16 bg-zinc-100 dark:bg-zinc-700 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-images text-2xl text-zinc-400 dark:text-zinc-500"></i>
                    </div>
                    <p class="text-zinc-500 dark:text-zinc-400 text-lg font-medium">Nenhuma mídia encontrada</p>
                    <p class="text-zinc-400 dark:text-zinc-500 text-sm mt-1">Tente ajustar os filtros ou fazer upload de novos arquivos</p>
                </div>
            `;
            return;
        }

        items.forEach(media => {
            const item = this.createMediaItem(media, selectedIds.has(media.id), options);
            container.appendChild(item);
        });
    }

    static createMediaItem(media, isSelected, options = {}) {
        const item = document.createElement('div');
        
        // Adicionar classe especial para mídias órfãs
        const isOrphaned = media.exists === false;
        const baseClasses = 'media-item bg-white dark:bg-zinc-800 border-2 border-zinc-200 dark:border-zinc-700 rounded-xl overflow-hidden cursor-pointer transition-all duration-200 relative group hover:shadow-lg';
        const orphanedClasses = isOrphaned ? 'border-red-300 bg-red-50 dark:bg-red-900/20' : 'border-zinc-200 dark:border-zinc-700 hover:border-trends-primary dark:hover:border-trends-primary';
        const selectedClasses = isSelected ? 'selected border-trends-primary bg-trends-primary/5 dark:bg-trends-primary/10 shadow-lg' : '';
        
        item.className = `${baseClasses} ${orphanedClasses} ${selectedClasses}`;
        item.dataset.id = media.id;

        // Preview
        const preview = this.createPreview(media, isOrphaned);

        // Nome do arquivo
        const shortName = media.filename.length > 20 ? 
            media.filename.substring(0, 17) + '...' : 
            media.filename;

        // Badge para mídias órfãs
        const orphanedBadge = isOrphaned ? this.createOrphanedBadge() : '';

        // Ações (só se não for órfã)
        const actions = !isOrphaned ? this.createActions(media) : '';

        item.innerHTML = `
            ${orphanedBadge}
            ${preview}
            <div class="p-3">
                <div class="text-sm text-zinc-700 dark:text-zinc-300 font-medium truncate" title="${media.filename}">
                    ${shortName}
                </div>
                <div class="text-xs text-zinc-500 dark:text-zinc-400 mt-1">
                    ${media.size_human || ''}
                </div>
            </div>
            ${actions}
        `;

        return item;
    }

    // ==================== COMPONENTES ====================

    static createPreview(media, isOrphaned) {
        if (media.is_image) {
            if (isOrphaned) {
                return `
                    <div class="aspect-square bg-red-50 dark:bg-red-900/20 border-2 border-dashed border-red-300 dark:border-red-700 flex flex-col items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-500 text-xl mb-1"></i>
                        <span class="text-xs text-red-600 dark:text-red-400 font-medium">Arquivo não encontrado</span>
                    </div>
                `;
            } else {
                return `
                    <div class="aspect-square bg-zinc-100 dark:bg-zinc-700">
                        <img src="${media.url}" alt="${media.filename}" class="w-full h-full object-cover" 
                             onerror="this.parentNode.innerHTML='<div class=\\'w-full h-full bg-zinc-100 dark:bg-zinc-700 flex items-center justify-center\\'>
                                <i class=\\'fas fa-image text-zinc-400 text-2xl\\'></i>
                             </div>'">
                    </div>
                `;
            }
        } else {
            const iconClass = this.getFileIcon(media.mime_type);
            if (isOrphaned) {
                return `
                    <div class="aspect-square bg-red-50 dark:bg-red-900/20 border-2 border-dashed border-red-300 dark:border-red-700 flex flex-col items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-500 text-xl mb-1"></i>
                        <span class="text-xs text-red-600 dark:text-red-400 font-medium">Arquivo não encontrado</span>
                    </div>
                `;
            } else {
                return `
                    <div class="aspect-square bg-zinc-100 dark:bg-zinc-700 flex items-center justify-center">
                        <i class="${iconClass} text-3xl"></i>
                    </div>
                `;
            }
        }
    }

    static createSelectionCheckbox(isSelected) {
        // Removido - não há necessidade de checkbox visual pois a seleção é indicada pela borda
        return '';
    }

    static createActions(media) {
        return `
            <div class="media-actions absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
                <button class="view-btn w-8 h-8 bg-white/90 dark:bg-zinc-800/90 backdrop-blur-sm rounded-full shadow-lg text-zinc-600 dark:text-zinc-300 hover:text-trends-primary dark:hover:text-trends-primary hover:bg-white dark:hover:bg-zinc-800 transition-all duration-200" title="Visualizar">
                    <i class="fas fa-eye text-xs"></i>
                </button>
                <button class="delete-btn w-8 h-8 bg-white/90 dark:bg-zinc-800/90 backdrop-blur-sm rounded-full shadow-lg text-zinc-600 dark:text-zinc-300 hover:text-red-500 hover:bg-white dark:hover:bg-zinc-800 transition-all duration-200" title="Excluir">
                    <i class="fas fa-trash text-xs"></i>
                </button>
            </div>
        `;
    }

    static createOrphanedBadge() {
        return `
            <div class="absolute top-2 left-2 z-20">
                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full shadow-lg backdrop-blur-sm">
                    <i class="fas fa-exclamation-triangle mr-1"></i>Órfã
                </span>
            </div>
        `;
    }

    // ==================== CONTROLES DE SELEÇÃO ====================

    static renderSelectionControls(container, selectedCount, options = {}) {
        if (!container) return;

        // Procurar pelo container de controles fixo
        let controlsContainer = document.getElementById('selectionControlsContainer');
        
        // Se não encontrar o container fixo, usar o comportamento antigo (fallback)
        if (!controlsContainer) {
            return this.renderSelectionControlsOld(container, selectedCount, options);
        }

        // Verificar se já existe uma barra de controles no container fixo
        let existingControls = controlsContainer.querySelector('.selection-controls');
        if (existingControls) {
            this.updateSelectionControls(existingControls, selectedCount, options);
            return existingControls;
        }

        // Criar controles no container fixo
        const controls = document.createElement('div');
        controls.className = 'selection-controls';
        
        // Determinar se é modal e se permite múltiplos baseado no target
        const isModal = options.isModal || false;
        const target = options.target || 'thumb';
        const allowMultiple = MediaRenderer.isMultipleTarget(target);
        
        controls.innerHTML = `
            <div class="flex items-center justify-between flex-wrap gap-3">
                <div class="flex items-center gap-3 flex-wrap">
                    <span class="selection-count text-sm font-medium text-zinc-700 dark:text-zinc-300">0 itens selecionados</span>
                    ${allowMultiple ? `
                        <button type="button" class="select-page-btn text-sm text-trends-primary hover:text-trends-primary/80 underline">
                            Selecionar todos desta página
                        </button>
                        <button type="button" class="select-all-btn text-sm text-trends-primary hover:text-trends-primary/80 underline hidden">
                            Selecionar todos os itens
                        </button>
                    ` : ''}
                    <button type="button" class="clear-selection-btn text-sm text-zinc-600 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200 underline">
                        Limpar seleção
                    </button>
                </div>
                <div class="flex items-center gap-2">
                    ${isModal ? `
                        <button type="button" class="confirm-selection-btn bg-trends-primary text-white px-4 py-2 rounded-md text-sm hover:bg-trends-primary/90 disabled:opacity-50 transition-colors" disabled>
                            <i class="fas fa-check mr-2"></i> Selecionar
                        </button>
                    ` : `
                        <button type="button" class="delete-selected-btn bg-red-500 text-white px-4 py-2 rounded-md text-sm hover:bg-red-600 disabled:opacity-50 transition-colors" disabled>
                            <i class="fas fa-trash mr-2"></i> Excluir selecionados
                        </button>
                    `}
                </div>
            </div>
        `;

        controlsContainer.appendChild(controls);
        this.updateSelectionControls(controls, selectedCount, options);
        return controls;
    }

    // Comportamento antigo como fallback
    static renderSelectionControlsOld(container, selectedCount, options = {}) {
        // Verificar se já existe uma barra de controles
        let existingControls = document.querySelector('.selection-controls');
        if (existingControls) {
            this.updateSelectionControls(existingControls, selectedCount, options);
            return existingControls;
        }

        // Criar controles fixos na tela (comportamento antigo)
        const controls = document.createElement('div');
        controls.className = 'selection-controls bg-white border border-gray-300 rounded-lg p-4 shadow-lg';
        controls.style.cssText = `
            position: fixed;
            bottom: 2%;
            left: 0;
            right: 0;
            width: 96%;
            margin: 0 auto;
            z-index: 99999;
            transform: translateY(100%);
            opacity: 0;
            transition: all 0.3s ease-in-out;
        `;
        
        // Determinar se é modal e se permite múltiplos baseado no target
        const isModal = options.isModal || false;
        const target = options.target || 'thumb';
        const allowMultiple = MediaRenderer.isMultipleTarget(target);
        
        controls.innerHTML = `
            <div class="flex items-center justify-between flex-wrap gap-3">
                <div class="flex items-center gap-3 flex-wrap">
                    <span class="selection-count text-sm font-medium text-gray-700">0 itens selecionados</span>
                    ${allowMultiple ? `
                        <button type="button" class="select-page-btn text-sm text-blue-600 hover:text-blue-800 underline">
                            Selecionar todos desta página
                        </button>
                        <button type="button" class="select-all-btn text-sm text-blue-600 hover:text-blue-800 underline hidden">
                            Selecionar todos os itens
                        </button>
                    ` : ''}
                    <button type="button" class="clear-selection-btn text-sm text-gray-600 hover:text-gray-800 underline">
                        Limpar seleção
                    </button>
                </div>
                <div class="flex items-center gap-2">
                    ${isModal ? `
                        <button type="button" class="confirm-selection-btn bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600 disabled:opacity-50 transition-colors" disabled>
                            <i class="fas fa-check mr-2"></i> Selecionar
                        </button>
                    ` : `
                        <button type="button" class="delete-selected-btn bg-red-500 text-white px-4 py-2 rounded text-sm hover:bg-red-600 disabled:opacity-50 transition-colors" disabled>
                            <i class="fas fa-trash mr-2"></i> Excluir selecionados
                        </button>
                    `}
                </div>
            </div>
        `;

        // Adicionar ao body (não ao container)
        document.body.appendChild(controls);
        this.updateSelectionControls(controls, selectedCount, options);
        return controls;
    }

    static updateSelectionControls(controls, selectedCount, options = {}) {
        if (!controls) return;

        const countEl = controls.querySelector('.selection-count');
        const deleteBtn = controls.querySelector('.delete-selected-btn');
        const confirmBtn = controls.querySelector('.confirm-selection-btn');
        const selectPageBtn = controls.querySelector('.select-page-btn');
        const selectAllBtn = controls.querySelector('.select-all-btn');

        // Determinar se é modal e lógica de exibição baseado no target
        const isModal = options.isModal || false;
        const target = options.target || 'thumb';
        const allowMultiple = MediaRenderer.isMultipleTarget(target);
        const minSelectionToShow = isModal ? 1 : 2; // Modal: 1+, Standalone: 2+

        // Atualizar contador
        if (countEl) {
            countEl.textContent = `${selectedCount} ${selectedCount === 1 ? 'item selecionado' : 'itens selecionados'}`;
        }

        // Atualizar botão de ação principal
        if (deleteBtn) {
            deleteBtn.disabled = selectedCount === 0;
        }
        if (confirmBtn) {
            confirmBtn.disabled = selectedCount === 0;
        }

        // Verificar se é o container fixo ou o antigo (overlay)
        const controlsContainer = document.getElementById('selectionControlsContainer');
        const isFixedContainer = controlsContainer && controlsContainer.contains(controls);

        // Mostrar/esconder com animação
        if (selectedCount >= minSelectionToShow) {
            if (isFixedContainer) {
                // Para container fixo, apenas mostrar/esconder o container
                controlsContainer.classList.remove('hidden');
            } else {
                // Para overlay antigo, usar transformações
                controls.style.transform = 'translateY(0)';
                controls.style.opacity = '1';
            }
            
            // Mostrar opção "Selecionar todos" apenas se permite múltiplos e há mais itens
            if (selectAllBtn && allowMultiple && options.totalItems && options.totalItems > options.pageItems) {
                selectAllBtn.classList.remove('hidden');
                selectAllBtn.textContent = `Todos os ${options.totalItems} itens`;
            } else if (selectAllBtn) {
                selectAllBtn.classList.add('hidden');
            }
        } else {
            if (isFixedContainer) {
                // Para container fixo, esconder o container
                controlsContainer.classList.add('hidden');
            } else {
                // Para overlay antigo, usar transformações
                controls.style.transform = 'translateY(100%)';
                controls.style.opacity = '0';
            }
        }
    }

    static hideSelectionControls() {
        // Primeiro verificar se há container fixo
        const controlsContainer = document.getElementById('selectionControlsContainer');
        if (controlsContainer) {
            controlsContainer.classList.add('hidden');
            // Limpar conteúdo para economizar memória
            controlsContainer.innerHTML = '<!-- Controles serão renderizados dinamicamente aqui -->';
            return;
        }

        // Fallback para o comportamento antigo (overlay)
        const controls = document.querySelector('.selection-controls');
        if (controls) {
            controls.style.transform = 'translateY(100%)';
            controls.style.opacity = '0';
            
            // Remover após a animação
            setTimeout(() => {
                if (controls.parentNode) {
                    controls.parentNode.removeChild(controls);
                }
            }, 300);
        }
    }

    static isMultipleTarget(target) {
        // Targets que permitem múltiplos
        const multipleTargets = ['gallery', 'images', 'files', 'media'];
        // Targets que são únicos
        const singleTargets = ['thumb', 'thumbnail', 'avatar', 'logo', 'cover', 'image'];
        
        if (multipleTargets.includes(target)) {
            return true;
        }
        
        if (singleTargets.includes(target)) {
            return false;
        }
        
        // Fallback: se contém palavras-chave de múltiplos
        const targetLower = target.toLowerCase();
        return targetLower.includes('gallery') || targetLower.includes('multiple') || targetLower.includes('varios');
    }

    // ==================== PAGINAÇÃO ====================

    static renderPagination(container, pagination, onPageChange) {
        if (!container) return;

        // Procurar pelo container de paginação existente
        let paginationContainer = document.querySelector('.media-pagination');
        
        if (!paginationContainer) {
            paginationContainer = document.createElement('div');
            paginationContainer.className = 'media-pagination';
            // Inserir após o container principal
            const mediaContent = document.getElementById('media-content');
            if (mediaContent && mediaContent.parentNode) {
                mediaContent.parentNode.insertBefore(paginationContainer, mediaContent.nextSibling);
            }
        }

        if (pagination.last_page <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        const nav = document.createElement('nav');
        nav.className = 'flex items-center justify-between border-t border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 px-4 py-3';
        
        nav.innerHTML = `
            <!-- Mobile Navigation -->
            <div class="flex flex-1 justify-between sm:hidden">
                ${pagination.current_page > 1 ? 
                    `<button class="prev-page-btn px-4 py-2 text-sm bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-colors">
                        <i class="fas fa-chevron-left mr-2"></i>Anterior
                    </button>` : 
                    '<span></span>'
                }
                ${pagination.current_page < pagination.last_page ? 
                    `<button class="next-page-btn px-4 py-2 text-sm bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-colors">
                        Próximo<i class="fas fa-chevron-right ml-2"></i>
                    </button>` : 
                    '<span></span>'
                }
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-zinc-700 dark:text-zinc-300">
                        Mostrando <span class="font-medium">${pagination.from || 0}</span> a <span class="font-medium">${pagination.to || 0}</span> de <span class="font-medium">${pagination.total}</span> resultados
                    </p>
                </div>
                <div class="navigation-controls flex items-center gap-2"></div>
            </div>
        `;

        // Controles de navegação desktop
        const navigationContainer = nav.querySelector('.navigation-controls');
        if (navigationContainer) {
            this.createNavigationButtons(navigationContainer, pagination, onPageChange);
        }

        // Eventos de navegação mobile
        nav.querySelector('.prev-page-btn')?.addEventListener('click', () => {
            if (pagination.current_page > 1) {
                onPageChange(pagination.current_page - 1);
            }
        });

        nav.querySelector('.next-page-btn')?.addEventListener('click', () => {
            if (pagination.current_page < pagination.last_page) {
                onPageChange(pagination.current_page + 1);
            }
        });

        paginationContainer.innerHTML = '';
        paginationContainer.appendChild(nav);
    }

    static createNavigationButtons(container, pagination, onPageChange) {
        const { current_page, last_page } = pagination;
        
        // Botão Primeira Página
        if (current_page > 1) {
            const firstBtn = this.createNavButton('first', '<<', 'Primeira página');
            firstBtn.addEventListener('click', () => onPageChange(1));
            container.appendChild(firstBtn);
        }

        // Botão Página Anterior
        if (current_page > 1) {
            const prevBtn = this.createNavButton('prev', '<', 'Página anterior');
            prevBtn.addEventListener('click', () => onPageChange(current_page - 1));
            container.appendChild(prevBtn);
        }

        // Botões de páginas numeradas
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(last_page, current_page + 2);

        // Adicionar "..." se necessário no início
        if (startPage > 1) {
            if (startPage > 2) {
                const pageBtn = this.createPageButton(1, false);
                pageBtn.addEventListener('click', () => onPageChange(1));
                container.appendChild(pageBtn);
                
                if (startPage > 3) {
                    container.appendChild(this.createEllipsis());
                }
            }
        }

        // Páginas numeradas
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPageButton(i, i === current_page);
            if (i !== current_page) {
                pageBtn.addEventListener('click', () => onPageChange(i));
            }
            container.appendChild(pageBtn);
        }

        // Adicionar "..." se necessário no final
        if (endPage < last_page) {
            if (endPage < last_page - 1) {
                if (endPage < last_page - 2) {
                    container.appendChild(this.createEllipsis());
                }
                
                const pageBtn = this.createPageButton(last_page, false);
                pageBtn.addEventListener('click', () => onPageChange(last_page));
                container.appendChild(pageBtn);
            }
        }

        // Botão Próxima Página
        if (current_page < last_page) {
            const nextBtn = this.createNavButton('next', '>', 'Próxima página');
            nextBtn.addEventListener('click', () => onPageChange(current_page + 1));
            container.appendChild(nextBtn);
        }

        // Botão Última Página
        if (current_page < last_page) {
            const lastBtn = this.createNavButton('last', '>>', 'Última página');
            lastBtn.addEventListener('click', () => onPageChange(last_page));
            container.appendChild(lastBtn);
        }
    }

    static createNavButton(type, text, title) {
        const button = document.createElement('button');
        button.className = 'relative inline-flex items-center px-3 py-2 text-sm font-medium text-zinc-500 dark:text-zinc-400 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-trends-primary hover:text-white hover:border-trends-primary transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-trends-primary/30';
        button.innerHTML = text;
        button.title = title;
        button.dataset.type = type;
        return button;
    }

    static createPageButton(pageNumber, isActive) {
        const button = document.createElement('button');
        if (isActive) {
            button.className = 'relative inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-trends-primary border border-trends-primary rounded-md cursor-default';
        } else {
            button.className = 'relative inline-flex items-center px-4 py-2 text-sm font-medium text-zinc-500 dark:text-zinc-400 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-trends-primary hover:text-white hover:border-trends-primary transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-trends-primary/30';
        }
        button.innerHTML = pageNumber;
        button.title = `Página ${pageNumber}`;
        return button;
    }

    static createEllipsis() {
        const span = document.createElement('span');
        span.className = 'relative inline-flex items-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300';
        span.innerHTML = '...';
        return span;
    }

    // ==================== FILTROS ====================

    static renderCategoryFilters(container, categories, onCategoryChange) {
        if (!container) return;
        
        const categoryList = container.querySelector('ul');
        if (!categoryList) return;
        
        // Limpar filtros existentes
        categoryList.innerHTML = '';
        
        // Adicionar novos filtros
        Object.entries(categories).forEach(([key, category]) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <button class="media-category-btn w-full text-left px-3 py-2 rounded-md hover:bg-zinc-100 dark:hover:bg-zinc-800 flex items-center justify-between transition-colors ${key === 'all' ? 'bg-trends-primary/10 text-trends-primary' : ''}" 
                        data-category="${key}">
                    <div class="flex items-center">
                        <i class="${category.icon} mr-2"></i>
                        ${category.label}
                    </div>
                    <span class="text-xs bg-zinc-200 dark:bg-zinc-700 px-2 py-1 rounded-full">
                        ${category.count}
                    </span>
                </button>
            `;
            categoryList.appendChild(li);
        });
        
        // Vincular eventos aos novos botões
        this.bindCategoryEvents(container, onCategoryChange);
    }

    static bindCategoryEvents(container, onCategoryChange) {
        const categoryButtons = container.querySelectorAll('.media-category-btn');
        
        categoryButtons.forEach(btn => {
            // Remover listeners anteriores
            btn.replaceWith(btn.cloneNode(true));
        });
        
        // Reselecionar após clonagem
        const newCategoryButtons = container.querySelectorAll('.media-category-btn');
        
        newCategoryButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const category = btn.dataset.category;
                
                // Atualizar estado ativo dos botões
                newCategoryButtons.forEach(b => {
                    b.classList.remove('bg-trends-primary/10', 'text-trends-primary');
                });
                btn.classList.add('bg-trends-primary/10', 'text-trends-primary');
                
                // Chamar callback
                if (onCategoryChange) {
                    const filters = category === 'all' ? {} : { type: category };
                    onCategoryChange(filters);
                }
            });
        });
    }

    // ==================== ESTADOS ====================

    static showLoading(container) {
        if (!container) return;
        container.innerHTML = `
            <div class="flex items-center justify-center py-12">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500">Carregando mídias...</p>
                </div>
            </div>
        `;
    }

    static showError(container) {
        if (!container) return;
        container.innerHTML = `
            <div class="flex items-center justify-center py-12">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i>
                    <p class="text-red-500">Erro ao carregar mídias</p>
                    <button class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" onclick="location.reload()">
                        Tentar novamente
                    </button>
                </div>
            </div>
        `;
    }

    // ==================== PREVIEW ====================

    static openPreview(media, getFileIcon) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-2xl max-h-[90vh] overflow-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">${media.filename}</h3>
                    <button class="close-preview text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                ${media.is_image ? 
                    `<img src="${media.original_url}" alt="${media.filename}" class="max-w-full h-auto">` :
                    `<div class="text-center py-8">
                        <i class="${getFileIcon(media.mime_type)} text-6xl mb-4"></i>
                        <p class="text-gray-600">Preview não disponível para este tipo de arquivo</p>
                    </div>`
                }
                <div class="mt-4 text-sm text-gray-600">
                    <p><strong>Tamanho:</strong> ${media.size_human}</p>
                    <p><strong>Tipo:</strong> ${media.mime_type}</p>
                    <p><strong>Criado em:</strong> ${media.created_at}</p>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.querySelector('.close-preview').addEventListener('click', () => modal.remove());
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
    }

    // ==================== UTILITÁRIOS ====================

    static getFileIcon(mimeType) {
        const icons = {
            'application/pdf': 'fas fa-file-pdf text-red-500',
            'application/msword': 'fas fa-file-word text-blue-500',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word text-blue-500',
            'video/mp4': 'fas fa-file-video text-purple-500',
            'audio/mp3': 'fas fa-file-audio text-green-500',
        };
        return icons[mimeType] || 'fas fa-file text-gray-500';
    }
} 