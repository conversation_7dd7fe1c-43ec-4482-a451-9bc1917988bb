@extends('layouts.panel')

@section('title', '<PERSON>al<PERSON> da Matrícula')
@section('page_title', '<PERSON>al<PERSON> da Matrícula')

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        <div class="p-6 border-b border-zinc-800/20 dark:border-zinc-800">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-zinc-900 dark:text-white">
                        Detalhes da Matrícula #{{ $enrollment->id }}
                    </h2>
                    <p class="text-sm text-zinc-600 dark:text-zinc-400 mt-1">
                        Informações completas da matrícula
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('admin.enrollments.edit', $enrollment->id) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Editar
                    </a>
                    <a href="{{ route('admin.enrollments.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-zinc-600 hover:bg-zinc-700 text-white text-sm font-medium rounded-lg transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar
                    </a>
                </div>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Informações do Estudante -->
                <div class="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-zinc-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        Informações do Estudante
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Nome:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->student->name ?? 'N/A' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Email:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->student->email ?? 'N/A' }}</p>
                        </div>
                        @if($enrollment->student->phone1)
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Telefone:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->student->phone1 }}</p>
                        </div>
                        @endif
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Status do Estudante:</label>
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full 
                                {{ $enrollment->student->registration_status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ ucfirst($enrollment->student->registration_status ?? 'N/A') }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Informações do Curso/Módulo -->
                <div class="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-zinc-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-book mr-2 text-green-600"></i>
                        Informações do Curso/Módulo
                    </h3>
                    
                    <div class="space-y-3">
                        @if($enrollment->module && $enrollment->module->course)
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Curso:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->module->course->title }}</p>
                        </div>
                        @endif
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Módulo:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->module->title ?? 'N/A' }}</p>
                        </div>
                        @if($enrollment->module && $enrollment->module->description)
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Descrição:</label>
                            <p class="text-zinc-900 dark:text-white text-sm">{{ Str::limit($enrollment->module->description, 100) }}</p>
                        </div>
                        @endif
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Preço Pago:</label>
                            <p class="text-zinc-900 dark:text-white font-semibold">
                                @if($enrollment->module_price_at_time > 0)
                                    R$ {{ number_format($enrollment->module_price_at_time, 2, ',', '.') }}
                                @else
                                    <span class="text-green-600">Gratuito</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Status da Matrícula -->
                <div class="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-zinc-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-purple-600"></i>
                        Status da Matrícula
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Status:</label>
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full 
                                @if($enrollment->status === 'active') bg-green-100 text-green-800
                                @elseif($enrollment->status === 'expired') bg-red-100 text-red-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst($enrollment->status) }}
                            </span>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Status de Aprovação:</label>
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full 
                                @if($enrollment->approval_status === 'approved') bg-green-100 text-green-800
                                @elseif($enrollment->approval_status === 'rejected') bg-red-100 text-red-800
                                @else bg-yellow-100 text-yellow-800 @endif">
                                {{ ucfirst($enrollment->approval_status) }}
                            </span>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Método de Matrícula:</label>
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full 
                                @if($enrollment->enrollment_method === 'admin_created') bg-purple-100 text-purple-800
                                @else bg-blue-100 text-blue-800 @endif">
                                {{ $enrollment->enrollment_method === 'admin_created' ? 'Criado pelo Admin' : 'Auto-matrícula' }}
                            </span>
                        </div>
                        @if($enrollment->rejection_reason)
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Motivo da Rejeição:</label>
                            <p class="text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-900/20 p-2 rounded">
                                {{ $enrollment->rejection_reason }}
                            </p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Datas Importantes -->
                <div class="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-zinc-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-calendar mr-2 text-orange-600"></i>
                        Datas Importantes
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Data de Solicitação:</label>
                            <p class="text-zinc-900 dark:text-white">
                                {{ $enrollment->requested_at ? $enrollment->requested_at->format('d/m/Y H:i') : 'N/A' }}
                            </p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Data de Matrícula:</label>
                            <p class="text-zinc-900 dark:text-white">
                                {{ $enrollment->enrolled_at ? $enrollment->enrolled_at->format('d/m/Y H:i') : 'N/A' }}
                            </p>
                        </div>
                        @if($enrollment->approved_at)
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Data de Aprovação:</label>
                            <p class="text-zinc-900 dark:text-white">
                                {{ $enrollment->approved_at->format('d/m/Y H:i') }}
                            </p>
                        </div>
                        @endif
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Data de Expiração:</label>
                            <p class="text-zinc-900 dark:text-white">
                                @if($enrollment->expires_at)
                                    {{ $enrollment->expires_at->format('d/m/Y') }}
                                    @if($enrollment->expires_at->isPast())
                                        <span class="text-red-600 text-sm">(Expirado)</span>
                                    @endif
                                @else
                                    <span class="text-green-600">Sem expiração</span>
                                @endif
                            </p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Criado em:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-zinc-600 dark:text-zinc-400">Atualizado em:</label>
                            <p class="text-zinc-900 dark:text-white">{{ $enrollment->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ações de Aprovação (se pendente) -->
            @if($enrollment->approval_status === 'pending')
            <div class="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <h4 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-3">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Matrícula Pendente de Aprovação
                </h4>
                <p class="text-yellow-700 dark:text-yellow-300 mb-4">
                    Esta matrícula está aguardando aprovação. Você pode aprovar ou rejeitar abaixo.
                </p>
                <div class="flex items-center space-x-4">
                    <form action="{{ route('admin.enrollments.approve', $enrollment->id) }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors"
                                onclick="return confirm('Tem certeza que deseja aprovar esta matrícula?')">
                            <i class="fas fa-check mr-2"></i>
                            Aprovar Matrícula
                        </button>
                    </form>
                    
                    <button type="button" 
                            class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors"
                            onclick="showRejectModal()">
                        <i class="fas fa-times mr-2"></i>
                        Rejeitar Matrícula
                    </button>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal de Rejeição -->
@if($enrollment->approval_status === 'pending')
<div id="rejectModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-zinc-800 rounded-lg max-w-md w-full p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-white mb-4">
                Rejeitar Matrícula
            </h3>
            <form action="{{ route('admin.enrollments.reject', $enrollment->id) }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label for="rejection_reason" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                        Motivo da Rejeição *
                    </label>
                    <textarea name="rejection_reason" id="rejection_reason" rows="4" required
                              class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-700 dark:text-white"
                              placeholder="Descreva o motivo da rejeição..."></textarea>
                </div>
                <div class="flex items-center justify-end space-x-3">
                    <button type="button" 
                            class="px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-700 border border-zinc-300 dark:border-zinc-600 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-600 transition-colors"
                            onclick="hideRejectModal()">
                        Cancelar
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                        Rejeitar Matrícula
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal() {
    document.getElementById('rejectModal').classList.remove('hidden');
}

function hideRejectModal() {
    document.getElementById('rejectModal').classList.add('hidden');
}

// Fechar modal ao clicar fora
document.getElementById('rejectModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideRejectModal();
    }
});
</script>
@endif
@endsection
