<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PlgTest extends Model
{
    use HasFactory;

    protected $table = 'plg_tests';

    protected $fillable = [
        'company_id',
        'title',
        'slug',
        'description',
        'module_id',
        'test_type',
        'questions', // JSON com IDs das questões
        'time_limit_minutes',
        'max_attempts',
        'passing_score',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
        'questions' => 'array',
        'passing_score' => 'decimal:2',
        'max_attempts' => 'integer',
        'time_limit_minutes' => 'integer',
    ];

    /**
     * Boot method para gerar slug automaticamente
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($test) {
            if (empty($test->slug)) {
                $test->slug = Str::slug($test->title);

                // Garantir que o slug seja único
                $originalSlug = $test->slug;
                $counter = 1;
                while (static::where('slug', $test->slug)->exists()) {
                    $test->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }
        });

        static::updating(function ($test) {
            if ($test->isDirty('title') && empty($test->slug)) {
                $test->slug = Str::slug($test->title);

                // Garantir que o slug seja único
                $originalSlug = $test->slug;
                $counter = 1;
                while (static::where('slug', $test->slug)->where('id', '!=', $test->id)->exists()) {
                    $test->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }
        });
    }

    /**
     * Relacionamento com o módulo.
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(PlgModule::class, 'module_id');
    }

    /**
     * Relacionamento com as tentativas dos estudantes.
     */
    public function attempts(): HasMany
    {
        return $this->hasMany(PlgTestAttempt::class, 'test_id');
    }

    /**
     * Relacionamento com os relatórios dos estudantes.
     */
    public function reports(): HasMany
    {
        return $this->hasMany(PlgTestReport::class, 'test_id');
    }

    /**
     * Obtém as questões do teste baseado no campo JSON.
     * Aplica randomização automática se configurado.
     * Para manter consistência durante uma tentativa, use getQuestionsForAttempt().
     */
    public function getQuestions()
    {
        if (!$this->questions || !is_array($this->questions)) {
            return collect();
        }

        // Extrair apenas os IDs das questões do array JSON
        $questionIds = array_column($this->questions, 'id');

        if (empty($questionIds)) {
            return collect();
        }

        $questions = PlgQuestion::whereIn('id', $questionIds)
            ->with('answers')
            ->get();

        // Aplicar randomização de questões se configurado
        if ($this->randomize_questions) {
            $questions = $questions->shuffle();
        }

        // Aplicar randomização de alternativas se configurado
        if ($this->randomize_alternatives) {
            $questions->each(function ($question) {
                if ($question->answers) {
                    $question->setRelation('answers', $question->answers->shuffle());
                }
            });
        }

        return $questions;
    }

    /**
     * Obtém as questões para uma tentativa específica na ordem original.
     * Não aplica randomização de questões - apenas retorna na ordem definida no teste.
     * A randomização de alternativas será feita no frontend para segurança.
     */
    public function getQuestionsForAttempt()
    {
        if (!$this->questions || !is_array($this->questions)) {
            return collect();
        }

        // Extrair apenas os IDs das questões do array JSON
        $questionIds = array_column($this->questions, 'id');

        if (empty($questionIds)) {
            return collect();
        }

        // Buscar questões na ordem original definida no teste
        $questions = PlgQuestion::whereIn('id', $questionIds)
            ->with('answers')
            ->get();

        // Reordenar conforme a ordem definida no array JSON do teste
        $orderedQuestions = collect();
        foreach ($questionIds as $questionId) {
            $question = $questions->firstWhere('id', $questionId);
            if ($question) {
                $orderedQuestions->push($question);
            }
        }

        return $orderedQuestions;
    }



    /**
     * Verifica se permite tentativas ilimitadas.
     */
    public function allowsUnlimitedAttempts(): bool
    {
        return $this->max_attempts == 0;
    }

    /**
     * Obtém o limite de tempo em segundos.
     */
    public function getTimeLimitSeconds(): ?int
    {
        return $this->time_limit_minutes ? $this->time_limit_minutes * 60 : null;
    }

    /**
     * Verifica se é um quiz (baseado no campo test_type).
     */
    public function isQuiz(): bool
    {
        return $this->test_type === 'quiz';
    }

    /**
     * Verifica se é um exame (baseado no campo test_type).
     */
    public function isExam(): bool
    {
        return $this->test_type === 'exam';
    }

    /**
     * Verifica se é um desafio (baseado no campo test_type).
     */
    public function isChallenge(): bool
    {
        return $this->test_type === 'challenge';
    }

    /**
     * Obtém o relatório de um estudante específico.
     */
    public function getStudentReport(int $studentId): ?PlgTestReport
    {
        return $this->reports()
            ->where('student_id', $studentId)
            ->first();
    }

    /**
     * Cria uma nova tentativa para um estudante.
     */
    public function createAttempt(int $studentId): PlgTestAttempt
    {
        // Buscar o maior attempt_number existente para este estudante e teste
        $maxAttemptNumber = PlgTestAttempt::where('test_id', $this->id)
            ->where('student_id', $studentId)
            ->max('attempt_number') ?? 0;

        $attemptNumber = $maxAttemptNumber + 1;

        // Criar a tentativa primeiro para ter o ID
        $attempt = PlgTestAttempt::create([
            'company_id' => $this->company_id,
            'test_id' => $this->id,
            'student_id' => $studentId,
            'attempt_number' => $attemptNumber,
            'answers' => [], // Inicializar com array vazio
            'total_questions' => 0, // Será atualizado abaixo
            'started_at' => now(),
            'status' => 'in_progress',
        ]);

        // Agora obter as questões na ordem original
        $questions = $this->getQuestionsForAttempt();

        // Atualizar o total de questões
        $attempt->update(['total_questions' => $questions->count()]);

        return $attempt;
    }
}