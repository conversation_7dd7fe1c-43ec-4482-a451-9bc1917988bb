@extends('layouts.panel')

@section('title', 'Gerenciamento de Questões')
@section('page_title', 'Questões')

@section('content')
    <div class="animate-fade-in">
        <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
            @include('panel.admin.includes.page-header', [
                'title' => 'Gerenciamento de Questões',
                'description' => 'Gere<PERSON>ie todas as questões disponíveis na plataforma.',
                'actions' => [
                    [
                        'route' => route('admin.questions.create'),
                        'text' => 'Nova Questão',
                        'icon' => 'fas fa-plus',
                        'class' => 'bg-trends-primary text-white',
                        'hover_class' => 'bg-trends-primary/90',
                    ],
                    [
                        'route' => route('admin.questions.import'),
                        'text' => 'Importar CSV',
                        'icon' => 'fas fa-file-import',
                        'class' => 'bg-yellow-500 text-white',
                        'hover_class' => 'bg-yellow-600',
                    ],
                ],
            ])

            @include('panel.admin.includes.datatable', [
                'id' => $id,
                'columns' => $columns,
                'ajaxUrl' => $ajaxUrl,
                'filters' => $filters ?? [],
                'clearFiltersBtn' => $clearFiltersBtn ?? false,
            ])
        </div>
    </div>
@endsection
