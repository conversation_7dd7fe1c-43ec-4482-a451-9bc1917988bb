<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Plank\Mediable\Mediable;

class PlgQuestion extends Model
{
    use HasFactory, SoftDeletes, Mediable;

    protected $table = 'plg_questions';

    protected $fillable = [
        'company_id',
        'user_id', // Campo adicionado para rastreabilidade
        'question',
        'question_type',
        'answer_format',
        'essay_answer',
        'free',
        'imported',
        'image',
    ];

    protected $casts = [
        'free' => 'boolean',
        'imported' => 'boolean'
    ];

    public function answers()
    {
        return $this->hasMany(PlgQuestionAnswer::class, 'question_id');
    }

    public function categories()
    {
        return $this->belongsToMany(
            PlgCategories::class,
            'plg_questions_x_category',
            'question_id',
            'category_id'
        )->withPivot('company_id')->withTimestamps();
    }

    public function media()
    {
        return $this->morphToMany(\Plank\Mediable\Media::class, 'mediable')
        ->withPivot('tag', 'order', 'caption'); // Adicione 'caption' aqui
    }

}