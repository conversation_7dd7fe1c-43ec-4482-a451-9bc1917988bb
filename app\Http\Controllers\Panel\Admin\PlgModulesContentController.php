<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlgModule;
use App\Models\PlgModuleContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Storage;

class PlgModulesContentController extends Controller
{
    public function index($moduleId)
    {
        $module = PlgModule::findOrFail($moduleId);
        return view('panel.admin.modules.contents.index', [
            'module' => $module,
            'columns' => $this->getColumns(),
            'id' => 'contents-table',
            'ajaxUrl' => route('admin.modules.contents.data', $moduleId),
            'filters' => $this->getFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    public function getData($moduleId)
    {
        $query = PlgModuleContent::where('module_id', $moduleId)
            ->select('plg_modules_contents.*')->orderBy('id', 'desc');
        
        return DataTables::of($query)
            ->addColumn('actions', function($content) use ($moduleId) {
                return view('panel.admin.includes.table-actions', [
                    'actions' => [
                        [
                            'type' => 'edit',
                            'route' => route('admin.modules.contents.edit', ['moduleId' => $moduleId, 'content' => $content->id]),
                            'title' => 'Editar conteúdo'
                        ],
                        [
                            'type' => 'show',
                            'route' => route('admin.modules.contents.show', ['moduleId' => $moduleId, 'content' => $content->id]),
                            'title' => 'Visualizar conteúdo'
                        ],
                        [
                            'type' => 'duplicate',
                            'route' => route('admin.modules.contents.duplicate', ['moduleId' => $moduleId, 'content' => $content->id]),
                            'title' => 'Duplicar conteúdo'
                        ],
                        [
                            'type' => 'delete',
                            'route' => route('admin.modules.contents.destroy', ['moduleId' => $moduleId, 'content' => $content->id]),
                            'title' => 'Excluir conteúdo',
                            'confirm_message' => 'Tem certeza que deseja excluir este conteúdo?'
                        ]
                    ]
                ])->render();
            })
            ->editColumn('title', function($content) {
                return '<div class="font-medium text-zinc-900 dark:text-zinc-100">' . e($content->title) . '</div>';
            })
            ->editColumn('content_type', function($content) {
                return view('panel.admin.modules.contents.partials.type-badge', ['type' => $content->content_type])->render();
            })
            ->editColumn('duration', function($content) {
                return $content->duration ? $content->duration . ' min' : '-';
            })
            ->editColumn('active', function($content) {
                return view('panel.admin.modules.contents.partials.status', ['active' => $content->active])->render();
            })
            ->rawColumns(['actions', 'title', 'content_type', 'active'])
            ->make(true);
    }

    public function create($moduleId)
    {
        $module = PlgModule::findOrFail($moduleId);
        return view('panel.admin.modules.contents.create', array_merge(
            ['module' => $module],
            $this->formData()
        ));
    }

    public function store(Request $request, $moduleId)
    {
        $module = PlgModule::findOrFail($moduleId);
        $validated = $this->validateRequest($request);

        $user = Auth::user();

        $validated['module_id'] = $module->id;
        $validated['company_id'] = $user->company_id;
        $validated['user_id'] = $user->id; // Auto-preenchimento do user_id
        $validated['active'] = $request->has('active');
        $validated['order'] = $validated['order'] ?? 0;

        PlgModuleContent::create($validated);
        
        return redirect()->route('admin.modules.contents.index', $moduleId)
            ->with('success', 'Conteúdo criado com sucesso');
    }

    public function show($moduleId, $content)
    {
        $module = PlgModule::findOrFail($moduleId);
        $content = PlgModuleContent::findOrFail($content);
        return view('panel.admin.modules.contents.show', compact('module', 'content'));
    }

    public function edit($moduleId, $content)
    {
        $module = PlgModule::findOrFail($moduleId);
        $content = PlgModuleContent::findOrFail($content);
        return view('panel.admin.modules.contents.edit', array_merge(
            ['module' => $module, 'content' => $content],
            $this->formData()
        ));
    }


    

    public function update(Request $request, $moduleId, $content)
    {
        $content = PlgModuleContent::findOrFail($content);
        $validated = $this->validateRequest($request);
        
        $validated['active'] = $request->has('active');
        $validated['order'] = $validated['order'] ?? 0;
        
        $content->update($validated);
        
        return redirect()->route('admin.modules.contents.index', $moduleId)
            ->with('success', 'Conteúdo atualizado com sucesso');
    }

    public function destroy($moduleId, $content)
    {
        $content = PlgModuleContent::findOrFail($content);
        
        // Remove o arquivo se existir
        if ($content->content_type == 'file' && $content->content && str_starts_with($content->content, 'storage/')) {
            $path = str_replace('storage/', '', $content->content);
            Storage::disk('public')->delete($path);
        }
        
        $content->delete();
        return response()->json(['success' => true]);
    }

    public function duplicate($moduleId, $contentId)
    {
        $content = PlgModuleContent::findOrFail($contentId);
        
        // Criar uma cópia do conteúdo
        $newContent = $content->replicate();
        
        // Atualizar campos únicos
        $newContent->title = $content->title . ' (Cópia)';
        $newContent->active = false; // Sempre começa como inativo
        
        // Se for um arquivo, duplica o arquivo também
        if ($content->content_type == 'file' && $content->content) {
            $oldPath = str_replace('storage/', '', $content->content);
            $extension = pathinfo($oldPath, PATHINFO_EXTENSION);
            $newPath = 'modules/contents/' . uniqid() . '.' . $extension;
            
            if (Storage::disk('public')->copy($oldPath, $newPath)) {
                $newContent->content = 'storage/' . $newPath;
            }
        }
        
        $newContent->save();

        return redirect()->route('admin.modules.contents.edit', ['moduleId' => $moduleId, 'content' => $newContent->id])
            ->with('success', 'Conteúdo duplicado com sucesso! Você pode editar os detalhes agora.');
    }

    public function data(Request $request, string $moduleId)
    {
        $query = PlgModuleContent::query()
            ->where('module_id', $moduleId)
            ->select([
                'plg_modules_contents.id',
                'plg_modules_contents.title',
                'plg_modules_contents.content_type',
                'plg_modules_contents.duration',
                'plg_modules_contents.order',
                'plg_modules_contents.active'
            ]);

        return datatables()->of($query)
            ->addColumn('content_type', function ($content) {
                return view('panel.admin.modules.contents.partials.type-badge', ['type' => $content->content_type])->render();
            })
            ->addColumn('active', function ($content) {
                return view('panel.admin.modules.contents.partials.status', ['active' => $content->active])->render();
            })
            ->addColumn('actions', function ($content) use ($moduleId) {
                return view('panel.admin.includes.table-actions', [
                    'editRoute' => route('admin.modules.contents.edit', ['moduleId' => $moduleId, 'content' => $content->id]),
                    'showRoute' => route('admin.modules.contents.show', ['moduleId' => $moduleId, 'content' => $content->id]),
                    'deleteRoute' => route('admin.modules.contents.destroy', ['moduleId' => $moduleId, 'content' => $content->id]),
                    'duplicateRoute' => route('admin.modules.contents.duplicate', ['moduleId' => $moduleId, 'content' => $content->id])
                ])->render();
            })
            ->rawColumns(['content_type', 'active', 'actions'])
            ->make(true);
    }

    private function getColumns()
    {
        return [
            [
                'data' => 'order',
                'name' => 'plg_modules_contents.order',
                'label' => 'Ordem'
            ],
            [
                'data' => 'title',
                'name' => 'plg_modules_contents.title',
                'label' => 'Título'
            ],
            [
                'data' => 'content_type',
                'name' => 'plg_modules_contents.content_type',
                'label' => 'Tipo'
            ],
            [
                'data' => 'duration',
                'name' => 'plg_modules_contents.duration',
                'label' => 'Duração'
            ],
            [
                'data' => 'active',
                'name' => 'plg_modules_contents.active',
                'label' => 'Status'
            ],
            [
                'data' => 'actions',
                'name' => 'actions',
                'label' => 'Ações',
                'orderable' => false,
                'searchable' => false
            ],
        ];
    }

    private function getFilters()
    {
        return [
            [
                'label' => 'Tipo',
                'column' => 'content_type',
                'options' => [
                    ['value' => 'media', 'label' => 'Imagem / PDF / MP3'],
                    ['value' => 'video', 'label' => 'Vídeo'],
                    ['value' => 'link', 'label' => 'Link Externo'],
                ]
            ],
            [
                'label' => 'Status',
                'column' => 'active',
                'options' => [
                    ['value' => '1', 'label' => 'Ativo'],
                    ['value' => '0', 'label' => 'Inativo'],
                ]
            ],
        ];
    }

    private function formData()
    {
        return [
            'contentTypes' => [
                'media' => 'Imagem / PDF / MP3',
                'video' => 'Vídeo',
                'link' => 'Link Externo',
            ]
        ];
    }

    private function validateRequest(Request $request)
    {
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content_type' => 'required|in:media,video,link',
            'duration' => 'nullable|integer|min:1',
            'order' => 'nullable|integer',
        ];

        // Regras específicas baseadas no tipo de conteúdo
        switch ($request->content_type) {
            case 'media':
                $rules['media_id'] = 'required|exists:media,id';
                break;
            case 'video':
                $rules['video_embed'] = 'required|string';
                break;
            case 'link':
                $rules['external_link'] = 'required|url';
                break;
        }

        $validated = $request->validate($rules);

        // Preparar o campo content baseado no tipo
        switch ($validated['content_type']) {
            case 'media':
                $validated['content'] = $validated['media_id'];
                break;
            case 'video':
                $validated['content'] = $validated['video_embed'];
                break;
            case 'link':
                $validated['content'] = $validated['external_link'];
                break;
        }

        // Remover campos extras que não são do modelo
        unset($validated['media_id'], $validated['video_embed'], $validated['external_link']);

        return $validated;
    }
} 