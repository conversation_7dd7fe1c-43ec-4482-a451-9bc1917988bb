<x-layouts.student title="Histórico de Tentativas - {{ $test->name }}">
    <!-- Breadcrumb -->
    <x-student.breadcrumb :course="$course" :module="$module" :test="$test" />

    <div class="container mx-auto px-4 py-8">
        <!-- Cabe<PERSON>lho -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-history mr-2 text-blue-600"></i>
                        Histórico de Tentativas
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300">{{ $test->name }} - {{ $module->title }}</p>
                    
                    <div class="flex items-center space-x-4 mt-4 text-sm">
                        @if($test->isQuiz())
                            <span class="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-3 py-1 rounded-full font-medium">
                                Quiz de Treino
                            </span>
                        @else
                            <span class="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 px-3 py-1 rounded-full font-medium">
                                Teste Final
                            </span>
                        @endif
                        
                        <span class="text-gray-600 dark:text-gray-300">
                            <i class="fas fa-question-circle mr-1"></i>
                            {{ is_array($test->questions) ? count($test->questions) : 0 }} questões
                        </span>
                        
                        @if($test->isFinalTest())
                            <span class="text-gray-600 dark:text-gray-300">
                                <i class="fas fa-target mr-1"></i>
                                Nota mínima: {{ $test->passing_score }}%
                            </span>
                        @endif
                    </div>
                </div>
                
                <div class="text-right">
                    @php
                        $bestAttempt = $attempts->where('passed', true)->first() ?: $attempts->sortByDesc('score')->first();
                        $totalAttempts = $attempts->total();
                        $passedAttempts = $attempts->where('passed', true)->count();
                    @endphp
                    
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ $totalAttempts }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">
                        {{ $totalAttempts === 1 ? 'tentativa' : 'tentativas' }}
                    </div>
                    
                    @if($bestAttempt)
                        <div class="mt-2 text-lg font-semibold {{ $bestAttempt->passed ? 'text-green-600' : 'text-gray-600' }}">
                            {{ $bestAttempt->score }}%
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Estatísticas Resumidas -->
        @if($attempts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <div class="text-3xl font-bold text-blue-600 mb-2">{{ $totalAttempts }}</div>
                    <div class="text-gray-600 dark:text-gray-300">Total de Tentativas</div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <div class="text-3xl font-bold text-green-600 mb-2">{{ $passedAttempts }}</div>
                    <div class="text-gray-600 dark:text-gray-300">Aprovações</div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <div class="text-3xl font-bold text-gray-600 mb-2">
                        {{ $bestAttempt ? $bestAttempt->score . '%' : '-' }}
                    </div>
                    <div class="text-gray-600 dark:text-gray-300">Melhor Nota</div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <div class="text-3xl font-bold text-purple-600 mb-2">
                        {{ $attempts->count() > 0 ? round($attempts->avg('score'), 1) . '%' : '-' }}
                    </div>
                    <div class="text-gray-600 dark:text-gray-300">Média</div>
                </div>
            </div>
        @endif

        <!-- Lista de Tentativas -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                    Tentativas Realizadas
                </h2>
            </div>

            @if($attempts->count() > 0)
                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($attempts as $attempt)
                        <div class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-4 mb-2">
                                        <!-- Status -->
                                        @if($attempt->passed)
                                            <span class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-3 py-1 rounded-full text-sm font-medium">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                Aprovado
                                            </span>
                                        @else
                                            <span class="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 px-3 py-1 rounded-full text-sm font-medium">
                                                <i class="fas fa-times-circle mr-1"></i>
                                                Reprovado
                                            </span>
                                        @endif
                                        
                                        <!-- Data -->
                                        <span class="text-sm text-gray-600 dark:text-gray-300">
                                            <i class="fas fa-calendar mr-1"></i>
                                            {{ $attempt->completed_at ? $attempt->completed_at->format('d/m/Y H:i') : 'Em andamento' }}
                                        </span>

                                        <!-- Duração -->
                                        <span class="text-sm text-gray-600 dark:text-gray-300">
                                            <i class="fas fa-clock mr-1"></i>
                                            @if($attempt->completed_at && $attempt->started_at)
                                                {{ $attempt->completed_at->diffForHumans($attempt->started_at, true) }}
                                            @else
                                                Em andamento
                                            @endif
                                        </span>
                                    </div>
                                    
                                    <!-- Estatísticas da tentativa -->
                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Nota:</span>
                                            <span class="font-semibold {{ $attempt->passed ? 'text-green-600' : 'text-red-600' }}">
                                                {{ $attempt->score }}%
                                            </span>
                                        </div>
                                        
                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Acertos:</span>
                                            <span class="font-semibold text-gray-900 dark:text-white">
                                                {{ $attempt->correct_answers }}/{{ $attempt->total_questions }}
                                            </span>
                                        </div>
                                        
                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Precisão:</span>
                                            <span class="font-semibold text-gray-900 dark:text-white">
                                                {{ round(($attempt->correct_answers / $attempt->total_questions) * 100, 1) }}%
                                            </span>
                                        </div>
                                        
                                        @if($test->isFinalTest())
                                            <div>
                                                <span class="text-gray-600 dark:text-gray-400">Necessário:</span>
                                                <span class="font-semibold text-blue-600">
                                                    {{ $test->passing_score }}%
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Ações -->
                                <div class="ml-6">
                                    <a href="{{ route('student.quiz.results', $attempt->id) }}" 
                                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-eye mr-1"></i>
                                        Ver Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Paginação -->
                @if($attempts->hasPages())
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        {{ $attempts->links() }}
                    </div>
                @endif
            @else
                <div class="p-8 text-center">
                    <i class="fas fa-history text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma tentativa encontrada</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Você ainda não fez nenhuma tentativa neste {{ $test->isQuiz() ? 'quiz' : 'teste' }}.
                    </p>
                    
                    @if($test->isQuiz())
                        <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $test->slug]) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-play mr-2"></i>
                            Fazer Primeiro Quiz
                        </a>
                    @elseif($student->canTakeTest($test))
                        <a href="{{ route('student.test.start', [$course->slug, $module->slug, $test->slug]) }}" 
                           class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-graduation-cap mr-2"></i>
                            Fazer Primeiro Teste
                        </a>
                    @endif
                </div>
            @endif
        </div>

        <!-- Botões de Ação -->
        <div class="mt-8 text-center space-y-4">
            @if($attempts->count() > 0)
                @if($test->isQuiz() || ($test->isFinalTest() && $student->canTakeTest($test)))
                    @if($test->isQuiz())
                        <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $test->slug]) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block">
                            <i class="fas fa-redo mr-2"></i>
                            Fazer Novo Quiz
                        </a>
                    @else
                        <a href="{{ route('student.test.start', [$course->slug, $module->slug, $test->slug]) }}" 
                           class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block">
                            <i class="fas fa-redo mr-2"></i>
                            Tentar Novamente
                        </a>
                    @endif
                @elseif($test->isFinalTest() && !$student->canTakeTest($test))
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 inline-block">
                        <p class="text-yellow-800">
                            <i class="fas fa-clock mr-2"></i>
                            Próxima tentativa disponível em: {{ $student->getTestCooldownRemaining($test) }}
                        </p>
                    </div>
                @endif
            @endif
            
            <div>
                <a href="{{ route('student.module.show', [$course->slug, $module->slug]) }}" 
                   class="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar ao Módulo
                </a>
            </div>
        </div>
    </div>
</x-layouts.student>
