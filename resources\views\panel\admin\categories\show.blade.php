@extends('layouts.panel')

@section('title', 'Visualizar Categoria')
@section('page_title', 'Visualizar Categoria')

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        <div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                    <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Visualizar Categoria</h1>
                </div>
                <div class="flex items-center gap-2">
                    <a href="{{ route('admin.categories.edit', $category->id) }}" 
                       class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-trends-primary border border-transparent rounded-md hover:bg-trends-primary/90 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                        <i class="fas fa-edit mr-2"></i>
                        Editar
                    </a>
                    <a href="{{ route('admin.categories.index') }}" 
                       class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar
                    </a>
                </div>
            </div>
        </div>

        <div class="p-6">
            <div class="space-y-6">
                <div>
                    <h2 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Nome</h2>
                    <p class="mt-1 text-base text-zinc-900 dark:text-zinc-100">{{ $category->title }}</p>
                </div>

                <div>
                    <h2 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Descrição</h2>
                    <p class="mt-1 text-base text-zinc-900 dark:text-zinc-100">
                        {{ $category->description ?: 'Nenhuma descrição fornecida' }}
                    </p>
                </div>

                <div>
                    <h2 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Ícone</h2>
                    <div class="mt-1 flex items-center gap-2">
                        @if($category->icon)
                            <i class="fas {{ $category->icon }} text-xl text-zinc-900 dark:text-zinc-100"></i>
                            <span class="text-base text-zinc-900 dark:text-zinc-100">{{ $category->icon }}</span>
                        @else
                            <span class="text-base text-zinc-900 dark:text-zinc-100">Nenhum ícone definido</span>
                        @endif
                    </div>
                </div>

                <div>
                    <h2 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Ordem</h2>
                    <p class="mt-1 text-base text-zinc-900 dark:text-zinc-100">{{ $category->order }}</p>
                </div>

                <div>
                    <h2 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Cursos</h2>
                    @if($category->courses->count() > 0)
                        <div class="mt-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($category->courses as $course)
                                <div class="bg-zinc-50 dark:bg-zinc-800 p-4 rounded-lg">
                                    <h3 class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ $course->name }}</h3>
                                    <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
                                        {{ Str::limit($course->description, 100) }}
                                    </p>
                                    <a href="{{ route('admin.courses.show', $course->id) }}" 
                                       class="mt-2 inline-flex items-center text-sm text-trends-primary hover:text-trends-primary/90">
                                        Ver curso
                                        <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="mt-1 text-base text-zinc-900 dark:text-zinc-100">Nenhum curso nesta categoria</p>
                    @endif
                </div>

                <div>
                    <h2 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Informações Adicionais</h2>
                    <div class="mt-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-zinc-500 dark:text-zinc-400">Criado em</p>
                            <p class="text-base text-zinc-900 dark:text-zinc-100">
                                {{ $category->created_at->format('d/m/Y H:i:s') }}
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-zinc-500 dark:text-zinc-400">Última atualização</p>
                            <p class="text-base text-zinc-900 dark:text-zinc-100">
                                {{ $category->updated_at->format('d/m/Y H:i:s') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 