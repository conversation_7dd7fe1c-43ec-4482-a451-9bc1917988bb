<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Plank\Mediable\Mediable;

class PlgQuestionAnswer extends Model
{
    use HasFactory, SoftDeletes, Mediable;

    protected $table = 'plg_questions_answers';

    protected $fillable = [
        'company_id',
        'question_id',
        'answer_number',
        'answer',
        'correct',
        'explanation',
        'image',
        'caption',
    ];

    protected $casts = [
        'correct' => 'boolean',
        'answer_number' => 'integer'
    ];

    public function question()
    {
        return $this->belongsTo(PlgQuestion::class, 'question_id');
    }


    public function media()
    {
        return $this->morphToMany(\Plank\Mediable\Media::class, 'mediable')
        ->withPivot('tag', 'order', 'caption'); // Adicione 'caption' aqui
    }
    
} 