@props(['title', 'subtitle' => null, 'modules', 'course'])

<div class="mb-8">
    <div class="mb-4">
        <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">{{ $title }}</h2>
        @if($subtitle)
            <p class="text-sm text-zinc-600 dark:text-zinc-400 mt-1">{{ $subtitle }}</p>
        @endif
    </div>

    @if($modules->count() > 0)
        <div class="swiper-carousel swiper group">
            <div class="swiper-wrapper">
                @foreach($modules as $module)
                    <div class="swiper-slide">
                        <x-student.module-card :module="$module" :course="$module->course_ref ?? $course" />
                    </div>
                @endforeach
            </div>

            <!-- Bo<PERSON>ões de navegação -->
            <div class="swiper-button-next absolute top-1/2 -translate-y-1/2 right-2 z-10 w-10 h-10 bg-black/70 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg cursor-pointer">
                <i class="fas fa-chevron-right text-sm"></i>
            </div>
            <div class="swiper-button-prev absolute top-1/2 -translate-y-1/2 left-2 z-10 w-10 h-10 bg-black/70 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg cursor-pointer">
                <i class="fas fa-chevron-left text-sm"></i>
            </div>
        </div>
    @else
        <div class="text-center py-8">
            <i class="fas fa-book text-4xl text-gray-400 dark:text-zinc-500 mb-4"></i>
            <p class="text-gray-500 dark:text-zinc-400">Nenhum módulo disponível</p>
        </div>
    @endif
</div>
