/* Importar estilos do Quill da instalação local */
@import 'quill/dist/quill.snow.css';

/* Customizações adicionais para o editor Quill */
.ql-editor {
    min-height: 150px;
    font-size: 14px;
    line-height: 1.6;
}

.ql-toolbar {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.ql-container {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 8px 8px;
}

/* Dark mode support */
.dark .ql-toolbar {
    background-color: #374151;
    border-color: #4b5563;
}

.dark .ql-container {
    background-color: #374151;
    border-color: #4b5563;
}

.dark .ql-editor {
    color: #f9fafb;
}

.dark .ql-editor.ql-blank::before {
    color: #9ca3af;
}

/* Melhorar aparência dos botões da toolbar */
.ql-toolbar .ql-formats {
    margin-right: 15px;
}

.ql-toolbar button:hover,
.ql-toolbar button:focus {
    color: #06b6d4;
}

.ql-toolbar button.ql-active {
    color: #06b6d4;
}

/* Estilizar dropdowns */
.ql-toolbar .ql-picker-label:hover,
.ql-toolbar .ql-picker-label.ql-active {
    color: #06b6d4;
}

.ql-toolbar .ql-picker-options {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .ql-toolbar .ql-picker-options {
    background-color: #374151;
    border-color: #4b5563;
}

/* Responsividade */
@media (max-width: 768px) {
    .ql-toolbar {
        padding: 8px;
    }
    
    .ql-toolbar .ql-formats {
        margin-right: 8px;
    }
} 