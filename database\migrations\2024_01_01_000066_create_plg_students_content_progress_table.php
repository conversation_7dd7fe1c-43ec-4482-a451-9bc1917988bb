<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_students_content_progress', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->foreignId('student_id')->constrained('plg_students')->onDelete('cascade');
            $table->foreignId('content_id')->constrained('plg_modules_contents')->onDelete('cascade');
            $table->enum('status', ['not_started', 'in_progress', 'completed'])->default('not_started');
            $table->integer('progress_percentage')->default(0); // 0-100
            $table->integer('time_spent')->default(0); // Tempo em segundos
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('last_accessed_at')->nullable();
            $table->json('progress_data')->nullable(); // Dados específicos do progresso
            $table->timestamps();
            
            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            
            // Índices para performance
            $table->unique(['student_id', 'content_id']); // Progresso único por estudante/conteúdo
            $table->index(['company_id', 'student_id']); // Multi-tenancy + estudante
            $table->index(['content_id', 'status']); // Conteúdo + status
            $table->index(['student_id', 'status']); // Estudante + status
            $table->index(['progress_percentage']); // Percentual de progresso
            $table->index(['last_accessed_at']); // Último acesso
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_students_content_progress');
    }
};
