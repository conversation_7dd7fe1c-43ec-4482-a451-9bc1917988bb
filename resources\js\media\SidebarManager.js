﻿export class SidebarManager {
    constructor(container = document) {
        this.container = container;
        this.mobileSidebar = null;
        this.toggleBtn = null;
        this.closeBtn = null;
        this.sidebarOpen = false;
        this.isInitialized = false;
        
        // Armazenar referência no container para evitar múltiplas instâncias
        if (!container._sidebarManager) {
            container._sidebarManager = this;
        }
        
        this.init();
    }

    init() {
        this.findOrCreateElements();
        if (this.hasRequiredElements()) {
            this.bindEvents();
            this.setupSync();
            this.isInitialized = true;
        }
    }

    findOrCreateElements() {
        // Buscar elementos existentes
        this.toggleBtn = this.container.querySelector('#toggleSidebarBtn');
        this.mobileSidebar = this.container.querySelector('#mobileSidebar');
        this.closeBtn = this.container.querySelector('#closeSidebarBtn');
        
        // Se não existir o botão toggle, não criar sidebar mobile
        if (!this.toggleBtn) {
            return;
        }
        
        // Criar sidebar mobile se não existir
        if (!this.mobileSidebar) {
            this.createMobileSidebar();
        }
        
        // Criar botão de fechar se não existir
        if (!this.closeBtn) {
            this.createCloseButton();
        }
    }

    hasRequiredElements() {
        return this.mobileSidebar && this.toggleBtn && this.closeBtn;
    }

    createMobileSidebar() {
        const mediaContent = this.container.querySelector('#media-content');
        if (!mediaContent) return;
        
        this.mobileSidebar = document.createElement('div');
        this.mobileSidebar.id = 'mobileSidebar';
        this.mobileSidebar.className = 'md:hidden absolute top-0 left-0 w-64 h-full bg-white dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-800 p-4 overflow-auto transform -translate-x-full transition-transform duration-300 ease-in-out z-10';
        
        // Clonar estrutura da sidebar desktop se existir
        const desktopSidebar = this.container.querySelector('#mediaSidebar');
        if (desktopSidebar) {
            this.mobileSidebar.innerHTML = desktopSidebar.innerHTML;
        } else {
            // Fallback: estrutura mínima
            this.mobileSidebar.innerHTML = `
                <ul class="space-y-1">
                    <!-- Categorias serão renderizadas dinamicamente -->
                </ul>
                
                <!-- Botão para limpar mídias órfãs -->
                <div class="mt-6 pt-4 border-t border-zinc-200 dark:border-zinc-800">
                    <button class="cleanOrphanedBtn w-full px-2 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center justify-center text-sm">
                        <i class="fas fa-broom mr-2"></i>
                        <small>Limpar Mídias Órfãs (0)</small>
                    </button>
                    <p class="text-xs text-zinc-500 mt-2 text-center">
                        Remove registros sem arquivo físico
                    </p>
                </div>
            `;
        }
        
        mediaContent.appendChild(this.mobileSidebar);
    }

    createCloseButton() {
        const mediaContent = this.container.querySelector('#media-content');
        if (!mediaContent) return;
        
        this.closeBtn = document.createElement('button');
        this.closeBtn.id = 'closeSidebarBtn';
        this.closeBtn.className = 'md:hidden absolute top-4 left-64 w-8 h-8 bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-full flex items-center justify-center text-zinc-500 hover:text-zinc-800 dark:text-zinc-400 dark:hover:text-zinc-200 shadow-lg z-20 transform -translate-x-full opacity-0 transition-all duration-300 ease-in-out pointer-events-none';
        
        this.closeBtn.innerHTML = '<i class="fas fa-times text-sm"></i>';
        
        mediaContent.appendChild(this.closeBtn);
    }

    // ==================== CONTROLE DA SIDEBAR ====================

    showSidebar() {
        if (!this.mobileSidebar || !this.closeBtn) return;
        
        this.mobileSidebar.classList.remove('-translate-x-full');
        this.mobileSidebar.classList.add('translate-x-0');
        this.closeBtn.classList.remove('-translate-x-full', 'opacity-0', 'pointer-events-none');
        this.closeBtn.classList.add('translate-x-2', 'opacity-100', 'pointer-events-auto');
        this.sidebarOpen = true;
    }

    hideSidebar() {
        if (!this.mobileSidebar || !this.closeBtn) return;
        
        this.mobileSidebar.classList.remove('translate-x-0');
        this.mobileSidebar.classList.add('-translate-x-full');
        this.closeBtn.classList.remove('translate-x-2', 'opacity-100', 'pointer-events-auto');
        this.closeBtn.classList.add('-translate-x-full', 'opacity-0', 'pointer-events-none');
        this.sidebarOpen = false;
    }

    toggleSidebar() {
        if (this.sidebarOpen) {
            this.hideSidebar();
        } else {
            this.showSidebar();
        }
    }

    // ==================== EVENTOS ====================

    bindEvents() {
        this.bindToggleButton();
        this.bindCloseButton();
        this.bindOutsideClick();
        this.bindResizeHandler();
    }

    bindToggleButton() {
        if (!this.toggleBtn) return;
        
        // Remover listeners anteriores criando novo elemento
        const newToggleBtn = this.toggleBtn.cloneNode(true);
        this.toggleBtn.parentNode.replaceChild(newToggleBtn, this.toggleBtn);
        this.toggleBtn = newToggleBtn;
        
        this.toggleBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleSidebar();
        });
    }

    bindCloseButton() {
        if (!this.closeBtn) return;
        
        // Remover listeners anteriores criando novo elemento
        const newCloseBtn = this.closeBtn.cloneNode(true);
        this.closeBtn.parentNode.replaceChild(newCloseBtn, this.closeBtn);
        this.closeBtn = newCloseBtn;
        
        this.closeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.hideSidebar();
        });
    }

    bindOutsideClick() {
        const handleOutsideClick = (e) => {
            if (this.sidebarOpen && 
                this.mobileSidebar && 
                !this.mobileSidebar.contains(e.target) && 
                !this.toggleBtn.contains(e.target) && 
                !this.closeBtn.contains(e.target)) {
                this.hideSidebar();
            }
        };

        // Remover listener anterior se existir
        if (this._outsideClickHandler) {
            this.container.removeEventListener('click', this._outsideClickHandler);
        }
        
        this._outsideClickHandler = handleOutsideClick;
        this.container.addEventListener('click', handleOutsideClick);
    }

    bindResizeHandler() {
        const handleResize = () => {
            if (window.innerWidth >= 768) {
                this.hideSidebar();
            }
        };

        // Remover listener anterior se existir
        if (this._resizeHandler) {
            window.removeEventListener('resize', this._resizeHandler);
        }
        
        this._resizeHandler = handleResize;
        window.addEventListener('resize', handleResize);
    }

    // ==================== SINCRONIZAÇÃO ====================

    setupSync() {
        this.syncSidebarContent();
        this.observeDesktopSidebar();
    }

    syncSidebarContent() {
        if (!this.mobileSidebar) return;
        
        const desktopSidebar = this.container.querySelector('#mediaSidebar');
        const mobileSidebarUl = this.mobileSidebar.querySelector('ul');
        const desktopUl = desktopSidebar?.querySelector('ul');
        
        if (desktopUl && mobileSidebarUl) {
            mobileSidebarUl.innerHTML = desktopUl.innerHTML;
        }
    }

    observeDesktopSidebar() {
        const desktopSidebar = this.container.querySelector('#mediaSidebar');
        const desktopUl = desktopSidebar?.querySelector('ul');
        
        if (desktopUl) {
            // Limpar observer anterior se existir
            if (this._mutationObserver) {
                this._mutationObserver.disconnect();
            }
            
            this._mutationObserver = new MutationObserver(() => {
                this.syncSidebarContent();
            });
            
            this._mutationObserver.observe(desktopUl, { 
                childList: true, 
                subtree: true 
            });
        }
    }

    // ==================== MÉTODOS PÚBLICOS ====================

    isOpen() {
        return this.sidebarOpen;
    }

    refresh() {
        this.findOrCreateElements();
        if (this.hasRequiredElements() && !this.isInitialized) {
            this.bindEvents();
            this.setupSync();
            this.isInitialized = true;
        }
    }

    destroy() {
        // Limpar event listeners
        if (this._outsideClickHandler) {
            this.container.removeEventListener('click', this._outsideClickHandler);
        }
        
        if (this._resizeHandler) {
            window.removeEventListener('resize', this._resizeHandler);
        }
        
        if (this._mutationObserver) {
            this._mutationObserver.disconnect();
        }
        
        // Reset state
        this.hideSidebar();
        
        // Remover elementos criados dinamicamente
        if (this.mobileSidebar && this.mobileSidebar.parentNode) {
            this.mobileSidebar.remove();
        }
        
        if (this.closeBtn && this.closeBtn.parentNode) {
            this.closeBtn.remove();
        }
        
        // Reset references
        this.mobileSidebar = null;
        this.closeBtn = null;
        
        // Limpar referência do container
        if (this.container._sidebarManager === this) {
            delete this.container._sidebarManager;
        }
        
        this.isInitialized = false;
    }

    // ==================== MÉTODOS ESTÁTICOS ====================

    /**
     * Cria um SidebarManager para qualquer container
     */
    static createForContainer(container) {
        return new SidebarManager(container);
    }

    /**
     * Inicializa sidebar em múltiplos containers
     */
    static initializeAll(containers = [document]) {
        return containers.map(container => new SidebarManager(container));
    }

    /**
     * Event delegation global para elementos criados dinamicamente
     */
    static setupGlobalDelegation() {
        document.addEventListener('click', function(e) {
            // Toggle sidebar via event delegation
            if (e.target.closest('#toggleSidebarBtn')) {
                e.preventDefault();
                const container = e.target.closest('#mediaModal') || e.target.closest('[data-media-container]') || document;
                
                // Verificar se já existe uma instância do SidebarManager
                let sidebar = container._sidebarManager;
                if (!sidebar) {
                    sidebar = new SidebarManager(container);
                    container._sidebarManager = sidebar;
                }
                
                sidebar.toggleSidebar();
            }
            
            // Close sidebar via event delegation
            if (e.target.closest('#closeSidebarBtn')) {
                e.preventDefault();
                const container = e.target.closest('#mediaModal') || e.target.closest('[data-media-container]') || document;
                
                // Verificar se já existe uma instância do SidebarManager
                let sidebar = container._sidebarManager;
                if (sidebar) {
                    sidebar.hideSidebar();
                }
            }
        });
    }

    /**
     * Observer para detectar novos elementos no DOM (AJAX)
     */
    static setupDOMObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Verificar se é um modal de mídia ou contém botão toggle
                        if (node.id === 'mediaModal' || node.querySelector('#mediaModal, #toggleSidebarBtn')) {
                            const targetElement = node.id === 'mediaModal' ? node : node.querySelector('#mediaModal') || node;
                            
                            // Verificar se já não tem SidebarManager
                            if (!targetElement._sidebarManager) {
                                setTimeout(() => {
                                    new SidebarManager(targetElement);
                                }, 100);
                            }
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        return observer;
    }
}
