@extends('layouts.panel')

@section('title', 'Editar <PERSON>údo')
@section('page_title', 'Editar <PERSON>údo')

@push('styles')
    @vite(['resources/css/quill-editor.css'])
@endpush

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Editar Conteúdo',
            'description' => 'Edite as informações deste conteúdo do módulo.',
            'actions' => [
                [
                    'route' => route('admin.modules.edit', $module->id),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ]
            ]
        ])

        <div class="p-6">
            @include('panel.admin.includes.alerts')

            <form action="{{ route('admin.modules.contents.update', ['moduleId' => $module->id, 'content' => $content->id]) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')

                <div class="space-y-6">
                    <!-- Título -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Título <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" id="title"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('title') border-red-500 @enderror"
                            value="{{ old('title', $content->title) }}"
                            placeholder="Digite o título do conteúdo" required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Descrição -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Descrição
                        </label>
                        <div id="description-editor" class="editor" data-input="description"
                            data-placeholder="Digite uma descrição para o conteúdo..."></div>
                        <input type="hidden" name="description" id="description"
                            value="{{ old('description', $content->description) }}">
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tipo de Conteúdo -->
                    <div>
                        <label for="content_type" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Tipo de Conteúdo <span class="text-red-500">*</span>
                        </label>
                        <select name="content_type" id="content_type"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('content_type') border-red-500 @enderror"
                            required>
                            <option value="">Selecione o tipo</option>
                            <option value="media" {{ old('content_type', $content->content_type) == 'media' ? 'selected' : '' }}>Imagem / PDF / MP3</option>
                            <option value="video" {{ old('content_type', $content->content_type) == 'video' ? 'selected' : '' }}>Vídeo</option>
                            <option value="link" {{ old('content_type', $content->content_type) == 'link' ? 'selected' : '' }}>Link Externo</option>
                        </select>
                        @error('content_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>















                    <!-- Conteúdo -->
                    <div>
                        <label for="content" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Conteúdo <span class="text-red-500">*</span>
                        </label>
                        <div id="content-wrapper">
                            <textarea name="content" id="content" rows="6"
                                class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('content') border-red-500 @enderror"
                                required>{{ old('content', $content->content) }}</textarea>
                        </div>
                        @error('content')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Duração -->
                    <div>
                        <label for="duration" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Duração (em minutos)
                        </label>
                        <input type="number" name="duration" id="duration" min="0"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('duration') border-red-500 @enderror"
                            value="{{ old('duration', $content->duration) }}"
                            placeholder="Duração estimada em minutos">
                        @error('duration')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Ordem -->
                    <div>
                        <label for="order" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Ordem
                        </label>
                        <input type="number" name="order" id="order" min="0"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('order') border-red-500 @enderror"
                            value="{{ old('order', $content->order) }}">
                        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">Ordem de exibição do conteúdo (0 = primeiro)</p>
                        @error('order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Status
                        </label>
                        <div class="flex items-center gap-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="active" value="1"
                                    {{ old('active', $content->active) == '1' ? 'checked' : '' }}
                                    class="h-4 w-4 text-trends-primary border-zinc-300 dark:border-zinc-700 focus:ring-trends-primary/30 dark:bg-zinc-800/50">
                                <span class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">Ativo</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="active" value="0"
                                    {{ old('active', $content->active) == '0' ? 'checked' : '' }}
                                    class="h-4 w-4 text-trends-primary border-zinc-300 dark:border-zinc-700 focus:ring-trends-primary/30 dark:bg-zinc-800/50">
                                <span class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">Inativo</span>
                            </label>
                        </div>
                        @error('active')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Botões -->
                <div class="flex justify-end gap-4">
                    <a href="{{ route('admin.modules.edit', $module->id) }}"
                        class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                        <i class="fas fa-times"></i>
                        <span>Cancelar</span>
                    </a>
                    <button type="submit"
                        class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                        <i class="fas fa-save"></i>
                        <span>Atualizar Conteúdo</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
@vite(['resources/js/quill-editor.js'])
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 DEBUG: DOM carregado - inicializando página de edição');
    
    // Aguardar um pouco para o quill-editor.js terminar a inicialização
    setTimeout(() => {
        // Verificar se os editores foram inicializados
        const descEditor = document.getElementById('description-editor');
        const descInput = document.getElementById('description');
        
        if (descEditor && descInput) {
            console.log('🎯 Elementos encontrados:');
            console.log('- Editor:', descEditor);
            console.log('- Input:', descInput);
            console.log('- Valor inicial:', `"${descInput.value}"`);
            
            // Verificar se o Quill foi inicializado
            if (window.quill_description) {
                console.log('✅ Quill já foi inicializado pelo quill-editor.js');
                
                // Se há valor inicial mas o editor está vazio, recarregar
                if (descInput.value && descInput.value.trim() !== '') {
                    const currentContent = window.quill_description.root.innerHTML;
                    if (currentContent === '<p><br></p>' || currentContent.trim() === '') {
                        console.log('🔄 Recarregando conteúdo inicial no editor...');
                        window.quill_description.root.innerHTML = descInput.value;
                    }
                }
                
                // Verificar se a sincronização está funcionando
                const testSync = () => {
                    setTimeout(() => {
                        console.log('🔍 Teste de sincronização:');
                        console.log('- Conteúdo do editor:', window.quill_description.root.innerHTML);
                        console.log('- Valor do input:', descInput.value);
                    }, 1000);
                };
                
                // Executar teste quando o usuário digitar
                window.quill_description.on('text-change', testSync);
                
            } else {
                console.log('⚠️ Quill não foi inicializado ainda, aguardando...');
                
                // Tentar novamente em mais um pouco
                setTimeout(() => {
                    if (window.quill_description) {
                        console.log('✅ Quill inicializado com delay');
                    } else {
                        console.error('❌ Quill não foi inicializado após timeout');
                    }
                }, 1000);
            }
        } else {
            console.error('❌ Elementos não encontrados');
        }
    }, 300);

    // Sistema dinâmico do content
    const contentType = document.getElementById('content_type');
    const contentWrapper = document.getElementById('content-wrapper');
    const contentLabel = document.querySelector('label[for="content"]');

    function updateContentField() {
        const type = contentType.value;
        let template = '';

        // Preservar o valor atual do content
        const currentContentValue = document.getElementById('content') ? document.getElementById('content').value : '{{ old("content", $content->content) }}';

        switch(type) {
            case 'media':
                contentLabel.innerHTML = 'Selecionar Mídia <span class="text-red-500">*</span>';
                template = `
                    <div class="space-y-4">
                        <input type="hidden" name="media_id" id="media_id" value="${currentContentValue}">
                        <div class="flex items-center gap-4">
                            <button type="button" id="select-media-btn" 
                                class="open-media-modal inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors"
                                data-mediable-type="App\\Models\\PlgModuleContent"
                                data-tag="content_media"
                                data-input-id="media_id"
                                data-multiple="false">
                                <i class="fas fa-image"></i>
                                <span>Selecionar Mídia</span>
                            </button>
                            <button type="button" id="clear-media-btn" 
                                class="inline-flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-times"></i>
                                <span>Limpar</span>
                            </button>
                        </div>
                        <div id="media-preview" class="hidden">
                            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                <div class="flex items-center gap-4">
                                    <div id="media-preview-content" class="flex-1"></div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        <p id="media-filename"></p>
                                        <p id="media-size"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                break;
            case 'video':
                contentLabel.innerHTML = 'Código de Incorporação <span class="text-red-500">*</span>';
                template = `
                    <div class="space-y-4">
                        <textarea name="video_embed" id="video_embed" rows="4"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('video_embed') border-red-500 @enderror"
                            placeholder="Cole o código de incorporação do vídeo (iframe)..."
                            required>${currentContentValue}</textarea>
                        <div id="video-preview" class="hidden">
                            <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Preview:</h4>
                            <div id="video-preview-content" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700"></div>
                        </div>
                    </div>
                `;
                break;
            case 'link':
                contentLabel.innerHTML = 'URL Externa <span class="text-red-500">*</span>';
                template = `
                    <div class="space-y-4">
                        <input type="url" name="external_link" id="external_link"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('external_link') border-red-500 @enderror"
                            placeholder="Cole a URL externa (ex: https://exemplo.com)"
                            value="${currentContentValue}"
                            required>
                        <div id="link-preview" class="hidden">
                            <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Preview:</h4>
                            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                <a href="${currentContentValue}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                    ${currentContentValue}
                                </a>
                            </div>
                        </div>
                    </div>
                `;
                break;
            default:
                contentLabel.innerHTML = 'Conteúdo <span class="text-red-500">*</span>';
                template = `
                    <textarea name="content" id="content" rows="6"
                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('content') border-red-500 @enderror"
                        required>${currentContentValue}</textarea>
                `;
        }

        contentWrapper.innerHTML = template;
    }

    contentType.addEventListener('change', updateContentField);

    // Trigger change event on load to set initial state
    updateContentField();

    // Event listeners para os novos campos dinâmicos
    document.addEventListener('click', function(e) {
        if (e.target.id === 'clear-media-btn') {
            document.getElementById('media_id').value = '';
            document.getElementById('media-preview').classList.add('hidden');
        }
    });

    // Preview de vídeo
    document.addEventListener('input', function(e) {
        if (e.target.id === 'video_embed') {
            const preview = document.getElementById('video-preview');
            const previewContent = document.getElementById('video-preview-content');
            
            if (e.target.value.trim()) {
                preview.classList.remove('hidden');
                previewContent.innerHTML = e.target.value;
            } else {
                preview.classList.add('hidden');
            }
        }

        if (e.target.id === 'external_link') {
            const preview = document.getElementById('link-preview');
            const previewContent = preview.querySelector('a');
            
            if (e.target.value.trim()) {
                preview.classList.remove('hidden');
                previewContent.href = e.target.value;
                previewContent.textContent = e.target.value;
            } else {
                preview.classList.add('hidden');
            }
        }
    });

    function showMediaPreview(media) {
        console.log('🖼️ Mostrando preview da mídia:', media);
        const preview = document.getElementById('media-preview');
        const previewContent = document.getElementById('media-preview-content');
        const filename = document.getElementById('media-filename');
        const size = document.getElementById('media-size');

        if (!preview || !previewContent || !filename || !size) {
            console.error('❌ Elementos de preview não encontrados');
            return;
        }

        filename.textContent = media.filename || 'Arquivo sem nome';
        size.textContent = formatFileSize(media.size || 0);

        if (media.mime_type && media.mime_type.startsWith('image/')) {
            previewContent.innerHTML = `<img src="${media.url}" alt="${media.alt_text || media.filename}" class="w-20 h-20 object-cover rounded">`;
        } else if (media.mime_type && media.mime_type === 'application/pdf') {
            previewContent.innerHTML = `<i class="fas fa-file-pdf text-red-500 text-2xl"></i>`;
        } else if (media.mime_type && media.mime_type.startsWith('audio/')) {
            previewContent.innerHTML = `<i class="fas fa-music text-blue-500 text-2xl"></i>`;
        } else {
            previewContent.innerHTML = `<i class="fas fa-file text-gray-500 text-2xl"></i>`;
        }

        preview.classList.remove('hidden');
        console.log('✅ Preview da mídia exibido');
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Carregar preview inicial se já existe mídia selecionada
    const initialMediaId = document.getElementById('media_id')?.value;
    if (initialMediaId && contentType.value === 'media') {
        // Fazer requisição para buscar dados da mídia
        fetch(`/panel/admin/media/${initialMediaId}/info`)
            .then(response => response.json())
            .then(media => showMediaPreview(media))
            .catch(error => console.error('Erro ao carregar mídia:', error));
    }

    // Event listener para capturar seleção de mídia
    document.addEventListener('mediaSelected', function(e) {
        console.log('🎯 Evento mediaSelected capturado:', e.detail);
        const media = e.detail.media;
        if (media && media.id) {
            const mediaIdInput = document.getElementById('media_id');
            if (mediaIdInput) {
                mediaIdInput.value = media.id;
                showMediaPreview(media);
                console.log('✅ Mídia selecionada:', media.id);
            }
        }
    });
});
</script>
@endpush

@endsection 