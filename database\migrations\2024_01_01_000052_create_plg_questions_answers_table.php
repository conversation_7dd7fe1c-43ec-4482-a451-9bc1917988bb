<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_questions_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('question_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('answer_number');
            $table->boolean('correct')->default(false);
            
            $table->text('answer')->nullable();
            $table->text('explanation')->nullable();

            $table->string('caption')->nullable(); // Campo adicionado posteriormente
            $table->timestamps();
            $table->softDeletes();
            
            // Chave estrangeira com ON DELETE CASCADE
            $table->foreign('question_id')
                ->references('id')->on('plg_questions')
                ->onDelete('cascade');
                
            // Índices para performance
            $table->index(['question_id', 'answer_number']);
            $table->index(['correct']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_questions_answers');
    }
};
