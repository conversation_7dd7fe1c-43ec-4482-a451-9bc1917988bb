@props(['student'])

<!-- Modal de Contato para Módulos <PERSON>gos -->
<div id="contactModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-xl max-w-lg w-full mx-4 transform transition-all">
        <!-- Header do <PERSON> -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-zinc-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-graduation-cap mr-2 text-blue-600"></i>
                Solicitar Matrícula
            </h3>
            <button onclick="closeContactModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- <PERSON><PERSON><PERSON><PERSON> do <PERSON>dal -->
        <div class="p-6">
            <!-- Informações do Módulo -->
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-book text-2xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2" id="modalModuleTitle">
                    Módulo Selecionado
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Valor: <span class="font-semibold text-green-600 text-lg" id="modalModulePrice">R$ 0,00</span>
                </p>
            </div>

            <!-- Informações do Estudante (Pré-preenchidas) -->
            <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 mb-6">
                <h5 class="font-semibold text-gray-900 dark:text-white mb-3">
                    <i class="fas fa-user mr-2"></i>Seus Dados:
                </h5>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Nome:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $student->name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Email:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $student->email }}</span>
                    </div>
                    @if($student->phone)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Telefone:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $student->phone }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Mensagem Personalizada -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-comment mr-1"></i>Mensagem (opcional):
                </label>
                <textarea id="customMessage" rows="3" 
                    class="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-zinc-700 dark:text-white"
                    placeholder="Adicione alguma observação ou pergunta específica..."></textarea>
            </div>

            <!-- Opções de Contato -->
            <div class="space-y-3">
                <p class="text-gray-700 dark:text-gray-300 text-center font-medium mb-4">
                    Escolha como deseja entrar em contato:
                </p>

                <!-- WhatsApp -->
                <button onclick="sendWhatsApp()" 
                   class="w-full flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors border border-green-200 dark:border-green-800">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fab fa-whatsapp text-white text-xl"></i>
                    </div>
                    <div class="text-left flex-1">
                        <p class="font-semibold text-gray-900 dark:text-white">WhatsApp</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Resposta rápida via WhatsApp</p>
                    </div>
                    <i class="fas fa-arrow-right text-green-600"></i>
                </button>

                <!-- Email -->
                <button onclick="sendEmail()" 
                   class="w-full flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors border border-blue-200 dark:border-blue-800">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-envelope text-white text-xl"></i>
                    </div>
                    <div class="text-left flex-1">
                        <p class="font-semibold text-gray-900 dark:text-white">Email</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Contato formal via email</p>
                    </div>
                    <i class="fas fa-arrow-right text-blue-600"></i>
                </button>

                <!-- Formulário Interno (Futuro) -->
                <button onclick="sendInternalForm()" 
                   class="w-full flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors border border-purple-200 dark:border-purple-800">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-paper-plane text-white text-xl"></i>
                    </div>
                    <div class="text-left flex-1">
                        <p class="font-semibold text-gray-900 dark:text-white">Solicitar Agora</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Envio direto pelo sistema</p>
                    </div>
                    <i class="fas fa-arrow-right text-purple-600"></i>
                </button>
            </div>
        </div>

        <!-- Footer do Modal -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-zinc-700 rounded-b-lg">
            <button onclick="closeContactModal()" 
                    class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-times mr-2"></i>Cancelar
            </button>
        </div>
    </div>
</div>

<!-- Dados do estudante para JavaScript -->
<script>
window.studentData = {
    name: @json($student->name),
    email: @json($student->email),
    phone: @json($student->phone ?? ''),
    id: @json($student->id)
};
</script>
