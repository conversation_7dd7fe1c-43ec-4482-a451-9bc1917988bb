import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                // Aplicação principal
                'resources/css/app.css',
                'resources/js/app.js',

                // DataTables
                'resources/css/datatable-custom.css',
                'resources/js/datatable-custom.js',

                // Questões
                'resources/css/questions.css',
                'resources/js/questions.js',

                //Editor de texto
                'resources/js/quill-editor.js',
                'resources/css/quill-editor.css',

                // Netflix Carousel (apenas para páginas de estudante)
                'resources/js/netflix-carousel.js'
            ],
            refresh: true,
        }),
    ],
    build: {
        // Garantir que gere arquivos separados
        rollupOptions: {
            output: {
                manualChunks: {
                    'datatable': [
                        'resources/js/datatable-custom.js',
                        'resources/css/datatable-custom.css',
                    ],
                    'question': [
                        'resources/css/questions.css',
                        'resources/js/questions.js',
                    ],
                },
            },
        },
    },
});