<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MediaUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && in_array(auth()->user()->role, [
            'super_admin', 'admin', 'teacher'
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,gif,webp,pdf,doc,docx,xls,xlsx,zip,rar,mp4,avi,mp3,wav',
                'max:' . $this->getMaxFileSize(),
            ],
            'mediable_type' => 'nullable|string|max:255',
            'tag' => 'nullable|string|max:100',
            'alt_text' => 'nullable|string|max:255',
            'caption' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'Por favor, selecione um arquivo para upload.',
            'file.file' => 'O arquivo enviado não é válido.',
            'file.mimes' => 'Tipo de arquivo não permitido. Tipos aceitos: :values',
            'file.max' => 'O arquivo é muito grande. Tamanho máximo: :max KB.',
            'mediable_type.string' => 'Tipo de mídia deve ser uma string.',
            'tag.string' => 'Tag deve ser uma string.',
            'tag.max' => 'Tag deve ter no máximo :max caracteres.',
            'alt_text.string' => 'Texto alternativo deve ser uma string.',
            'alt_text.max' => 'Texto alternativo deve ter no máximo :max caracteres.',
            'caption.string' => 'Legenda deve ser uma string.',
            'caption.max' => 'Legenda deve ter no máximo :max caracteres.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'arquivo',
            'mediable_type' => 'tipo de mídia',
            'tag' => 'tag',
            'alt_text' => 'texto alternativo',
            'caption' => 'legenda',
        ];
    }

    /**
     * Get max file size based on file type
     */
    private function getMaxFileSize(): int
    {
        $file = $this->file('file');

        if (!$file || !$file->isValid()) {
            return 10240; // 10MB default
        }

        try {
            $mimeType = $file->getMimeType();

            // Se não conseguir obter o MIME type, usar extensão
            if (!$mimeType) {
                $extension = strtolower($file->getClientOriginalExtension());
                $mimeType = $this->getMimeTypeFromExtension($extension);
            }
        } catch (\Exception $e) {
            // Se houver erro ao obter MIME type, usar tamanho padrão
            return 10240;
        }

        // Configurações por tipo de arquivo (em KB)
        $limits = [
            'image' => 5120,    // 5MB para imagens
            'document' => 10240, // 10MB para documentos
            'video' => 51200,   // 50MB para vídeos
            'audio' => 10240,   // 10MB para áudio
            'archive' => 20480, // 20MB para arquivos compactados
        ];

        if (str_starts_with($mimeType, 'image/')) {
            return $limits['image'];
        }

        if (str_starts_with($mimeType, 'video/')) {
            return $limits['video'];
        }

        if (str_starts_with($mimeType, 'audio/')) {
            return $limits['audio'];
        }

        if (in_array($mimeType, ['application/zip', 'application/x-rar-compressed'])) {
            return $limits['archive'];
        }

        return $limits['document'];
    }

    /**
     * Get MIME type from file extension
     */
    private function getMimeTypeFromExtension(string $extension): string
    {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $file = $this->file('file');
            
            if ($file) {
                // Verificar se o arquivo não está corrompido
                if ($file->getError() !== UPLOAD_ERR_OK) {
                    $validator->errors()->add('file', 'Erro no upload do arquivo.');
                }

                // Verificar extensão vs MIME type para segurança
                $extension = strtolower($file->getClientOriginalExtension());
                $mimeType = $file->getMimeType();
                
                if (!$this->isValidMimeTypeForExtension($extension, $mimeType)) {
                    $validator->errors()->add('file', 'Tipo de arquivo não corresponde à extensão.');
                }
            }
        });
    }

    /**
     * Verifica se o MIME type corresponde à extensão
     */
    private function isValidMimeTypeForExtension(string $extension, string $mimeType): bool
    {
        $validCombinations = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'webp' => ['image/webp'],
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'xls' => ['application/vnd.ms-excel'],
            'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            'zip' => ['application/zip'],
            'rar' => ['application/x-rar-compressed'],
            'mp4' => ['video/mp4'],
            'avi' => ['video/x-msvideo', 'video/avi'],
            'mp3' => ['audio/mpeg'],
            'wav' => ['audio/wav', 'audio/x-wav'],
        ];

        return isset($validCombinations[$extension]) && 
               in_array($mimeType, $validCombinations[$extension]);
    }
} 