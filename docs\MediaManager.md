# MediaManager - Documentação Completa

## Visão Geral

O MediaManager é um sistema completo de gerenciamento de mídia para o sistema de quiz, desenvolvido em JavaScript vanilla com integração Laravel. Ele permite upload, seleção, organização e manipulação de arquivos de mídia (imagens, documentos, etc.) através de uma interface intuitiva.

## Características Principais

- **Upload de Mídia**: Suporte a múltiplos formatos de arquivo
- **Seleção Modal**: Interface modal para seleção de mídia
- **Seleção Múltipla**: Suporte a seleção de múltiplos arquivos
- **Preview em Tempo Real**: Visualização instantânea das mídias selecionadas
- **Organização por Categorias**: Filtros e organização por categorias
- **Busca e Filtros**: Sistema de busca avançada
- **Manipulação de Imagens**: Redimensionamento e otimização automática
- **Integração com Laravel**: Usa o pacote Mediable para persistência
- **Segurança**: Validação rigorosa de arquivos e autenticação
- **Performance**: Otimização automática de imagens e cache

## Estrutura dos Arquivos

### Frontend (JavaScript)
```
resources/js/media/
├── MediaManager.js              # Classe principal e inicialização
├── MediaManagerModal.js         # Modal de seleção de mídia
├── MediaManagerStandalone.js    # Gerenciador standalone
├── MediaManagerCore.js          # Funcionalidades core
├── MediaManagerControls.js      # Controles de interface
├── MediaRenderer.js             # Renderização de mídia
├── SidebarManager.js            # Gerenciamento de sidebar
└── MediaAPI.js                  # Comunicação com backend
```

### Backend (Laravel)
```
app/
├── Http/
│   ├── Controllers/
│   │   └── Panel/Admin/
│   │       └── MediaController.php      # Controller principal
│   ├── Requests/
│   │   └── MediaUploadRequest.php       # Validação de upload
│   └── Resources/
│       └── MediaResource.php            # API Resource
├── Services/
│   └── MediaService.php                 # Lógica de negócio
└── Models/
    ├── PlgCourse.php                    # Modelo com Mediable
    ├── PlgQuestion.php                  # Modelo com Mediable
    ├── PlgQuestionAnswer.php            # Modelo com Mediable
    ├── PlgModule.php                    # Modelo com Mediable
    └── PlgCategories.php                # Modelo com Mediable

config/
└── mediable.php                         # Configuração do pacote

routes/
└── web.php                              # Rotas do sistema
```

## Inicialização

### Inicialização Automática

O MediaManager é inicializado automaticamente quando a página carrega:

```javascript
// Inicialização automática
MediaManager.init();
```

### Inicialização Manual

```javascript
// Aguardar DOM estar pronto
document.addEventListener('DOMContentLoaded', () => {
    MediaManager.init();
});
```

## Uso Básico

### 1. Seleção Simples (Uma Mídia)

```html
<!-- Botão para abrir seletor -->
<button type="button" 
        class="open-media-modal"
        data-input-id="thumbnail"
        data-mediable-type="App\Models\PlgCourse"
        data-tag="thumbnail"
        data-multiple="false">
    <i class="fas fa-image"></i>
    <span>Selecionar Imagem</span>
</button>

<!-- Campo hidden para armazenar o ID da mídia -->
<input type="hidden" name="thumbnail" id="thumbnail" value="">

<!-- Container para preview -->
<div class="media-preview hidden" id="thumbnail-preview">
    <!-- Preview será inserido aqui -->
</div>
```

### 2. Seleção Múltipla (Galeria)

```html
<!-- Botão para abrir seletor múltiplo -->
<button type="button" 
        class="open-media-modal"
        data-input-id="gallery"
        data-mediable-type="App\Models\PlgCourse"
        data-tag="gallery"
        data-multiple="true">
    <i class="fas fa-images"></i>
    <span>Selecionar Imagens</span>
</button>

<!-- Campo hidden para armazenar array de IDs -->
<input type="hidden" name="gallery" id="gallery" value="">

<!-- Container para preview múltiplo -->
<div class="media-preview hidden" id="gallery-preview">
    <!-- Previews serão inseridos aqui -->
</div>
```

## Atributos de Configuração

### Atributos Obrigatórios

- `data-input-id`: ID do campo hidden que armazenará o valor selecionado
- `data-mediable-type`: Classe do modelo Laravel (ex: `App\Models\PlgCourse`)
- `data-tag`: Tag para organizar a mídia no modelo

### Atributos Opcionais

- `data-multiple`: `"true"` para seleção múltipla, `"false"` (padrão) para seleção única
- `data-accept`: Tipos de arquivo aceitos (ex: `"image/*"`, `"application/pdf"`)
- `data-preview`: ID do container de preview (se diferente do padrão)
- `data-target`: Target específico (detectado automaticamente se não especificado)

## Exemplos de Implementação

### 1. Thumbnail de Curso

```html
<div class="media-selector">
    <div class="flex justify-between items-center mb-2">
        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">
            Thumbnail do Curso
        </label>
        <button type="button"
            class="open-media-modal inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors"
            data-mediable-type="App\Models\PlgCourse"
            data-tag="thumbnail"
            data-input-id="thumbnail"
            data-multiple="false">
            <i class="fas fa-image"></i>
            <span>Selecionar Imagem</span>
        </button>
    </div>

    <input type="hidden" name="thumbnail" id="thumbnail" value="{{ old('thumbnail', isset($course) && $course->hasMedia('thumbnail') ? $course->firstMedia('thumbnail')->id : '') }}">

    <div class="media-preview {{ isset($course) && $course->hasMedia('thumbnail') ? '' : 'hidden' }}" id="thumbnail-preview">
        @if (isset($course) && $course->hasMedia('thumbnail'))
            @php
                $thumbnailMedia = $course->firstMedia('thumbnail');
            @endphp
            @if($thumbnailMedia)
                <div class="relative">
                    <img src="{{ $thumbnailMedia->getUrl() }}" alt="{{ $course->title ?? 'Thumbnail do curso' }}" class="w-full h-32 object-cover rounded-lg">
                    <button type="button" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
                <p class="text-sm text-zinc-500 mt-2 truncate">{{ $thumbnailMedia->filename }}</p>
            @endif
        @endif
    </div>
    <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendado: 1280x720px</p>
</div>
```

### 2. Imagem de Questão

```html
<div class="media-selector">
    <div class="flex justify-between items-center mb-2">
        <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">
            Imagem da Questão
        </label>
        <button type="button"
            class="open-media-modal inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors"
            data-input-id="question_image" 
            data-mediable-type="App\Models\PlgQuestion"
            data-tag="question_image" 
            data-multiple="false">
            <i class="fas fa-image"></i>
            <span>Selecionar Imagem</span>
        </button>
    </div>

    <input type="hidden" name="question_image" id="question_image" value="{{ old('question_image', $question->image ?? '') }}">

    <div class="media-preview {{ isset($question) && $question->image ? '' : 'hidden' }}">
        @if (isset($question) && $question->image)
            <div class="relative">
                <img src="{{ $question->image }}" alt="Imagem da questão" class="w-full h-32 object-cover rounded-lg">
                <button type="button" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                    <i class="fas fa-times text-xs"></i>
                </button>
            </div>
            <p class="text-sm text-zinc-500 mt-2 truncate">{{ basename($question->image) }}</p>
        @endif
    </div>
    <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF</p>
</div>
```

### 3. Imagem de Alternativa (Múltiplas)

```html
<div class="flex items-center gap-2">
    <input type="text" name="answers[{{ $index }}][answer]" class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 answer-text" value="{{ old("answers.{$index}.answer", $answer->answer) }}">

    <input type="hidden" name="answers[{{ $index }}][image]" id="answer_image_{{ $answer->answer_number }}" class="answer-image-input" value="{{ old("answers.{$index}.image", $answer->image ?? '') }}">
    
    <button type="button"
        class="open-media-modal flex-shrink-0 p-2 bg-zinc-200 hover:bg-zinc-300 dark:bg-zinc-700 dark:hover:bg-zinc-600 text-zinc-700 dark:text-zinc-100 rounded-lg answer-image-button"
        data-input-id="answer_image_{{ $answer->answer_number }}"
        data-mediable-type="App\Models\PlgQuestionAnswer"
        data-tag="answer_image" 
        data-multiple="false">
        <i class="fas fa-image"></i>
    </button>
</div>

<!-- Container para imagem da alternativa -->
<div class="media-preview {{ old("answers.{$index}.image", $answer->image ?? '') ? '' : 'hidden' }} mt-2">
    @if (old("answers.{$index}.image", $answer->image ?? ''))
        <div class="relative">
            <img src="{{ old("answers.{$index}.image", $answer->image ?? '') }}" alt="Imagem da alternativa" class="w-full h-24 object-cover rounded-lg">
            <button type="button" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600" onclick="removeMediaPreview(this)">
                <i class="fas fa-times text-xs"></i>
            </button>
        </div>
        <p class="text-sm text-zinc-500 mt-2 truncate">{{ basename(old("answers.{$index}.image", $answer->image ?? '')) }}</p>
    @endif
</div>
```

## API JavaScript

### MediaManager

#### Métodos Estáticos

```javascript
// Inicializar o sistema
MediaManager.init()

// Abrir seletor programaticamente
MediaManager.openSelector({
    target: 'thumbnail',
    multiple: false,
    onSelect: (media) => console.log('Mídia selecionada:', media)
})

// Fechar modal ativo
MediaManager.closeModal()

// Obter instância da API
const api = MediaManager.getAPI()
```

### MediaAPI

```javascript
// Carregar mídias
const media = await MediaAPI.loadMedia(page, filters)

// Upload de arquivo
const uploadedMedia = await MediaAPI.uploadFile(file)

// Deletar mídia
await MediaAPI.deleteMedia(mediaId)

// Deletar múltiplas mídias
await MediaAPI.deleteMultiple([1, 2, 3])

// Atualizar mídia
await MediaAPI.updateMedia(mediaId, { filename: 'novo-nome.jpg' })

// Obter tipos de mídia
const types = await MediaAPI.getMediaTypes()

// Limpar mídias órfãs
await MediaAPI.cleanOrphaned()
```

## Backend (Laravel) - Arquitetura Completa

### 1. Controller - MediaController

O `MediaController` gerencia todas as operações de mídia:

```php
<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use Plank\Mediable\Media;
use Illuminate\Http\Request;
use App\Services\MediaService;
use App\Http\Resources\MediaResource;
use App\Http\Requests\MediaUploadRequest;

class MediaController extends Controller
{
    protected MediaService $mediaService;

    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    /**
     * Display the media manager page
     */
    public function index()
    {
        return view('panel.admin.media.index');
    }

    /**
     * Load media items via AJAX
     */
    public function loadMore(Request $request)
    {
        try {
            $filters = $request->only(['search', 'type', 'date_from', 'date_to', 'orphaned', 'per_page', 'sort']);
            $page = $request->input('page', 1);
            
            $mediaList = $this->mediaService->getMediaWithFilters($filters, $page);
            
            return response()->json([
                'items' => MediaResource::collection($mediaList->items()),
                'pagination' => $this->formatPagination($mediaList)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao carregar mídias'], 500);
        }
    }

    /**
     * Store a new media file
     */
    public function store(MediaUploadRequest $request)
    {
        try {
            $options = $request->only(['alt_text', 'caption', 'mediable_type', 'tag']);
            $media = $this->mediaService->uploadMedia($request->file('file'), $options);

            return response()->json(new MediaResource($media), 201);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erro no upload: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a media file
     */
    public function destroy(Media $media)
    {
        try {
            $this->mediaService->deleteMedia($media);
            
            return response()->json([
                'success' => true,
                'message' => 'Mídia excluída com sucesso'
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao excluir'], 500);
        }
    }

    /**
     * Delete multiple media files
     */
    public function deleteMultiple(Request $request)
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:media,id'
        ]);

        try {
            $results = $this->mediaService->deleteMultipleMedia($request->input('ids'));

            return response()->json([
                'success' => true,
                'deleted' => $results['deleted'],
                'message' => "{$results['deleted']} mídia(s) excluída(s) com sucesso."
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao excluir'], 500);
        }
    }

    /**
     * Update media information
     */
    public function update(Request $request, Media $media)
    {
        $request->validate([
            'filename' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'caption' => 'nullable|string|max:500',
        ]);

        try {
            $updatedMedia = $this->mediaService->updateMedia($media, $request->all());
            
            return response()->json([
                'success' => true,
                'message' => 'Mídia atualizada com sucesso',
                'media' => new MediaResource($updatedMedia)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao atualizar'], 500);
        }
    }

    /**
     * Get media types
     */
    public function getMediaTypes()
    {
        return response()->json($this->mediaService->getAvailableMediaTypes());
    }

    /**
     * Clean orphaned media
     */
    public function cleanOrphaned()
    {
        try {
            $results = $this->mediaService->cleanOrphanedMedia();
            
            return response()->json([
                'success' => true,
                'deleted' => $results['deleted'],
                'message' => "{$results['deleted']} mídia(s) órfã(s) removida(s)."
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao limpar mídias órfãs'], 500);
        }
    }

    /**
     * Serve protected media files with authentication
     */
    public function serve(Request $request, $path)
    {
        // Verificar se o usuário está autenticado
        if (!Auth::check()) {
            abort(401, 'Acesso não autorizado');
        }

        // Verificar se o usuário tem permissão
        $user = Auth::user();
        if (!in_array($user->role, ['super_admin', 'admin', 'teacher', 'student'])) {
            abort(403, 'Acesso negado');
        }

        // Verificar se o arquivo existe
        $fullPath = storage_path('app/public/' . $path);
        if (!file_exists($fullPath)) {
            abort(404, 'Arquivo não encontrado');
        }

        // Servir arquivo com manipulação de imagem se necessário
        $isImage = $this->isImage($fullPath);
        $hasImageParams = $request->has(['w', 'h', 'fit', 'fm', 'q']);

        if ($isImage && $hasImageParams) {
            return $this->serveProcessedImage($request, $path);
        }

        return $this->serveOriginalFile($fullPath, $path);
    }
}
```

### 2. Service - MediaService

O `MediaService` contém toda a lógica de negócio para gerenciamento de mídia:

```php
<?php

namespace App\Services;

use Plank\Mediable\Media;
use Plank\Mediable\MediaUploader;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\MediaResource;
use Illuminate\Pagination\LengthAwarePaginator;

class MediaService
{
    protected MediaUploader $uploader;
    protected int $perPage = 18;

    public function __construct(MediaUploader $uploader)
    {
        $this->uploader = $uploader;
    }

    /**
     * Busca mídias com filtros e paginação
     */
    public function getMediaWithFilters(array $filters = [], int $page = 1, int $perPage = null): LengthAwarePaginator
    {
        // Determinar quantidade por página
        if (isset($filters['per_page'])) {
            if ($filters['per_page'] === 'all') {
                $perPage = 1000; // Limite razoável para "todos"
            } else {
                $perPage = (int) $filters['per_page'];
            }
        } else {
            $perPage = $perPage ?? $this->perPage;
        }

        // Criar query base com ordenação
        $query = Media::query();
        
        // Aplicar ordenação
        if (isset($filters['sort']) && $filters['sort'] === 'oldest') {
            $query->orderBy('created_at', 'asc');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Aplicar filtros
        $this->applyFilters($query, $filters);
        
        // Por padrão, excluir mídias órfãs
        if (empty($filters['orphaned']) || $filters['orphaned'] !== 'true') {
            $this->applyValidMediaFilter($query);
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Upload de mídia melhorado
     */
    public function uploadMedia(UploadedFile $file, array $options = []): Media
    {
        try {
            DB::beginTransaction();

            // Configurar e fazer upload
            $media = $this->configureUploader($file, $options)->upload();

            // Adicionar metadados
            $this->addMetadata($media, $options);

            // Associar a modelo se especificado
            if (!empty($options['mediable']) && !empty($options['tag'])) {
                $options['mediable']->attachMedia($media, $options['tag']);
            }

            DB::commit();

            Log::info('Mídia uploaded', ['media_id' => $media->id]);

            return $media;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro no upload', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Atualiza informações da mídia
     */
    public function updateMedia(Media $media, array $data): Media
    {
        $updateData = array_filter([
            'filename' => $data['filename'] ?? null,
            'alt' => $data['alt_text'] ?? null,
            'caption' => $data['caption'] ?? null,
        ], fn($value) => $value !== null);

        if (!empty($updateData)) {
            $media->update($updateData);
        }

        return $media;
    }

    /**
     * Substitui arquivo de mídia existente
     */
    public function replaceMediaFile(Media $media, UploadedFile $newFile): Media
    {
        try {
            DB::beginTransaction();

            // Backup dos metadados
            $metadata = [
                'alt' => $media->alt,
                'caption' => $media->caption,
                'mediables' => $media->mediables()->get()
            ];

            // Remover arquivo antigo
            $this->deleteFile($media);

            // Upload do novo arquivo mantendo o ID
            $newPath = $this->uploader
                ->fromSource($newFile)
                ->toDestination($media->disk, dirname($media->getDiskPath()))
                ->upload();

            // Atualizar registro existente
            $media->update([
                'filename' => $newPath->filename,
                'extension' => $newPath->extension,
                'mime_type' => $newPath->mime_type,
                'size' => $newPath->size,
            ]);

            // Restaurar metadados
            $media->update($metadata);

            // Limpar upload temporário
            $newPath->delete();

            DB::commit();

            return $media->fresh();

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Exclui mídia e arquivo físico
     */
    public function deleteMedia(Media $media): bool
    {
        try {
            DB::beginTransaction();

            $this->deleteFile($media);
            $media->delete();

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Exclui múltiplas mídias
     */
    public function deleteMultipleMedia(array $mediaIds): array
    {
        $results = ['deleted' => 0, 'errors' => []];

        foreach ($mediaIds as $mediaId) {
            try {
                $media = Media::find($mediaId);
                if ($media && $this->deleteMedia($media)) {
                    $results['deleted']++;
                }
            } catch (\Exception $e) {
                $results['errors'][] = "Erro ao excluir mídia ID {$mediaId}";
            }
        }

        return $results;
    }

    /**
     * Limpa mídias órfãs (não vinculadas a modelos)
     */
    public function cleanOrphanedMedia(): array
    {
        $orphanedMedia = Media::whereDoesntHave('mediables')->get();
        $deleted = 0;

        foreach ($orphanedMedia as $media) {
            try {
                if ($this->deleteMedia($media)) {
                    $deleted++;
                }
            } catch (\Exception $e) {
                Log::error('Erro ao limpar mídia órfã', [
                    'media_id' => $media->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return ['deleted' => $deleted, 'total' => $orphanedMedia->count()];
    }

    /**
     * Obtém estatísticas de mídia
     */
    public function getMediaStats(): array
    {
        return [
            'total' => Media::count(),
            'orphaned' => Media::whereDoesntHave('mediables')->count(),
            'by_type' => Media::selectRaw('aggregate_type, COUNT(*) as count')
                ->groupBy('aggregate_type')
                ->pluck('count', 'aggregate_type')
                ->toArray(),
            'total_size' => Media::sum('size'),
            'total_size_human' => $this->formatBytes(Media::sum('size'))
        ];
    }

    /**
     * Obtém tipos de mídia disponíveis
     */
    public function getAvailableMediaTypes(): array
    {
        return [
            'image' => [
                'label' => 'Imagens',
                'mime_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                'icon' => 'fas fa-image'
            ],
            'document' => [
                'label' => 'Documentos',
                'mime_types' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
                'extensions' => ['pdf', 'doc', 'docx'],
                'icon' => 'fas fa-file-alt'
            ],
            'video' => [
                'label' => 'Vídeos',
                'mime_types' => ['video/mp4', 'video/avi'],
                'extensions' => ['mp4', 'avi'],
                'icon' => 'fas fa-video'
            ],
            'audio' => [
                'label' => 'Áudios',
                'mime_types' => ['audio/mp3', 'audio/wav'],
                'extensions' => ['mp3', 'wav'],
                'icon' => 'fas fa-music'
            ],
            'archive' => [
                'label' => 'Arquivos Compactados',
                'mime_types' => ['application/zip', 'application/x-rar-compressed'],
                'extensions' => ['zip', 'rar'],
                'icon' => 'fas fa-archive'
            ]
        ];
    }

    // Métodos privados para filtros e utilitários...
    private function applyFilters($query, array $filters): void
    {
        // Implementação dos filtros
    }

    private function configureUploader(UploadedFile $file, array $options): MediaUploader
    {
        // Configuração do uploader
    }

    private function addMetadata(Media $media, array $options): void
    {
        // Adição de metadados
    }

    private function deleteFile(Media $media): void
    {
        // Exclusão do arquivo físico
    }

    private function formatBytes(int $bytes, int $precision = 2): string
    {
        // Formatação de bytes
    }
}
```

### 3. Request - MediaUploadRequest

Validação rigorosa para upload de arquivos:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MediaUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && in_array(auth()->user()->role, [
            'super_admin', 'admin', 'teacher'
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,gif,webp,pdf,doc,docx,xls,xlsx,zip,rar,mp4,avi,mp3,wav',
                'max:' . $this->getMaxFileSize(),
            ],
            'mediable_type' => 'nullable|string|max:255',
            'tag' => 'nullable|string|max:100',
            'alt_text' => 'nullable|string|max:255',
            'caption' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get max file size based on file type
     */
    private function getMaxFileSize(): int
    {
        $file = $this->file('file');
        
        if (!$file) {
            return 10240; // 10MB default
        }

        $mimeType = $file->getMimeType();
        
        // Configurações por tipo de arquivo (em KB)
        $limits = [
            'image' => 5120,    // 5MB para imagens
            'document' => 10240, // 10MB para documentos
            'video' => 51200,   // 50MB para vídeos
            'audio' => 10240,   // 10MB para áudio
            'archive' => 20480, // 20MB para arquivos compactados
        ];

        if (str_starts_with($mimeType, 'image/')) {
            return $limits['image'];
        }

        if (str_starts_with($mimeType, 'video/')) {
            return $limits['video'];
        }

        if (str_starts_with($mimeType, 'audio/')) {
            return $limits['audio'];
        }

        if (in_array($mimeType, ['application/zip', 'application/x-rar-compressed'])) {
            return $limits['archive'];
        }

        return $limits['document'];
    }

    /**
     * Verifica se o MIME type corresponde à extensão
     */
    private function isValidMimeTypeForExtension(string $extension, string $mimeType): bool
    {
        $validCombinations = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'webp' => ['image/webp'],
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'xls' => ['application/vnd.ms-excel'],
            'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            'zip' => ['application/zip'],
            'rar' => ['application/x-rar-compressed'],
            'mp4' => ['video/mp4'],
            'avi' => ['video/x-msvideo', 'video/avi'],
            'mp3' => ['audio/mpeg'],
            'wav' => ['audio/wav', 'audio/x-wav'],
        ];

        return isset($validCombinations[$extension]) && 
               in_array($mimeType, $validCombinations[$extension]);
    }
}
```

### 4. Resource - MediaResource

Transformação de dados para API:

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class MediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'filename' => $this->filename,
            'original_filename' => $this->filename . '.' . $this->extension,
            'extension' => $this->extension,
            'mime_type' => $this->mime_type,
            'size' => $this->size,
            'size_human' => $this->formatBytes($this->size),
            'url' => $this->getOptimizedUrl(),
            'original_url' => $this->getOriginalUrl(),
            'exists' => $this->fileExists(),
            'created_at' => $this->created_at->format('d/m/Y H:i:s'),
            'created_at_diff' => $this->created_at->diffForHumans(),
            'is_image' => str_starts_with($this->mime_type, 'image/'),
            'thumbnail_url' => $this->getThumbnailUrl(),
            'download_url' => $this->getDownloadUrl(),
        ];
    }

    /**
     * Gera URL otimizada para exibição
     */
    private function getOptimizedUrl(int $width = 250, int $height = 150): string
    {
        $path = $this->getDiskPath();
        $baseUrl = route('media.serve', ['path' => $path]);
        
        if (str_starts_with($this->mime_type, 'image/')) {
            $params = [
                'w' => $width,
                'h' => $height,
                'fit' => 'crop',
                'fm' => 'webp'
            ];
            
            return $baseUrl . '?' . http_build_query($params);
        }
        
        return $baseUrl;
    }

    /**
     * URL original sem otimizações
     */
    private function getOriginalUrl(): string
    {
        return route('media.serve', ['path' => $this->getDiskPath()]);
    }

    /**
     * URL para thumbnail pequeno
     */
    private function getThumbnailUrl(): string
    {
        if (!str_starts_with($this->mime_type, 'image/')) {
            return $this->getFileTypeIcon();
        }

        $path = $this->getDiskPath();
        $baseUrl = route('media.serve', ['path' => $path]);
        
        $params = [
            'w' => 100,
            'h' => 100,
            'fit' => 'crop',
            'fm' => 'webp'
        ];
        
        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * URL para download
     */
    private function getDownloadUrl(): string
    {
        return route('media.serve', [
            'path' => $this->getDiskPath(),
            'download' => 1
        ]);
    }

    /**
     * Verifica se o arquivo existe fisicamente
     */
    private function fileExists(): bool
    {
        $path = $this->getDiskPath();
        $fullPath = storage_path('app/public/' . $path);
        return file_exists($fullPath);
    }

    /**
     * Formata bytes em formato legível
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Ícone para tipos de arquivo não-imagem
     */
    private function getFileTypeIcon(): string
    {
        $icons = [
            'application/pdf' => 'fas fa-file-pdf text-red-500',
            'application/msword' => 'fas fa-file-word text-blue-500',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'fas fa-file-word text-blue-500',
            'application/vnd.ms-excel' => 'fas fa-file-excel text-green-500',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'fas fa-file-excel text-green-500',
            'application/zip' => 'fas fa-file-archive text-yellow-500',
            'application/x-rar-compressed' => 'fas fa-file-archive text-yellow-500',
            'video/mp4' => 'fas fa-file-video text-purple-500',
            'video/avi' => 'fas fa-file-video text-purple-500',
            'audio/mp3' => 'fas fa-file-audio text-pink-500',
            'audio/wav' => 'fas fa-file-audio text-pink-500',
        ];

        return $icons[$this->mime_type] ?? 'fas fa-file text-gray-500';
    }
}
```

### 5. Modelos com Mediable

Exemplo de modelo que usa o trait Mediable:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Plank\Mediable\Mediable;
use Plank\Mediable\MediableInterface;

class PlgCourse extends Model implements MediableInterface
{
    use Mediable;

    protected $table = 'plg_courses';
    
    protected $mediableCollections = ['thumbnail'];
    
    protected $fillable = [
        'title', 'slug', 'description', 'short_description', 
        'category_id', 'user_id', 'status',
        'featured', 'duration_minutes', 'company_id'
    ];
    
    /**
     * Relacionamento com categoria
     */
    public function category()
    {
        return $this->belongsTo(PlgCategories::class, 'category_id');
    }
    
    /**
     * Relacionamento com professor
     */
    public function teacher()
    {
        return $this->belongsTo(SysUser::class, 'user_id');
    }
    
    /**
     * Relacionamento com módulos
     */
    public function modules()
    {
        return $this->hasMany(PlgModule::class, 'course_id')
                    ->orderBy('order');
    }

    /**
     * Acessor para thumbnail
     */
    public function getThumbnailAttribute()
    {
        return $this->firstMedia('thumbnail');
    }

    /**
     * Acessor para URL do thumbnail
     */
    public function getThumbnailUrlAttribute()
    {
        $media = $this->firstMedia('thumbnail');
        return $media ? $media->getUrl() : null;
    }
}
```

### 6. Configuração - mediable.php

Configuração do pacote Mediable:

```php
<?php

return [
    /*
     * The database connection name to use
     */
    'connection_name' => null,

    /*
     * FQCN of the model to use for media
     */
    'model' => Plank\Mediable\Media::class,

    /*
     * Name to be used for mediables joining table
     */
    'mediables_table' => 'mediables',

    /*
     * Filesystem disk to use if none is specified
     */
    'default_disk' => 'public',

    /*
     * Filesystems that can be used for media storage
     */
    'allowed_disks' => [
        'public',
    ],

    /*
     * The maximum file size in bytes for a single uploaded file
     */
    'max_size' => 1024 * 1024 * 10, // 10MB

    /*
     * What to do if a duplicate file is uploaded.
     */
    'on_duplicate' => Plank\Mediable\MediaUploader::ON_DUPLICATE_INCREMENT,

    /*
     * Reject files unless both their mime and extension are recognized
     */
    'strict_type_checking' => false,

    /*
     * Reject files whose mime type or extension is not recognized
     */
    'allow_unrecognized_types' => false,

    /*
     * Only allow files with specific MIME type(s) to be uploaded
     */
    'allowed_mime_types' => [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/zip',
        'application/x-rar-compressed',
        'video/mp4',
        'video/avi',
        'audio/mp3',
        'audio/wav',
    ],

    /*
     * List of aggregate types recognized by the application
     */
    'aggregate_types' => [
        Plank\Mediable\Media::TYPE_IMAGE => [
            'mime_types' => [
                'image/webp',
                'image/jpeg',
                'image/png',
                'image/gif',
            ],
            'extensions' => [
                'webp',
                'jpg',
                'jpeg',
                'png',
                'gif',
            ]
        ],
        Plank\Mediable\Media::TYPE_PDF => [
            'mime_types' => [
                'application/pdf',
            ],
            'extensions' => [
                'pdf',
            ]
        ],
        Plank\Mediable\Media::TYPE_AUDIO => [
            'mime_types' => [
                'audio/mpeg',
                'audio/mp3',
                'audio/wav'
            ],
            'extensions' => [
                'mp3',
                'wav',
            ]
        ],
        Plank\Mediable\Media::TYPE_VIDEO => [
            'mime_types' => [
                'video/mp4',
                'video/avi'
            ],
            'extensions' => [
                'mp4',
                'avi',
            ]
        ],
        Plank\Mediable\Media::TYPE_ARCHIVE => [
            'mime_types' => [
                'application/zip',
                'application/x-rar-compressed',
            ],
            'extensions' => [
                'zip',
                'rar',
            ]
        ],
        Plank\Mediable\Media::TYPE_DOCUMENT => [
            'mime_types' => [
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'extensions' => [
                'doc',
                'docx',
                'xls',
                'xlsx',
            ]
        ],
    ],
];
```

### 7. Rotas

Configuração das rotas no `routes/web.php`:

```php
// Rota protegida para servir mídias com autenticação
Route::middleware('auth')->get('/media/{path}', [MediaController::class, 'serve'])
    ->where('path', '.*')
    ->name('media.serve');

// Media routes
Route::prefix('panel/admin/media')->name('admin.media.')->middleware('auth')->group(function () {
    Route::get('/', [MediaController::class, 'index'])->name('index');
    Route::get('/load-more', [MediaController::class, 'loadMore'])->name('load-more');
    Route::get('/types', [MediaController::class, 'getMediaTypes'])->name('types');
    Route::get('/stats', [MediaController::class, 'stats'])->name('stats');
    Route::post('/', [MediaController::class, 'store'])->name('store');
    Route::get('/{media}/info', [MediaController::class, 'info'])->name('info');
    Route::delete('/{media}', [MediaController::class, 'destroy'])->name('destroy');
    Route::put('/{media}', [MediaController::class, 'update'])->name('update');
    Route::post('/{media}/replace', [MediaController::class, 'replace'])->name('replace');
    Route::post('/delete-multiple', [MediaController::class, 'deleteMultiple'])->name('delete-multiple');
    Route::post('/clean-orphaned', [MediaController::class, 'cleanOrphaned'])->name('clean-orphaned');
    
    // Modal AJAX
    Route::get('/modal', function () {
        return view('panel.admin.media.partials.modal');
    })->name('modal');
});
```

## Funcionalidades Avançadas

### 1. Filtros e Busca

O MediaManager suporta filtros avançados:

- **Busca por texto**: Nome do arquivo, alt text, caption
- **Filtro por tipo**: Imagens, documentos, vídeos
- **Filtro por data**: Período de upload
- **Mídias órfãs**: Arquivos não vinculados a modelos
- **Ordenação**: Por nome, data, tamanho

### 2. Manipulação de Imagens

- **Redimensionamento automático**: Configurável por tag
- **Otimização**: Compressão automática
- **Formatos suportados**: JPG, PNG, GIF, WebP
- **Transformações**: Crop, resize, watermark

### 3. Sidebar de Informações

- **Detalhes da mídia**: Tamanho, dimensões, formato
- **Metadados**: Alt text, caption, tags
- **Histórico**: Data de upload, modificações
- **Ações rápidas**: Editar, deletar, substituir

### 4. Upload Drag & Drop

- **Arrastar arquivos**: Para upload direto
- **Múltiplos arquivos**: Upload em lote
- **Progresso**: Barra de progresso em tempo real
- **Validação**: Verificação de tipo e tamanho

## Eventos JavaScript

```javascript
// Evento de seleção de mídia
document.addEventListener('mediaSelected', (event) => {
    const { media, target, multiple } = event.detail;
    console.log('Mídia selecionada:', media);
});

// Evento de upload concluído
document.addEventListener('mediaUploaded', (event) => {
    const { media } = event.detail;
    console.log('Upload concluído:', media);
});

// Evento de mídia deletada
document.addEventListener('mediaDeleted', (event) => {
    const { mediaId } = event.detail;
    console.log('Mídia deletada:', mediaId);
});
```



## Troubleshooting

### Problemas Comuns

1. **Modal não abre**
   - Verificar se o MediaManager foi inicializado
   - Verificar se há conflitos de CSS/JS
   - Verificar console para erros

2. **Upload falha**
   - Verificar permissões de pasta storage
   - Verificar configuração do filesystem
   - Verificar tamanho máximo de upload

3. **Preview não aparece**
   - Verificar se o container tem a classe `media-preview`
   - Verificar se o ID do input está correto
   - Verificar se o MediaRenderer está funcionando

4. **Mídia não salva**
   - Verificar se o modelo usa o trait Mediable
   - Verificar se as tags estão configuradas
   - Verificar se o ID da mídia está sendo enviado

### Debug

```javascript
// Habilitar logs detalhados
MediaManager.debug = true;

// Verificar inicialização
console.log('MediaManager initialized:', MediaManager.initialized);

// Verificar instâncias ativas
console.log('Standalone instance:', MediaManager.standaloneInstance);
console.log('Modal instance:', MediaManager.activeModalInstance);
```

## Boas Práticas

1. **Sempre use data-input-id**: Para garantir que o valor seja salvo corretamente
2. **Configure data-mediable-type**: Para organização adequada da mídia
3. **Use tags descritivas**: Para facilitar a organização
4. **Implemente preview**: Para melhor UX
5. **Valide tipos de arquivo**: No frontend e backend
6. **Configure tamanhos máximos**: Para evitar problemas de performance
7. **Use lazy loading**: Para grandes galerias
8. **Implemente cache**: Para melhor performance

## Exemplo Completo

```html
<!-- Formulário completo com MediaManager -->
<form method="POST" action="{{ route('admin.courses.store') }}" enctype="multipart/form-data">
    @csrf
    
    <!-- Thumbnail -->
    <div class="media-selector">
        <label>Thumbnail</label>
        <button type="button" 
                class="open-media-modal"
                data-input-id="thumbnail"
                data-mediable-type="App\Models\PlgCourse"
                data-tag="thumbnail"
                data-multiple="false">
            Selecionar Imagem
        </button>
        <input type="hidden" name="thumbnail" id="thumbnail" value="">
        <div class="media-preview hidden" id="thumbnail-preview"></div>
    </div>
    
    <!-- Galeria -->
    <div class="media-selector">
        <label>Galeria de Imagens</label>
        <button type="button" 
                class="open-media-modal"
                data-input-id="gallery"
                data-mediable-type="App\Models\PlgCourse"
                data-tag="gallery"
                data-multiple="true">
            Selecionar Imagens
        </button>
        <input type="hidden" name="gallery" id="gallery" value="">
        <div class="media-preview hidden" id="gallery-preview"></div>
    </div>
    
    <button type="submit">Salvar</button>
</form>

<script>
document.addEventListener('DOMContentLoaded', () => {
    MediaManager.init();
});
</script>
```

Esta documentação cobre todos os aspectos do MediaManager, desde o uso básico até funcionalidades avançadas. Para dúvidas específicas ou problemas, consulte os logs do console e verifique a configuração do backend. 