<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlgTest;
use App\Models\PlgTestAttempt;
use App\Models\PlgTestReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TestReportsController extends Controller
{
    /**
     * Exibe a página principal de relatórios
     */
    public function index()
    {
        $tests = PlgTest::with('module.course')
            ->where('active', true)
            ->orderBy('title')
            ->get();

        return view('panel.admin.reports.tests.index', compact('tests'));
    }

    /**
     * Relatório detalhado de um teste específico
     */
    public function show(PlgTest $test)
    {
        // Estatísticas gerais
        $stats = $this->getTestStatistics($test);

        // Tentativas recentes
        $recentAttempts = PlgTestAttempt::with(['student'])
            ->where('test_id', $test->id)
            ->orderBy('started_at', 'desc')
            ->limit(10)
            ->get();

        // Questões com mais erros (baseado no JSON de respostas)
        $difficultQuestions = $this->getDifficultQuestions($test);

        return view('panel.admin.reports.tests.show', compact(
            'test', 'stats', 'recentAttempts', 'difficultQuestions'
        ));
    }

    /**
     * Relatório de tentativas de um estudante específico
     */
    public function studentAttempts(PlgTest $test, $studentId)
    {
        $attempts = PlgTestAttempt::with(['student'])
            ->where('test_id', $test->id)
            ->where('student_id', $studentId)
            ->orderBy('attempt_number', 'desc')
            ->get();

        return view('panel.admin.reports.tests.student-attempts', compact('test', 'attempts'));
    }

    /**
     * Exportar relatório em CSV
     */
    public function exportCsv(PlgTest $test)
    {
        $attempts = PlgTestAttempt::with(['student'])
            ->where('test_id', $test->id)
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->get();

        $filename = "relatorio_teste_{$test->id}_" . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($attempts) {
            $file = fopen('php://output', 'w');
            
            // Cabeçalho
            fputcsv($file, [
                'Estudante',
                'Email',
                'Tentativa',
                'Iniciado em',
                'Finalizado em',
                'Tempo Gasto (min)',
                'Nota',
                'Total Questões',
                'Passou',
                'Status'
            ]);

            // Dados
            foreach ($attempts as $attempt) {
                $timeSpent = $attempt->started_at && $attempt->completed_at
                    ? round($attempt->started_at->diffInMinutes($attempt->completed_at), 2)
                    : 0;

                $totalQuestions = count($attempt->answers ?? []);

                fputcsv($file, [
                    $attempt->student->name,
                    $attempt->student->email,
                    $attempt->attempt_number,
                    $attempt->started_at->format('d/m/Y H:i:s'),
                    $attempt->completed_at ? $attempt->completed_at->format('d/m/Y H:i:s') : '',
                    $timeSpent,
                    $attempt->score,
                    $totalQuestions,
                    $attempt->passed ? 'Sim' : 'Não',
                    ucfirst($attempt->status)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * API para dados do dashboard de relatórios
     */
    public function dashboardData(Request $request)
    {
        $testId = $request->get('test_id');
        $period = $request->get('period', '30'); // dias

        $query = PlgTestAttempt::query();

        if ($testId) {
            $query->where('test_id', $testId);
        }

        $query->where('started_at', '>=', now()->subDays($period));

        // Tentativas por dia
        $attemptsByDay = $query->clone()
            ->selectRaw('DATE(started_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Taxa de aprovação
        $passRate = $query->clone()
            ->where('status', 'completed')
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN passed = 1 THEN 1 ELSE 0 END) as passed
            ')
            ->first();

        // Tempo médio (calculado pela diferença entre started_at e completed_at)
        $completedAttempts = $query->clone()
            ->where('status', 'completed')
            ->whereNotNull('completed_at')
            ->get();

        $avgTimeMinutes = 0;
        if ($completedAttempts->count() > 0) {
            $totalMinutes = $completedAttempts->sum(function($attempt) {
                return $attempt->started_at->diffInMinutes($attempt->completed_at);
            });
            $avgTimeMinutes = round($totalMinutes / $completedAttempts->count(), 2);
        }

        return response()->json([
            'attempts_by_day' => $attemptsByDay,
            'pass_rate' => $passRate ? ($passRate->passed / $passRate->total * 100) : 0,
            'avg_time_minutes' => $avgTimeMinutes,
            'total_attempts' => $query->count()
        ]);
    }

    /**
     * Obter estatísticas gerais do teste
     */
    private function getTestStatistics(PlgTest $test)
    {
        $attempts = PlgTestAttempt::where('test_id', $test->id);

        // Calcular tempo médio baseado na diferença entre started_at e completed_at
        $completedAttempts = $attempts->clone()->where('status', 'completed')->whereNotNull('completed_at')->get();
        $avgTimeMinutes = 0;
        if ($completedAttempts->count() > 0) {
            $totalMinutes = $completedAttempts->sum(function($attempt) {
                return $attempt->started_at->diffInMinutes($attempt->completed_at);
            });
            $avgTimeMinutes = round($totalMinutes / $completedAttempts->count());
        }

        return [
            'total_attempts' => $attempts->count(),
            'completed_attempts' => $attempts->where('status', 'completed')->count(),
            'in_progress_attempts' => $attempts->where('status', 'in_progress')->count(),
            'abandoned_attempts' => $attempts->where('status', 'abandoned')->count(),
            'pass_rate' => $this->calculatePassRate($test),
            'avg_score' => $attempts->where('status', 'completed')->avg('score'),
            'avg_time_minutes' => $avgTimeMinutes,
            'unique_students' => $attempts->distinct('student_id')->count(),
        ];
    }

    /**
     * Calcular taxa de aprovação
     */
    private function calculatePassRate(PlgTest $test)
    {
        $completed = PlgTestAttempt::where('test_id', $test->id)
            ->where('status', 'completed')
            ->count();

        if ($completed === 0) return 0;

        $passed = PlgTestAttempt::where('test_id', $test->id)
            ->where('status', 'completed')
            ->where('passed', true)
            ->count();

        return round(($passed / $completed) * 100);
    }

    /**
     * Obter questões mais difíceis (baseado no JSON de respostas)
     */
    private function getDifficultQuestions(PlgTest $test)
    {
        $attempts = PlgTestAttempt::where('test_id', $test->id)
            ->where('status', 'completed')
            ->whereNotNull('answers')
            ->get();

        $questionStats = [];

        foreach ($attempts as $attempt) {
            $answers = $attempt->answers ?? [];

            foreach ($answers as $answer) {
                $questionId = $answer['question_id'];

                if (!isset($questionStats[$questionId])) {
                    $questionStats[$questionId] = [
                        'total_answers' => 0,
                        'correct_answers' => 0,
                    ];
                }

                $questionStats[$questionId]['total_answers']++;
                if ($answer['is_correct']) {
                    $questionStats[$questionId]['correct_answers']++;
                }
            }
        }

        // Calcular taxa de sucesso e buscar dados das questões
        $difficultQuestions = collect();

        foreach ($questionStats as $questionId => $stats) {
            if ($stats['total_answers'] >= 3) { // Mínimo 3 respostas para considerar
                $successRate = round(($stats['correct_answers'] / $stats['total_answers']) * 100);

                $question = \App\Models\PlgQuestion::find($questionId);
                if ($question) {
                    $difficultQuestions->push((object)[
                        'id' => $questionId,
                        'question' => $question->question,
                        'total_answers' => $stats['total_answers'],
                        'correct_answers' => $stats['correct_answers'],
                        'success_rate' => $successRate
                    ]);
                }
            }
        }

        return $difficultQuestions->sortBy('success_rate')->take(10);
    }


}
