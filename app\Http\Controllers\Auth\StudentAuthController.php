<?php

namespace App\Http\Controllers\Auth;

use App\Models\PlgStudent;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class StudentAuthController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.student.login');
    }

    public function showRegistrationForm()
    {
        return view('auth.student.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:plg_students'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        $student = PlgStudent::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'active' => true,
            'company_id' => 1,
        ]);

        Auth::guard('students')->login($student);
        
        return redirect()->route('student.dashboard');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::guard('students')->attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();
            return redirect()->route('student.dashboard');
        }
        
        throw ValidationException::withMessages([
            'email' => ['As credenciais não correspondem aos registros de estudante.'],
        ]);
    }

    public function showForgotForm()
    {
        return view('auth.student.forgot-password');
    }

    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => ['required', 'email']]);

        $student = PlgStudent::where('email', $request->email)->first();
        if (!$student) {
            throw ValidationException::withMessages([
                'email' => ['Este email não está registrado como estudante.'],
            ]);
        }

        // Implementar envio de email de recuperação aqui
        return back()->with('status', 'Link de recuperação enviado para seu email!');
    }

    public function logout(Request $request)
    {
        Auth::guard('students')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('student.login');
    }
} 