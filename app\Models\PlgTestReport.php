<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlgTestReport extends Model
{
    use HasFactory;

    protected $table = 'plg_test_reports';

    protected $fillable = [
        'company_id',
        'test_id',
        'student_id',
        'best_score',
        'total_attempts',
        'last_attempt_at',
        'passed',
    ];

    protected $casts = [
        'best_score' => 'decimal:2',
        'total_attempts' => 'integer',
        'last_attempt_at' => 'datetime',
        'passed' => 'boolean',
    ];

    /**
     * Relacionamento com o teste.
     */
    public function test(): BelongsTo
    {
        return $this->belongsTo(PlgTest::class, 'test_id');
    }

    /**
     * Relacionamento com o estudante.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(PlgStudent::class, 'student_id');
    }

    /**
     * Obtém todas as tentativas do estudante para este teste.
     */
    public function attempts()
    {
        return PlgTestAttempt::where('test_id', $this->test_id)
            ->where('student_id', $this->student_id)
            ->orderBy('attempt_number', 'desc')
            ->get();
    }

    /**
     * Obtém a melhor tentativa do estudante.
     */
    public function bestAttempt()
    {
        return PlgTestAttempt::where('test_id', $this->test_id)
            ->where('student_id', $this->student_id)
            ->where('status', 'completed')
            ->orderBy('score', 'desc')
            ->first();
    }

    /**
     * Obtém a última tentativa do estudante.
     */
    public function lastAttempt()
    {
        return PlgTestAttempt::where('test_id', $this->test_id)
            ->where('student_id', $this->student_id)
            ->orderBy('attempt_number', 'desc')
            ->first();
    }

    /**
     * Verifica se o estudante pode fazer uma nova tentativa.
     */
    public function canMakeNewAttempt(): bool
    {
        $test = $this->test;
        
        // Se permite tentativas ilimitadas
        if ($test->max_attempts == 0) {
            return true;
        }

        // Verifica se ainda tem tentativas disponíveis
        return $this->total_attempts < $test->max_attempts;
    }

    /**
     * Obtém o próximo número de tentativa.
     */
    public function getNextAttemptNumber(): int
    {
        return $this->total_attempts + 1;
    }

    /**
     * Obtém estatísticas básicas.
     */
    public function getStats(): array
    {
        return [
            'best_score' => $this->best_score ?? 0,
            'total_attempts' => $this->total_attempts,
            'passed' => $this->passed,
            'last_attempt_at' => $this->last_attempt_at,
            'can_attempt' => $this->canMakeNewAttempt(),
            'next_attempt_number' => $this->getNextAttemptNumber(),
        ];
    }
}
