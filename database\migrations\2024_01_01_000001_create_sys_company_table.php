<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_company', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('planId');
            $table->string('company', 60);
            $table->string('responsible', 60)->nullable();
            $table->string('companyCountry', 60)->nullable();
            $table->string('companyEmail', 150)->nullable();
            $table->string('companySite', 145)->nullable();
            $table->string('companyPhone1', 15)->nullable();
            $table->string('companyPhone2', 15)->nullable();
            $table->string('companyPhone3', 15)->nullable();
            $table->string('companySession', 50)->nullable();
            $table->string('companyLegalName', 150)->nullable();
            $table->string('companyBrazilianRegistration', 65)->nullable();
            $table->string('companyStateRegistration', 65)->nullable();
            $table->timestamp('dateCreate')->useCurrent();
            $table->string('ftpHost', 120)->nullable();
            $table->string('ftpSubdomain', 90)->nullable();
            $table->string('ftpLogin', 150)->nullable();
            $table->string('ftpPassword', 150)->nullable();
            $table->date('mkt')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_company');
    }
};
