import { Notyf } from 'notyf';

// Configuração do Notyf
const notyf = new Notyf({
    duration: 4000,
    position: {
        x: 'right',
        y: 'top',
    },
    types: [
        {
            type: 'warning',
            background: '#f59e0b',
            icon: {
                className: 'fas fa-exclamation-triangle',
                tagName: 'i',
                color: 'white'
            }
        },
        {
            type: 'info',
            background: '#3b82f6',
            icon: {
                className: 'fas fa-info-circle',
                tagName: 'i',
                color: 'white'
            }
        }
    ]
});

// API simplificada para notificações
export const notify = {
    success: (message) => notyf.success(message),
    error: (message) => notyf.error(message),
    warning: (message) => notyf.open({ type: 'warning', message }),
    info: (message) => notyf.open({ type: 'info', message }),
    
    // Método customizado
    show: (message, type = 'info') => {
        switch(type) {
            case 'success': return notify.success(message);
            case 'error': return notify.error(message);
            case 'warning': return notify.warning(message);
            default: return notify.info(message);
        }
    }
};

// Disponibilizar globalmente
window.notify = notify;

export default notify; 