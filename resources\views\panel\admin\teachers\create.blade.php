@extends('layouts.panel')

@section('title', 'Add Teachers')
@section('page_title', 'Add Teachers')

@section('content')
<div class="container mx-auto py-6 px-6">
    <h1 class="text-2xl font-bold mb-4">Add Teacher</h1>
    <div class="bg-white shadow rounded p-4">
        <form action="{{ route('admin.teachers.store') }}" method="POST">
            @csrf
            <div class="mb-4">
                <label class="block text-gray-700">Name</label>
                <input type="text" name="name" class="w-full border rounded px-3 py-2" value="{{ old('name') }}" required>
                @error('name')<div class="text-red-600 text-sm">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Email</label>
                <input type="email" name="email" class="w-full border rounded px-3 py-2" value="{{ old('email') }}" required>
                @error('email')<div class="text-red-600 text-sm">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Password</label>
                <input type="password" name="password" class="w-full border rounded px-3 py-2" required>
                @error('password')<div class="text-red-600 text-sm">{{ $message }}</div>@enderror
            </div>
            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Save</button>
            <a href="{{ route('admin.teachers.index') }}" class="ml-2 text-gray-600 hover:underline">Cancel</a>
        </form>
    </div>
</div>
@endsection 