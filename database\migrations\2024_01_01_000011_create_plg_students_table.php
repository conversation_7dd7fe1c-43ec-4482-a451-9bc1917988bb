<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_students', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->boolean('active')->default(true);

            $table->string('name');
            $table->string('apelido')->nullable(); // Apelido opcional
            $table->string('email')->unique();
            $table->string('password');
            $table->string('document')->nullable(); // CPF/CNPJ em um campo
            $table->string('crm')->nullable(); // CRM opcional
            $table->string('avatar')->nullable();
            $table->string('phone1')->nullable(); // Telefone principal
            $table->boolean('is_phone1_whatsapp')->default(false); // Se phone1 é WhatsApp
            $table->string('phone2')->nullable(); // Telefone secundário
            $table->boolean('is_phone2_whatsapp')->default(false); // Se phone2 é WhatsApp
            $table->date('birth_date')->nullable();
            
            $table->rememberToken();

            $table->timestamp('last_login')->nullable();

            // Sistema de aprovação
            $table->enum('registration_status', ['pending', 'approved', 'rejected', 'blocked'])->default('pending');
            $table->text('admin_notes')->nullable();

            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('sys_users')->onDelete('set null');

            // Índices para performance
            $table->index(['company_id', 'registration_status']); // Multi-tenancy + status
            $table->index(['registration_status']);
            $table->index(['approved_at']);
            $table->index(['email', 'registration_status']);
            $table->index(['document']); // Para busca por CPF/CNPJ
            $table->index(['phone1']); // Para busca por telefone
            $table->index(['company_id', 'email']); // Multi-tenancy + email
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_students');
    }
};
