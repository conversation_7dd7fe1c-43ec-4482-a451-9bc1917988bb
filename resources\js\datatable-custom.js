// Importar jQuery
import $ from 'jquery';

// Importar DataTables
import 'datatables.net';
import 'datatables.net-responsive';

// Tornar jQuery disponível globalmente para DataTables
window.$ = window.jQuery = $;

// Configuração padrão do DataTables
$(document).ready(function() {
    // Aplicar DataTables automaticamente em tabelas com classe 'datatable'
    if ($('.datatable').length > 0) {
        $('.datatable').DataTable({
            responsive: true,
            language: {
                url: '/js/datatables/i18n/pt-BR.json'
            },
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Todos"]],
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
    }
});