@extends('layouts.panel')

@section('title', 'Relatório: ' . $test->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <div class="flex items-center gap-2 mb-2">
                <a href="{{ route('admin.reports.tests.index') }}" 
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400">
                    <i class="fas fa-arrow-left mr-2"></i>Voltar aos Relatórios
                </a>
            </div>
            <h1 class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">{{ $test->name }}</h1>
            <p class="text-zinc-600 dark:text-zinc-400">
                {{ $test->module->course->name }} - {{ $test->module->name }}
            </p>
        </div>
        
        <div class="flex gap-3">
            <a href="{{ route('admin.reports.tests.export', $test) }}" 
                class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-download mr-2"></i>Exportar CSV
            </a>
        </div>
    </div>

    <!-- Estatísticas Gerais -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <i class="fas fa-play text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Total de Tentativas</p>
                    <p class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">{{ $stats['total_attempts'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Taxa de Aprovação</p>
                    <p class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">{{ $stats['pass_rate'] }}%</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Tempo Médio</p>
                    <p class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">
                        {{ $stats['avg_time'] ? round($stats['avg_time'] / 60) . ' min' : '-' }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <i class="fas fa-users text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Estudantes Únicos</p>
                    <p class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">{{ $stats['unique_students'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Status das Tentativas -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100 mb-4">
                Status das Tentativas
            </h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">Concluídas</span>
                    <span class="text-sm font-medium text-green-600 dark:text-green-400">
                        {{ $stats['completed_attempts'] }}
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">Em Progresso</span>
                    <span class="text-sm font-medium text-blue-600 dark:text-blue-400">
                        {{ $stats['in_progress_attempts'] }}
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">Abandonadas</span>
                    <span class="text-sm font-medium text-red-600 dark:text-red-400">
                        {{ $stats['abandoned_attempts'] }}
                    </span>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100 mb-4">
                Configurações do Teste
            </h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">Tipo</span>
                    <span class="text-sm font-medium text-zinc-800 dark:text-zinc-100">
                        {{ ucfirst($test->test_type) }}
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">Máx. Tentativas</span>
                    <span class="text-sm font-medium text-zinc-800 dark:text-zinc-100">
                        {{ $test->allowsUnlimitedAttempts() ? 'Ilimitado' : $test->max_attempts }}
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-zinc-600 dark:text-zinc-400">Tempo Limite</span>
                    <span class="text-sm font-medium text-zinc-800 dark:text-zinc-100">
                        {{ $test->time_limit_minutes ? $test->time_limit_minutes . ' min' : 'Sem limite' }}
                    </span>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100 mb-4">
                Nota Média
            </h3>
            <div class="text-center">
                <div class="text-3xl font-bold text-zinc-800 dark:text-zinc-100 mb-2">
                    {{ $stats['avg_score'] ? round($stats['avg_score'], 1) : '-' }}
                </div>
                <div class="text-sm text-zinc-600 dark:text-zinc-400">
                    de 100 pontos
                </div>
            </div>
        </div>
    </div>

    <!-- Questões Mais Difíceis -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm mb-8">
        <div class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">
                Questões Mais Difíceis
            </h3>
            <p class="text-sm text-zinc-600 dark:text-zinc-400">
                Questões com menor taxa de acerto
            </p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-zinc-50 dark:bg-zinc-900/50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Questão
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Total de Respostas
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Acertos
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Taxa de Sucesso
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                    @forelse($difficultQuestions as $question)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50">
                            <td class="px-6 py-4">
                                <div class="text-sm text-zinc-900 dark:text-zinc-100">
                                    {{ Str::limit($question->question, 100) }}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $question->total_answers }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $question->correct_answers }}
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-zinc-200 dark:bg-zinc-700 rounded-full h-2 mr-3">
                                        <div class="bg-{{ $question->success_rate >= 70 ? 'green' : ($question->success_rate >= 50 ? 'yellow' : 'red') }}-500 h-2 rounded-full" 
                                             style="width: {{ $question->success_rate }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                        {{ $question->success_rate }}%
                                    </span>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-zinc-500 dark:text-zinc-400">
                                Nenhuma questão respondida ainda.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Tentativas Recentes -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">
                Tentativas Recentes
            </h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-zinc-50 dark:bg-zinc-900/50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Estudante
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Tentativa
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Iniciado em
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Nota
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                    @forelse($recentAttempts as $attempt)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50">
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                    {{ $attempt->student->name }}
                                </div>
                                <div class="text-sm text-zinc-500 dark:text-zinc-400">
                                    {{ $attempt->student->email }}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                #{{ $attempt->attempt_number }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $attempt->started_at ? $attempt->started_at->format('d/m/Y H:i') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $attempt->status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 
                                       ($attempt->status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : 
                                        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400') }}">
                                    {{ ucfirst($attempt->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $attempt->score ? round($attempt->score, 1) : '-' }}
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <a href="{{ route('admin.reports.tests.student-attempts', [$test, $attempt->student_id]) }}" 
                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                    <i class="fas fa-eye mr-1"></i>Ver Detalhes
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-zinc-500 dark:text-zinc-400">
                                Nenhuma tentativa encontrada.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
