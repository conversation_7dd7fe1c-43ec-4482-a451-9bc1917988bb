// Modal de Contato para Módulos Pagos
console.log('Contact Modal JS carregado via Vite!');

// Variáveis globais para o modal
let currentModule = {
    title: '',
    price: ''
};

/**
 * Abrir modal de contato
 */
window.openContactModal = function(moduleTitle, modulePrice) {
    console.log('Abrindo modal para:', moduleTitle, modulePrice);
    
    // Armazenar dados do módulo
    currentModule.title = moduleTitle;
    currentModule.price = modulePrice;
    
    // Atualizar informações do módulo no modal
    const modalTitleElement = document.getElementById('modalModuleTitle');
    const modalPriceElement = document.getElementById('modalModulePrice');
    
    if (!modalTitleElement || !modalPriceElement) {
        console.error('Elementos do modal não encontrados!');
        alert('Erro: Modal não encontrado na página!');
        return;
    }
    
    modalTitleElement.textContent = moduleTitle;
    modalPriceElement.textContent = 'R$ ' + modulePrice;
    
    // Mostrar modal
    const modal = document.getElementById('contactModal');
    if (!modal) {
        console.error('Modal contactModal não encontrado!');
        alert('Erro: Modal não encontrado!');
        return;
    }
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    document.body.style.overflow = 'hidden';
};

/**
 * Fechar modal de contato
 */
window.closeContactModal = function() {
    const modal = document.getElementById('contactModal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.style.overflow = 'auto';
        
        // Limpar mensagem personalizada
        const customMessage = document.getElementById('customMessage');
        if (customMessage) {
            customMessage.value = '';
        }
    }
};

/**
 * Enviar via WhatsApp
 */
window.sendWhatsApp = function() {
    const customMessage = document.getElementById('customMessage')?.value || '';
    const studentData = window.studentData || {};
    
    let message = `🎓 *SOLICITAÇÃO DE MATRÍCULA*\n\n`;
    message += `📚 *Módulo:* ${currentModule.title}\n`;
    message += `💰 *Valor:* R$ ${currentModule.price}\n\n`;
    message += `👤 *Dados do Estudante:*\n`;
    message += `• Nome: ${studentData.name}\n`;
    message += `• Email: ${studentData.email}\n`;
    if (studentData.phone) {
        message += `• Telefone: ${studentData.phone}\n`;
    }
    
    if (customMessage.trim()) {
        message += `\n💬 *Mensagem:*\n${customMessage}`;
    }
    
    message += `\n\n_Enviado via TrendsQuiz_`;
    
    const whatsappUrl = `https://wa.me/5511999999999?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
    
    closeContactModal();
};

/**
 * Enviar via Email
 */
window.sendEmail = function() {
    const customMessage = document.getElementById('customMessage')?.value || '';
    const studentData = window.studentData || {};
    
    const subject = `Solicitação de Matrícula - ${currentModule.title}`;
    
    let body = `Olá!\n\n`;
    body += `Gostaria de solicitar matrícula no seguinte módulo:\n\n`;
    body += `📚 Módulo: ${currentModule.title}\n`;
    body += `💰 Valor: R$ ${currentModule.price}\n\n`;
    body += `👤 Meus dados:\n`;
    body += `• Nome: ${studentData.name}\n`;
    body += `• Email: ${studentData.email}\n`;
    if (studentData.phone) {
        body += `• Telefone: ${studentData.phone}\n`;
    }
    
    if (customMessage.trim()) {
        body += `\n💬 Observações:\n${customMessage}\n`;
    }
    
    body += `\nAguardo retorno com as instruções para pagamento e acesso.\n\n`;
    body += `Atenciosamente,\n${studentData.name}`;
    
    const emailUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = emailUrl;
    
    closeContactModal();
};

/**
 * Enviar via formulário interno (futuro)
 */
window.sendInternalForm = function() {
    const customMessage = document.getElementById('customMessage')?.value || '';
    const studentData = window.studentData || {};
    
    // Por enquanto, mostrar um alert
    alert('🚧 Funcionalidade em desenvolvimento!\n\nPor enquanto, use WhatsApp ou Email para solicitar sua matrícula.');
    
    // TODO: Implementar envio via AJAX para o backend
    /*
    const formData = {
        module_title: currentModule.title,
        module_price: currentModule.price,
        student_id: studentData.id,
        custom_message: customMessage,
        contact_method: 'internal_form'
    };
    
    fetch('/panel/student/contact/module-request', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Solicitação enviada com sucesso!\n\nEntraremos em contato em breve.');
            closeContactModal();
        } else {
            alert('❌ Erro ao enviar solicitação. Tente novamente.');
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('❌ Erro ao enviar solicitação. Tente novamente.');
    });
    */
};

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado, configurando event listeners do modal...');
    
    // Fechar modal ao clicar no overlay
    const modal = document.getElementById('contactModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeContactModal();
            }
        });
    }

    // Fechar modal com ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeContactModal();
        }
    });
});
