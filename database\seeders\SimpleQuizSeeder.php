<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class SimpleQuizSeeder extends Seeder
{
    private $companyId;
    private $professorIds = [];
    private $categoryIds = [];
    private $courseIds = [];
    private $moduleIds = [];
    private $freeQuestionIds = [];
    private $paidQuestionIds = [];

    public function run(): void
    {
        $this->command->info('🚀 Criando sistema completo TrendsQuiz REVALIDA com estrutura robusta...');

        $this->createCompany();
        $this->createAdministrators();
        $this->createMedicalProfessors();
        $this->createMedicalStudents();
        $this->createMedicalCategories();
        $this->createComprehensiveMedicalCourses();
        $this->createVariedMedicalModules();
        $this->createComprehensiveRevalidaQuestions();
        $this->createVariedMedicalTests();
        $this->createModuleContents();

        $this->command->info('✅ Sistema TrendsQuiz REVALIDA populado com estrutura completa!');
        $this->displaySystemSummary();
    }

    private function createCompany(): void
    {
        $company = DB::table('sys_company')->where('company', 'TrendsQuiz')->first();
        if (!$company) {
            $this->companyId = DB::table('sys_company')->insertGetId([
                'planId' => 1,
                'company' => 'TrendsQuiz',
                'responsible' => 'Dr. Administrador',
                'companyCountry' => 'Brasil',
                'companyEmail' => '<EMAIL>',
                'companySite' => 'https://trendsquiz.med.br',
                'companyPhone1' => '(11) 3456-7890',
                'companyLegalName' => 'TrendsQuiz Educação Médica LTDA',
                'companyBrazilianRegistration' => '12.345.678/0001-90',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $this->companyId = $company->id;
        }
    }

    private function createAdministrators(): void
    {
        $this->command->info('👑 Criando administradores do sistema...');

        $administrators = [
            [
                'name' => 'Super Admin Principal',
                'email' => '<EMAIL>',
                'role' => 'super_admin'
            ],
            [
                'name' => 'Super Admin Secundário',
                'email' => '<EMAIL>',
                'role' => 'super_admin'
            ]
        ];

        foreach ($administrators as $admin) {
            $existing = DB::table('sys_users')->where('email', $admin['email'])->first();
            if (!$existing) {
                DB::table('sys_users')->insert([
                    'company_id' => $this->companyId,
                    'name' => $admin['name'],
                    'email' => $admin['email'],
                    'password' => Hash::make('123456'),
                    'role' => $admin['role'],
                    'active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function createMedicalProfessors(): void
    {
        $this->command->info('👨‍⚕️ Criando professores médicos especializados...');

        $professors = [
            [
                'name' => 'Dr. Ana Carolina Buffara Blitzkow',
                'email' => '<EMAIL>',
                'specialty' => 'Cirurgia Geral e REVALIDA'
            ],
            [
                'name' => 'Dr. Roberto Silva Santos',
                'email' => '<EMAIL>',
                'specialty' => 'Trauma e Emergência'
            ],
            [
                'name' => 'Dra. Maria Fernanda Costa',
                'email' => '<EMAIL>',
                'specialty' => 'Cirurgia de Emergência'
            ],
            [
                'name' => 'Dr. João Pedro Oliveira',
                'email' => '<EMAIL>',
                'specialty' => 'Pediatria Cirúrgica'
            ],
        ];

        foreach ($professors as $prof) {
            $existing = DB::table('sys_users')->where('email', $prof['email'])->first();
            if (!$existing) {
                $professorId = DB::table('sys_users')->insertGetId([
                    'company_id' => $this->companyId,
                    'name' => $prof['name'],
                    'email' => $prof['email'],
                    'password' => Hash::make('123456'),
                    'role' => 'teacher',
                    'active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->professorIds[] = $professorId;
            } else {
                $this->professorIds[] = $existing->id;
            }
        }
    }

    private function createMedicalStudents(): void
    {
        $this->command->info('👨‍🎓 Criando estudantes de medicina...');

        $students = [
            ['name' => 'Carlos Eduardo Mendes', 'email' => '<EMAIL>', 'year' => '6º ano'],
            ['name' => 'Beatriz Almeida Santos', 'email' => '<EMAIL>', 'year' => '5º ano'],
            ['name' => 'Rafael Pereira Lima', 'email' => '<EMAIL>', 'year' => '6º ano'],
            ['name' => 'Juliana Costa Ferreira', 'email' => '<EMAIL>', 'year' => 'Residente R1'],
            ['name' => 'Lucas Rodrigues Silva', 'email' => '<EMAIL>', 'year' => '4º ano'],
            ['name' => 'Amanda Oliveira Souza', 'email' => '<EMAIL>', 'year' => '6º ano'],
            ['name' => 'Gabriel Santos Martins', 'email' => '<EMAIL>', 'year' => 'Residente R2'],
            ['name' => 'Larissa Fernandes Costa', 'email' => '<EMAIL>', 'year' => '5º ano'],
            ['name' => 'Thiago Alves Pereira', 'email' => '<EMAIL>', 'year' => '6º ano'],
            ['name' => 'Camila Ribeiro Santos', 'email' => '<EMAIL>', 'year' => 'Formado'],
        ];

        foreach ($students as $student) {
            $existing = DB::table('plg_students')->where('email', $student['email'])->first();
            if (!$existing) {
                DB::table('plg_students')->insert([
                    'company_id' => $this->companyId,
                    'name' => $student['name'],
                    'email' => $student['email'],
                    'password' => Hash::make('123456'),
                    'active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function createMedicalCategories(): void
    {
        $categories = [
            ['title' => 'Cirurgia Geral', 'slug' => 'cirurgia-geral', 'description' => 'Questões de cirurgia geral e procedimentos cirúrgicos'],
            ['title' => 'Trauma e Emergência', 'slug' => 'trauma-emergencia', 'description' => 'Atendimento de trauma e situações de emergência'],
            ['title' => 'Gastroenterologia Cirúrgica', 'slug' => 'gastro-cirurgica', 'description' => 'Cirurgias do aparelho digestivo'],
            ['title' => 'Pediatria Cirúrgica', 'slug' => 'pediatria-cirurgica', 'description' => 'Cirurgias pediátricas e neonatais'],
            ['title' => 'Queimaduras', 'slug' => 'queimaduras', 'description' => 'Tratamento de queimaduras e lesões térmicas'],
        ];

        foreach ($categories as $cat) {
            $existing = DB::table('plg_categories')->where('slug', $cat['slug'])->first();
            if (!$existing) {
                $categoryId = DB::table('plg_categories')->insertGetId([
                    'company_id' => $this->companyId,
                    'user_id' => $this->professorIds[0],
                    'title' => $cat['title'],
                    'slug' => $cat['slug'],
                    'description' => $cat['description'],
                    'active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->categoryIds[] = $categoryId;
            } else {
                $this->categoryIds[] = $existing->id;
            }
        }
    }

    private function createComprehensiveMedicalCourses(): void
    {
        $this->command->info('📚 Criando cursos médicos abrangentes...');

        $courses = [
            [
                'title' => 'REVALIDA - Preparação Completa',
                'slug' => 'revalida-preparacao-completa',
                'description' => 'Curso preparatório completo para REVALIDA com todas as especialidades e casos clínicos reais',
                'category_id' => $this->categoryIds[0],
                'professor_id' => $this->professorIds[0],
                'price' => 199.90,
                'is_free' => false
            ],
            [
                'title' => 'Cirurgia Geral - Fundamentos',
                'slug' => 'cirurgia-geral-fundamentos',
                'description' => 'Fundamentos essenciais de cirurgia geral para estudantes e residentes',
                'category_id' => $this->categoryIds[0],
                'professor_id' => $this->professorIds[0],
                'price' => 89.90,
                'is_free' => false
            ],
            [
                'title' => 'Trauma e Emergência - Curso Gratuito',
                'slug' => 'trauma-emergencia-gratuito',
                'description' => 'Introdução gratuita ao atendimento de trauma e emergências médicas',
                'category_id' => $this->categoryIds[1],
                'professor_id' => $this->professorIds[1],
                'price' => 0.00,
                'is_free' => true
            ],
            [
                'title' => 'Pediatria Cirúrgica Avançada',
                'slug' => 'pediatria-cirurgica-avancada',
                'description' => 'Curso avançado de cirurgia pediátrica com casos complexos',
                'category_id' => $this->categoryIds[3],
                'professor_id' => $this->professorIds[3],
                'price' => 149.90,
                'is_free' => false
            ],
            [
                'title' => 'Queimaduras - Tratamento Especializado',
                'slug' => 'queimaduras-tratamento',
                'description' => 'Especialização em tratamento de queimaduras e lesões térmicas',
                'category_id' => $this->categoryIds[4],
                'professor_id' => $this->professorIds[2],
                'price' => 129.90,
                'is_free' => false
            ],
        ];

        foreach ($courses as $course) {
            $existing = DB::table('plg_courses')->where('slug', $course['slug'])->first();
            if (!$existing) {
                $courseId = DB::table('plg_courses')->insertGetId([
                    'company_id' => $this->companyId,
                    'user_id' => $course['professor_id'],
                    'category_id' => $course['category_id'],
                    'title' => $course['title'],
                    'slug' => $course['slug'],
                    'description' => $course['description'],
                    'status' => 'published',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->courseIds[] = $courseId;
            } else {
                $this->courseIds[] = $existing->id;
            }
        }
    }

    private function createVariedMedicalModules(): void
    {
        $this->command->info('📖 Criando módulos médicos com estratégias de monetização variadas...');

        $modules = [
            // ========== CURSO 1: REVALIDA - ESTRATÉGIA FREEMIUM ==========
            // 1º módulo GRÁTIS para atrair estudantes
            ['title' => 'Introdução ao REVALIDA - GRATUITO', 'slug' => 'intro-revalida-gratuito', 'course_id' => $this->courseIds[0], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 1],
            // Módulos pagos com preços crescentes
            ['title' => 'Clínica Médica Básica', 'slug' => 'clinica-medica-basica', 'course_id' => $this->courseIds[0], 'price' => 49.90, 'is_free' => false, 'active' => true, 'order' => 2],
            ['title' => 'Cirurgia Geral Essencial', 'slug' => 'cirurgia-geral-essencial', 'course_id' => $this->courseIds[0], 'price' => 69.90, 'is_free' => false, 'active' => true, 'order' => 3],
            ['title' => 'Pediatria Avançada', 'slug' => 'pediatria-avancada', 'course_id' => $this->courseIds[0], 'price' => 89.90, 'is_free' => false, 'active' => true, 'order' => 4],
            ['title' => 'Ginecologia e Obstetrícia PREMIUM', 'slug' => 'gineco-obstetricia-premium', 'course_id' => $this->courseIds[0], 'price' => 149.90, 'is_free' => false, 'active' => true, 'order' => 5],

            // ========== CURSO 2: CIRURGIA - ESTRATÉGIA MISTA ==========
            // Módulos básicos gratuitos
            ['title' => 'Anatomia Cirúrgica - GRATUITO', 'slug' => 'anatomia-cirurgica-gratuito', 'course_id' => $this->courseIds[1], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 1],
            ['title' => 'Instrumentação Básica - GRATUITO', 'slug' => 'instrumentacao-basica-gratuito', 'course_id' => $this->courseIds[1], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 2],
            // Módulos intermediários pagos
            ['title' => 'Técnicas de Sutura', 'slug' => 'tecnicas-sutura', 'course_id' => $this->courseIds[1], 'price' => 39.90, 'is_free' => false, 'active' => true, 'order' => 3],
            ['title' => 'Cirurgia Videolaparoscópica', 'slug' => 'cirurgia-videolaparoscopica', 'course_id' => $this->courseIds[1], 'price' => 79.90, 'is_free' => false, 'active' => true, 'order' => 4],
            // Módulo avançado gratuito (estratégia de retenção)
            ['title' => 'Complicações Cirúrgicas - GRATUITO', 'slug' => 'complicacoes-cirurgicas-gratuito', 'course_id' => $this->courseIds[1], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 5],

            // ========== CURSO 3: TRAUMA - ESTRATÉGIA DEGUSTAÇÃO ==========
            // Vários módulos gratuitos para demonstrar qualidade
            ['title' => 'Avaliação Inicial do Trauma - GRATUITO', 'slug' => 'avaliacao-inicial-trauma-gratuito', 'course_id' => $this->courseIds[2], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 1],
            ['title' => 'FAST Protocol - GRATUITO', 'slug' => 'fast-protocol-gratuito', 'course_id' => $this->courseIds[2], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 2],
            ['title' => 'Primeiros Socorros - GRATUITO', 'slug' => 'primeiros-socorros-gratuito', 'course_id' => $this->courseIds[2], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 3],
            // Módulos especializados pagos
            ['title' => 'Trauma Cranioencefálico Avançado', 'slug' => 'trauma-cranio-avancado', 'course_id' => $this->courseIds[2], 'price' => 99.90, 'is_free' => false, 'active' => true, 'order' => 4],
            ['title' => 'Politraumatismo Complexo', 'slug' => 'politraumatismo-complexo', 'course_id' => $this->courseIds[2], 'price' => 129.90, 'is_free' => false, 'active' => true, 'order' => 5],

            // ========== CURSO 4: PEDIATRIA - ESTRATÉGIA ESPECIALIZAÇÃO ==========
            // Módulos básicos gratuitos
            ['title' => 'Pediatria Básica - GRATUITO', 'slug' => 'pediatria-basica-gratuito', 'course_id' => $this->courseIds[3], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 1],
            // Módulos especializados pagos
            ['title' => 'Estenose Hipertrófica do Piloro', 'slug' => 'estenose-piloro', 'course_id' => $this->courseIds[3], 'price' => 69.90, 'is_free' => false, 'active' => true, 'order' => 2],
            ['title' => 'Complicações Pós-Operatórias Pediátricas', 'slug' => 'complicacoes-pos-op-pediatricas', 'course_id' => $this->courseIds[3], 'price' => 89.90, 'is_free' => false, 'active' => true, 'order' => 3],

            // ========== CURSO 5: EMERGÊNCIA - ESTRATÉGIA PREMIUM ==========
            // 1 módulo gratuito + vários pagos
            ['title' => 'Triagem de Emergência - GRATUITO', 'slug' => 'triagem-emergencia-gratuito', 'course_id' => $this->courseIds[4], 'price' => 0.00, 'is_free' => true, 'active' => true, 'order' => 1],
            ['title' => 'Parada Cardiorrespiratória', 'slug' => 'parada-cardiorrespiratoria', 'course_id' => $this->courseIds[4], 'price' => 79.90, 'is_free' => false, 'active' => true, 'order' => 2],
            ['title' => 'Trauma Penetrante Avançado', 'slug' => 'trauma-penetrante-avancado', 'course_id' => $this->courseIds[4], 'price' => 94.90, 'is_free' => false, 'active' => true, 'order' => 3],
            ['title' => 'Queimaduras Complexas PREMIUM', 'slug' => 'queimaduras-complexas-premium', 'course_id' => $this->courseIds[4], 'price' => 149.90, 'is_free' => false, 'active' => true, 'order' => 4],
        ];

        foreach ($modules as $module) {
            $existing = DB::table('plg_modules')->where('slug', $module['slug'])->first();
            if (!$existing) {
                $moduleId = DB::table('plg_modules')->insertGetId([
                    'company_id' => $this->companyId,
                    'user_id' => $this->professorIds[array_rand($this->professorIds)],
                    'course_id' => $module['course_id'],
                    'title' => $module['title'],
                    'slug' => $module['slug'],
                    'description' => "Módulo especializado sobre {$module['title']} com casos clínicos reais, questões práticas e conteúdo baseado no REVALIDA. " .
                                   ($module['is_free'] ? "Conteúdo gratuito para introdução ao tema." : "Conteúdo premium com acesso completo."),
                    'order' => $module['order'] ?? 1,
                    'price' => $module['price'],
                    'is_free' => $module['is_free'],
                    'is_new_release' => $module['price'] >= 100.00, // Módulos premium são novidades
                    'is_featured' => $module['price'] >= 100.00, // Módulos premium em destaque
                    'is_on_sale' => rand(1, 4) === 1, // 25% chance de estar em promoção
                    'price_old' => $module['price'] > 0 && rand(1, 3) === 1 ? $module['price'] * 1.2 : null, // Preço antigo para alguns
                    'marketing_description' => $this->getMarketingDescription($module),
                    'marketing_tags' => json_encode($this->getMarketingTags($module)),
                    'duration_months' => $module['is_free'] ? null : 12, // Módulos pagos têm duração de 12 meses
                    'active' => $module['active'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->moduleIds[] = $moduleId;

                $this->command->info("   ✅ Módulo criado: {$module['title']} " .
                    ($module['is_free'] ? "(GRATUITO)" : "(R$ " . number_format($module['price'], 2, ',', '.') . ")"));
            } else {
                $this->moduleIds[] = $existing->id;
            }
        }
    }

    private function getMarketingDescription(array $module): string
    {
        if ($module['is_free']) {
            return "Módulo introdutório gratuito perfeito para iniciantes. Conteúdo de qualidade para começar seus estudos.";
        }

        if ($module['price'] >= 100.00) {
            return "Módulo premium com conteúdo avançado e casos clínicos complexos. Ideal para profissionais que buscam excelência.";
        }

        return "Módulo completo com teoria e prática. Conteúdo baseado no REVALIDA com casos reais e questões comentadas.";
    }

    private function getMarketingTags(array $module): array
    {
        $tags = ['REVALIDA', 'Medicina'];

        if ($module['is_free']) {
            $tags[] = 'Gratuito';
            $tags[] = 'Introdutório';
        } else {
            $tags[] = 'Premium';
            $tags[] = 'Completo';
        }

        if ($module['price'] >= 100.00) {
            $tags[] = 'Avançado';
            $tags[] = 'Especialização';
            $tags[] = 'Premium';
        }

        return $tags;
    }

    private function createComprehensiveRevalidaQuestions(): void
    {
        $this->command->info('📝 Criando questões REVALIDA abrangentes...');

        // Criar questões GRATUITAS primeiro
        $this->createFreeQuestions();

        // Criar questões PAGAS baseadas no REVALIDA
        $this->createPaidRevalidaQuestions();

        // Criar questões de MÚLTIPLA ESCOLHA
        $this->createMultipleChoiceQuestions();
    }

    private function createFreeQuestions(): void
    {
        $this->command->info('🆓 Criando questões gratuitas...');

        $freeQuestions = [
            [
                'question' => 'Qual é o principal sinal clínico da apendicite aguda?',
                'type' => 'single',
                'category' => 0,
                'answers' => [
                    ['text' => 'Dor migratória de periumbilical para fossa ilíaca direita', 'correct' => true, 'explanation' => 'A dor migratória é o sinal mais característico da apendicite aguda, ocorrendo em cerca de 60% dos casos.'],
                    ['text' => 'Febre alta persistente acima de 39°C', 'correct' => false],
                    ['text' => 'Vômitos biliosos abundantes', 'correct' => false],
                    ['text' => 'Diarreia sanguinolenta', 'correct' => false],
                ]
            ],
            [
                'question' => 'Em relação ao trauma abdominal fechado, qual exame é considerado padrão ouro para avaliação inicial?',
                'type' => 'single',
                'category' => 1,
                'answers' => [
                    ['text' => 'Radiografia simples de abdome', 'correct' => false],
                    ['text' => 'FAST (Focused Assessment with Sonography for Trauma)', 'correct' => true, 'explanation' => 'O FAST é o exame inicial de escolha para detecção rápida de líquido livre intraperitoneal em pacientes traumatizados.'],
                    ['text' => 'Ressonância magnética de abdome', 'correct' => false],
                    ['text' => 'Lavado peritoneal diagnóstico', 'correct' => false],
                ]
            ],
            [
                'question' => 'Qual a principal indicação para cricotireoidotomia de emergência?',
                'type' => 'single',
                'category' => 1,
                'answers' => [
                    ['text' => 'Trauma facial leve', 'correct' => false],
                    ['text' => 'Obstrução de via aérea superior com impossibilidade de intubação', 'correct' => true, 'explanation' => 'A cricotireoidotomia é indicada quando há obstrução grave da via aérea e a intubação é impossível ou muito arriscada.'],
                    ['text' => 'Pneumotórax simples', 'correct' => false],
                    ['text' => 'Fratura de costela isolada', 'correct' => false],
                ]
            ],
        ];

        foreach ($freeQuestions as $questionData) {
            $questionId = DB::table('plg_questions')->insertGetId([
                'company_id' => $this->companyId,
                'user_id' => $this->professorIds[array_rand($this->professorIds)],
                'question_type' => $questionData['type'],
                'question' => $questionData['question'],
                'image' => rand(1, 3) === 1 ? $this->getRandomMedicalImage() : null,
                'free' => true, // QUESTÃO GRATUITA
                'imported' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->freeQuestionIds[] = $questionId;

            foreach ($questionData['answers'] as $index => $answerData) {
                DB::table('plg_questions_answers')->insert([
                    'company_id' => $this->companyId,
                    'question_id' => $questionId,
                    'answer_number' => $index + 1,
                    'answer' => $answerData['text'],
                    'correct' => $answerData['correct'],
                    'explanation' => $answerData['explanation'] ?? null,
                    'caption' => rand(1, 4) === 1 ? $this->getRandomImageCaption() : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Associar à categoria
            if (isset($this->categoryIds[$questionData['category']])) {
                DB::table('plg_questions_x_category')->insert([
                    'company_id' => $this->companyId,
                    'question_id' => $questionId,
                    'category_id' => $this->categoryIds[$questionData['category']],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function createPaidRevalidaQuestions(): void
    {
        $this->command->info('💰 Criando questões pagas baseadas no REVALIDA...');

        $paidQuestions = [
            [
                'question' => 'Paciente feminina, 45 anos, com histórico de obesidade, apresenta dor em hipocôndrio direito há 24 horas, febre de 38,5°C e náuseas. Ao exame: sinal de Murphy positivo, defesa à palpação profunda do quadrante superior direito. Foram solicitados exames laboratoriais (leucocitose, elevação discreta de bilirrubinas) e ultrassonografia de abdome, que revelou:

• Cálculo impactado no infundíbulo vesicular;
• Vesícula distendida (>5 cm de diâmetro transversal);
• Edema de parede vesicular;
• Sinal da parede trilaminada (camadas hiperecoica-hipoecoica-hiperecoica).

Com base no quadro clínico e imagem, analise as afirmativas abaixo:
1. O sinal de Murphy ultrassonográfico positivo é altamente sensível para colecistite aguda.
2. A presença de cálculo impactado no infundíbulo é patognomônica de colecistite aguda.
3. A parede trilaminada indica inflamação e edema da vesícula biliar.
4. A conduta inicial em pacientes estáveis é antibioticoterapia isolada por 14 dias.
5. A colecistectomia laparoscópica precoce é recomendada na maioria dos casos estáveis.

Assinale a alternativa correta:',
                'type' => 'single',
                'category' => 0,
                'answers' => [
                    [
                        'text' => 'Apenas 1, 3 e 5 estão corretas.',
                        'correct' => true,
                        'explanation' => 'O sinal de Murphy ultrassonográfico é considerado um achado altamente sensível para colecistite aguda. A presença de cálculo impactado no infundíbulo, embora sugestiva, não é patognomônica. A parede trilaminada é um sinal clássico de edema e inflamação da parede vesicular. A antibioticoterapia isolada não é suficiente - o tratamento definitivo é a colecistectomia laparoscópica precoce.'
                    ],
                    ['text' => 'Apenas 1, 2 e 4 estão corretas.', 'correct' => false],
                    ['text' => 'Apenas 2, 3 e 5 estão corretas.', 'correct' => false],
                    ['text' => 'Todas estão corretas.', 'correct' => false],
                ]
            ],
            [
                'question' => 'Paciente masculino, 35 anos, vítima de colisão motocicleta × poste. Encontra-se taquicárdico (FC 120 bpm), com PA 80 × 60 mmHg e sonolento. Realizado FAST (Focused Assessment with Sonography for Trauma) que revelou líquido livre em hipocôndrio direito e pelve. Assinale a conduta mais adequada no cenário descrito:',
                'type' => 'single',
                'category' => 1,
                'answers' => [
                    ['text' => 'Solicitar TC de abdome com contraste para estadiamento lesional.', 'correct' => false],
                    ['text' => 'Reposição com 2 L de cristaloide e reavaliação clínica.', 'correct' => false],
                    [
                        'text' => 'Laparotomia exploradora imediata.',
                        'correct' => true,
                        'explanation' => 'Paciente com trauma abdominal contuso, instável (PAS < 100 mmHg) e FAST positivo tem indicação de laparotomia imediata. Tomografia é reservada para pacientes estáveis. Cristaloide isolado pode atrasar a abordagem cirúrgica.'
                    ],
                    ['text' => 'Tratamento conservador com observação em UTI com monitorização hemodinâmica.', 'correct' => false],
                ]
            ],
            [
                'question' => 'Homem, 25 anos, queixa-se de dor abdominal há 18 horas, iniciada em região periumbilical com migração para fossa ilíaca direita, febre de 38,4 ºC, náuseas e inapetência. Exame físico: dor localizada, sinal de Blumberg +. Qual a conduta mais adequada?',
                'type' => 'single',
                'category' => 0,
                'answers' => [
                    ['text' => 'Antibioticoterapia (ciprofloxacino mais metronidazol) via oral por 10 dias com observação ambulatorial.', 'correct' => false],
                    ['text' => 'Solicitar ressonância magnética de abdome para confirmar o diagnóstico.', 'correct' => false],
                    [
                        'text' => 'Internação hospitalar, antibioticoterapia e apendicectomia laparoscópica.',
                        'correct' => true,
                        'explanation' => 'O paciente apresenta quadro clínico clássico de apendicite aguda. A conduta indicada é internação e cirurgia precoce. O uso isolado de antibióticos é controverso e reservado a casos específicos.'
                    ],
                    ['text' => 'Laparotomia exploradora mediana imediata.', 'correct' => false],
                ]
            ],
            [
                'question' => 'Sobre as hérnias inguinais, assinale a alternativa incorreta:',
                'type' => 'single',
                'category' => 0,
                'answers' => [
                    ['text' => 'A hérnia direta surge medial aos vasos epigástricos.', 'correct' => false],
                    ['text' => 'A hérnia indireta percorre o canal inguinal e pode atingir o escroto.', 'correct' => false],
                    [
                        'text' => 'A hérnia femoral ocorre acima do ligamento inguinal.',
                        'correct' => true,
                        'explanation' => 'A hérnia femoral ocorre abaixo do ligamento inguinal, no canal femoral. As demais estão corretas. A abordagem laparoscópica é ideal em recidivas por via anterior, pois evita dissecar cicatriz.'
                    ],
                    ['text' => 'A cirurgia laparoscópica é preferida em recidivas pós-abordagem anterior.', 'correct' => false],
                ]
            ],
            [
                'question' => 'Homem, 35 anos, vítima de acidente com líquido escaldante, apresenta queimadura de espessura parcial em tronco anterior e membros superiores. Peso: 80 kg. Sobre a reposição volêmica nas primeiras 24 horas, marque a correta:',
                'type' => 'single',
                'category' => 4,
                'answers' => [
                    [
                        'text' => 'Deve-se administrar 4.800 ml nas primeiras 24 horas.',
                        'correct' => true,
                        'explanation' => 'Regra de Parkland: 4 ml × 80 kg × 15 %SCQ = 4.800 ml. A primeira metade deve ser dada nas primeiras 8 horas. A fórmula de Evans não é a mais utilizada. A hidratação começa o mais precoce possível, mesmo com acesso periférico.'
                    ],
                    ['text' => 'A reposição deve começar após obtenção de acesso venoso central.', 'correct' => false],
                    ['text' => 'A primeira metade do volume calculado deve ser infundida nas primeiras 6 horas.', 'correct' => false],
                    ['text' => 'Utiliza-se a fórmula de Evans: 2 ml/kg/%SCQ.', 'correct' => false],
                ]
            ],
        ];

        foreach ($paidQuestions as $questionData) {
            $questionId = DB::table('plg_questions')->insertGetId([
                'company_id' => $this->companyId,
                'user_id' => $this->professorIds[array_rand($this->professorIds)],
                'question_type' => $questionData['type'],
                'question' => $questionData['question'],
                'image' => $this->getRandomMedicalImage(),
                'free' => false, // QUESTÃO PAGA
                'imported' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->paidQuestionIds[] = $questionId;

            foreach ($questionData['answers'] as $index => $answerData) {
                DB::table('plg_questions_answers')->insert([
                    'company_id' => $this->companyId,
                    'question_id' => $questionId,
                    'answer_number' => $index + 1,
                    'answer' => $answerData['text'],
                    'correct' => $answerData['correct'],
                    'explanation' => $answerData['explanation'] ?? null,
                    'caption' => $this->getRandomImageCaption(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Associar à categoria
            if (isset($this->categoryIds[$questionData['category']])) {
                DB::table('plg_questions_x_category')->insert([
                    'company_id' => $this->companyId,
                    'question_id' => $questionId,
                    'category_id' => $this->categoryIds[$questionData['category']],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function getRandomMedicalImage(?string $preferredImage = null): ?string
    {
        if ($preferredImage) {
            return $preferredImage;
        }

        $medicalImages = [
            'ultrassom_abdome_01.jpg',
            'tomografia_abdome_02.jpg',
            'raio_x_torax_03.jpg',
            'ressonancia_cerebro_04.jpg',
            'endoscopia_digestiva_05.jpg',
            'ultrassom_vesicular_06.jpg',
            'tomografia_trauma_07.jpg',
            'raio_x_abdome_08.jpg',
            'ultrassom_fast_09.jpg',
            'tomografia_piloro_10.jpg',
            'anatomia_hernia_11.jpg',
            'cirurgia_laparoscopica_12.jpg',
            'queimadura_grau2_13.jpg',
            'fissura_anal_14.jpg',
            'apendicite_tc_15.jpg',
        ];

        return $medicalImages[array_rand($medicalImages)];
    }

    private function getRandomImageCaption(): string
    {
        $captions = [
            'Imagem demonstrativa do caso clínico',
            'Exame de imagem complementar',
            'Achado radiológico característico',
            'Imagem ilustrativa da condição',
            'Exame diagnóstico por imagem',
            'Achado ultrassonográfico',
            'Imagem tomográfica',
            'Aspecto radiológico típico',
            'Exame de imagem de apoio diagnóstico',
            'Ilustração do quadro clínico',
        ];

        return $captions[array_rand($captions)];
    }



    private function createMultipleChoiceQuestions(): void
    {
        $this->command->info('☑️ Criando questões de múltipla escolha...');

        $multipleQuestions = [
            [
                'question' => 'Sobre as complicações pós-operatórias, analise as assertivas e marque todas as corretas:

I. Deiscência de anastomose costuma ocorrer entre o 4.º e o 7.º dia pós-op.
II. Abscesso intra-abdominal pode se manifestar com febre e dor sem peritonite.
III. TVP pós-operatória pode causar taquicardia, dor torácica e dispneia súbita.
IV. Derrame pleural reativo pós-laparotomia deve ser sempre drenado.

Marque todas as alternativas corretas:',
                'category' => 0,
                'answers' => [
                    ['text' => 'I - Deiscência de anastomose costuma ocorrer entre o 4.º e o 7.º dia pós-op.', 'correct' => true, 'explanation' => 'Verdadeiro. A deiscência de anastomose é uma complicação grave que costuma ocorrer tipicamente entre o 4º e o 7º dia pós-operatório.'],
                    ['text' => 'II - Abscesso intra-abdominal pode se manifestar com febre e dor sem peritonite.', 'correct' => true, 'explanation' => 'Verdadeiro. O abscesso intra-abdominal frequentemente se manifesta com febre persistente e dor localizada, muitas vezes sem sinais clássicos de peritonite generalizada.'],
                    ['text' => 'III - TVP pós-operatória pode causar taquicardia, dor torácica e dispneia súbita.', 'correct' => true, 'explanation' => 'Verdadeiro. A TVP pode evoluir para embolia pulmonar, manifestando-se com dispneia súbita, dor torácica pleurítica e taquicardia.'],
                    ['text' => 'IV - Derrame pleural reativo pós-laparotomia deve ser sempre drenado.', 'correct' => false, 'explanation' => 'Falso. O derrame pleural reativo geralmente não requer drenagem, salvo se sintomático ou volumoso.'],
                ],
                'free' => false
            ],
            [
                'question' => 'Em relação às hérnias da região inguinal, marque todas as afirmativas corretas:

I. O canal femoral está localizado medialmente à veia femoral.
II. A hérnia direta atravessa o trígono de Hesselbach.
III. Hérnias inguinais indiretas surgem lateralmente aos vasos epigástricos inferiores.
IV. O reparo de hérnias femorais deve ser preferencialmente por via laparoscópica em mulheres.
V. A hérnia obturatória é facilmente palpável no exame físico.

Marque todas as alternativas corretas:',
                'category' => 0,
                'answers' => [
                    ['text' => 'I - O canal femoral está localizado medialmente à veia femoral.', 'correct' => true, 'explanation' => 'Verdadeiro. O canal femoral está localizado medialmente à veia femoral, dentro da bainha femoral.'],
                    ['text' => 'II - A hérnia direta atravessa o trígono de Hesselbach.', 'correct' => true, 'explanation' => 'Verdadeiro. A hérnia inguinal direta ocorre através do trígono de Hesselbach.'],
                    ['text' => 'III - Hérnias inguinais indiretas surgem lateralmente aos vasos epigástricos inferiores.', 'correct' => true, 'explanation' => 'Verdadeiro. A hérnia inguinal indireta surge lateralmente aos vasos epigástricos inferiores.'],
                    ['text' => 'IV - O reparo de hérnias femorais deve ser preferencialmente por via laparoscópica em mulheres.', 'correct' => true, 'explanation' => 'Verdadeiro. O reparo laparoscópico é preferido em mulheres com suspeita de hérnia femoral.'],
                    ['text' => 'V - A hérnia obturatória é facilmente palpável no exame físico.', 'correct' => false, 'explanation' => 'Falso. A hérnia obturatória é profunda e não palpável ao exame físico.'],
                ],
                'free' => false
            ],
        ];

        foreach ($multipleQuestions as $questionData) {
            $questionId = DB::table('plg_questions')->insertGetId([
                'company_id' => $this->companyId,
                'user_id' => $this->professorIds[array_rand($this->professorIds)],
                'question_type' => 'multiple',
                'question' => $questionData['question'],
                'image' => $this->getRandomMedicalImage(),
                'free' => $questionData['free'],
                'imported' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            if ($questionData['free']) {
                $this->freeQuestionIds[] = $questionId;
            } else {
                $this->paidQuestionIds[] = $questionId;
            }

            foreach ($questionData['answers'] as $index => $answerData) {
                DB::table('plg_questions_answers')->insert([
                    'company_id' => $this->companyId,
                    'question_id' => $questionId,
                    'answer_number' => $index + 1,
                    'answer' => $answerData['text'],
                    'correct' => $answerData['correct'],
                    'explanation' => $answerData['explanation'] ?? null,
                    'caption' => $this->getRandomImageCaption(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Associar à categoria
            if (isset($this->categoryIds[$questionData['category']])) {
                DB::table('plg_questions_x_category')->insert([
                    'company_id' => $this->companyId,
                    'question_id' => $questionId,
                    'category_id' => $this->categoryIds[$questionData['category']],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }



    private function createVariedMedicalTests(): void
    {
        $this->command->info('🧪 Criando testes médicos para todos os módulos...');

        // Criar testes para CADA módulo (mínimo: 1 quiz, 1 desafio, 1 teste)
        $this->createTestsForAllModules();

        // Criar alguns testes extras especiais
        $this->createSpecialTests();
    }

    private function createTestsForAllModules(): void
    {
        $this->command->info('📋 Criando quiz, desafio e teste para cada módulo...');

        // Buscar todos os módulos criados
        $modules = DB::table('plg_modules')->where('company_id', $this->companyId)->get();

        foreach ($modules as $module) {
            $moduleSlug = $module->slug;
            $moduleTitle = $module->title;

            // Determinar tipo de questões baseado no módulo
            $questionType = $this->getQuestionTypeForModule($moduleTitle);

            // 1. QUIZ para cada módulo
            $this->createTestForModule([
                'title' => "Quiz - {$moduleTitle}",
                'slug' => "quiz-{$moduleSlug}",
                'description' => "Quiz rápido sobre {$moduleTitle} com questões práticas",
                'module_id' => $module->id,
                'test_type' => 'quiz',
                'time_limit' => rand(10, 20),
                'max_attempts' => -1, // Ilimitado para quizzes
                'passing_score' => 60.00,
                'question_type' => $questionType,
                'active' => true
            ]);

            // 2. DESAFIO para cada módulo
            $this->createTestForModule([
                'title' => "Desafio - {$moduleTitle}",
                'slug' => "desafio-{$moduleSlug}",
                'description' => "Desafio avançado sobre {$moduleTitle} com casos clínicos",
                'module_id' => $module->id,
                'test_type' => 'challenge',
                'time_limit' => rand(30, 60),
                'max_attempts' => rand(3, 5),
                'passing_score' => 75.00,
                'question_type' => $questionType,
                'active' => true
            ]);

            // 3. TESTE/EXAME para cada módulo
            $this->createTestForModule([
                'title' => "Exame - {$moduleTitle}",
                'slug' => "exame-{$moduleSlug}",
                'description' => "Exame completo sobre {$moduleTitle} baseado no REVALIDA",
                'module_id' => $module->id,
                'test_type' => 'exam',
                'time_limit' => rand(60, 120),
                'max_attempts' => rand(2, 3),
                'passing_score' => 70.00,
                'question_type' => $questionType,
                'active' => true
            ]);
        }
    }

    private function getQuestionTypeForModule(string $moduleTitle): string
    {
        // Módulos gratuitos usam questões gratuitas
        if (str_contains(strtolower($moduleTitle), 'gratuito') ||
            str_contains(strtolower($moduleTitle), 'introdução') ||
            str_contains(strtolower($moduleTitle), 'básico')) {
            return 'free';
        }

        // Módulos premium usam questões pagas
        if (str_contains(strtolower($moduleTitle), 'premium') ||
            str_contains(strtolower($moduleTitle), 'avançado')) {
            return 'paid';
        }

        // Módulos normais usam questões mistas
        return 'mixed';
    }

    private function createTestForModule(array $testData): void
    {
        $existing = DB::table('plg_tests')->where('slug', $testData['slug'])->first();
        if (!$existing) {
            // Selecionar questões baseado no tipo
            $questionsData = $this->selectQuestionsForTest($testData['question_type'], rand(5, 8));

            if (!empty($questionsData)) {
                DB::table('plg_tests')->insert([
                    'company_id' => $this->companyId,
                    'user_id' => $this->professorIds[array_rand($this->professorIds)],
                    'title' => $testData['title'],
                    'slug' => $testData['slug'],
                    'description' => $testData['description'],
                    'module_id' => $testData['module_id'],
                    'test_type' => $testData['test_type'],
                    'questions' => json_encode($questionsData),
                    'time_limit_minutes' => $testData['time_limit'],
                    'max_attempts' => $testData['max_attempts'],
                    'allow_unlimited_attempts' => $testData['max_attempts'] === -1,
                    'cooldown_hours' => 0,
                    'auto_abandon_inactive_minutes' => 30,
                    'show_activity_warning_minutes' => 25,
                    'allow_resume' => true,
                    'randomize_questions' => true,
                    'randomize_alternatives' => true,
                    'passing_score' => $testData['passing_score'],
                    'active' => $testData['active'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function createSpecialTests(): void
    {
        $this->command->info('🌟 Criando testes especiais adicionais...');

        $specialTests = [
            [
                'title' => 'REVALIDA - Simulado Geral Completo',
                'slug' => 'revalida-simulado-geral',
                'description' => 'Simulado completo do REVALIDA com questões de todas as especialidades',
                'module_id' => $this->moduleIds[3], // Módulo principal
                'test_type' => 'exam',
                'time_limit' => 240, // 4 horas
                'max_attempts' => 2,
                'passing_score' => 70.00,
                'question_type' => 'paid',
                'active' => true
            ],
            [
                'title' => 'Mega Desafio - Casos Complexos',
                'slug' => 'mega-desafio-casos-complexos',
                'description' => 'Mega desafio com os casos mais complexos do REVALIDA',
                'module_id' => $this->moduleIds[8], // Módulo premium
                'test_type' => 'challenge',
                'time_limit' => 90,
                'max_attempts' => 3,
                'passing_score' => 85.00,
                'question_type' => 'paid',
                'active' => true
            ],
        ];

        foreach ($specialTests as $testData) {
            $this->createTestForModule($testData);
        }
    }

    private function selectQuestionsForTest(string $type, int $limit): array
    {
        $questionsData = [];

        switch ($type) {
            case 'free':
                $questionIds = array_slice($this->freeQuestionIds, 0, $limit);
                break;
            case 'paid':
                $questionIds = array_slice($this->paidQuestionIds, 0, $limit);
                break;
            case 'mixed':
                $freeIds = array_slice($this->freeQuestionIds, 0, $limit / 2);
                $paidIds = array_slice($this->paidQuestionIds, 0, $limit / 2);
                $questionIds = array_merge($freeIds, $paidIds);
                break;
            default:
                $questionIds = array_merge($this->freeQuestionIds, $this->paidQuestionIds);
                $questionIds = array_slice($questionIds, 0, $limit);
        }

        foreach ($questionIds as $index => $questionId) {
            $questionsData[] = [
                'id' => $questionId,
                'order' => $index
            ];
        }

        return $questionsData;
    }



    private function displaySystemSummary(): void
    {
        $this->command->newLine();
        $this->command->warn('📊 RESUMO DO SISTEMA CRIADO:');

        // Contar dados criados
        $totalQuestions = DB::table('plg_questions')->where('company_id', $this->companyId)->count();
        $freeQuestions = DB::table('plg_questions')->where('company_id', $this->companyId)->where('free', true)->count();
        $paidQuestions = DB::table('plg_questions')->where('company_id', $this->companyId)->where('free', false)->count();
        $totalTests = DB::table('plg_tests')->where('company_id', $this->companyId)->count();
        $totalModules = DB::table('plg_modules')->where('company_id', $this->companyId)->count();
        $freeModules = DB::table('plg_modules')->where('company_id', $this->companyId)->where('is_free', true)->count();
        $paidModules = DB::table('plg_modules')->where('company_id', $this->companyId)->where('is_free', false)->count();
        $totalCourses = DB::table('plg_courses')->where('company_id', $this->companyId)->count();
        $totalCategories = DB::table('plg_categories')->where('company_id', $this->companyId)->count();
        $totalContents = DB::table('plg_modules_contents')->where('company_id', $this->companyId)->count();

        $this->command->line("📚 Cursos: {$totalCourses} (com estratégias de monetização variadas)");
        $this->command->line("📖 Módulos: {$totalModules} total ({$freeModules} gratuitos + {$paidModules} pagos)");
        $this->command->line("   • Estratégias: Freemium, Degustação, Mista, Especialização, Premium");
        $this->command->line("   • Preços: R$ 0,00 (gratuitos) até R$ 149,90 (premium)");
        $this->command->line("📄 Conteúdos: {$totalContents} (vídeos, PDFs, artigos, simuladores)");
        $this->command->line("🏷️ Categorias: {$totalCategories} (especialidades médicas)");
        $this->command->line("❓ Questões: {$totalQuestions} total ({$freeQuestions} gratuitas + {$paidQuestions} pagas)");
        $this->command->line("   • Tipos: Escolha única e Múltipla escolha (avaliação automática)");
        $this->command->line("   • Baseadas no documento REVALIDA com explicações detalhadas");
        $this->command->line("🧪 Testes: {$totalTests} (cada módulo tem: 1 quiz + 1 desafio + 1 exame)");

        $this->command->newLine();
        $this->command->warn('💰 ESTRATÉGIAS DE MONETIZAÇÃO IMPLEMENTADAS:');
        $this->command->line('📈 CURSO 1 - REVALIDA (Freemium):');
        $this->command->line('   • 1º módulo GRATUITO + 4 módulos pagos (R$ 49,90 a R$ 149,90)');
        $this->command->line('🔄 CURSO 2 - CIRURGIA (Mista):');
        $this->command->line('   • 2 módulos gratuitos + 2 pagos + 1 gratuito (retenção)');
        $this->command->line('🎁 CURSO 3 - TRAUMA (Degustação):');
        $this->command->line('   • 3 módulos gratuitos + 2 especializados pagos');
        $this->command->line('🎯 CURSO 4 - PEDIATRIA (Especialização):');
        $this->command->line('   • 1 básico gratuito + 2 especializados pagos');
        $this->command->line('💎 CURSO 5 - EMERGÊNCIA (Premium):');
        $this->command->line('   • 1 gratuito + 3 pagos (até R$ 149,90)');

        $this->command->newLine();
        $this->command->warn('👥 CREDENCIAIS DE ACESSO:');
        $this->command->line('🔑 Senha padrão para todos: 123456');
        $this->command->newLine();
        $this->command->line('👑 SUPER ADMINISTRADORES:');
        $this->command->line('   • <EMAIL> (Principal)');
        $this->command->line('   • <EMAIL> (Secundário)');
        $this->command->newLine();
        $this->command->line('👨‍⚕️ PROFESSORES MÉDICOS:');
        $this->command->line('   • <EMAIL> (Cirurgia Geral e REVALIDA)');
        $this->command->line('   • <EMAIL> (Trauma e Emergência)');
        $this->command->line('   • <EMAIL> (Cirurgia de Emergência)');
        $this->command->line('   • <EMAIL> (Pediatria Cirúrgica)');
        $this->command->newLine();
        $this->command->line('👨‍🎓 ESTUDANTES DE MEDICINA:');
        $this->command->line('   • <EMAIL> (6º ano)');
        $this->command->line('   • <EMAIL> (5º ano)');
        $this->command->line('   • <EMAIL> (6º ano)');
        $this->command->line('   • <EMAIL> (Residente R1)');
        $this->command->line('   • <EMAIL> (4º ano)');
        $this->command->line('   • <EMAIL> (6º ano)');
        $this->command->line('   • <EMAIL> (Residente R2)');
        $this->command->line('   • <EMAIL> (5º ano)');
        $this->command->line('   • <EMAIL> (6º ano)');
        $this->command->line('   • <EMAIL> (Formado)');

        $this->command->newLine();
        $this->command->info('💡 CARACTERÍSTICAS ESPECIAIS:');
        $this->command->line('✅ Sistema multi-tenant com company_id');
        $this->command->line('✅ Questões gratuitas apenas em módulos gratuitos');
        $this->command->line('✅ Questões pagas apenas em módulos pagos e exames');
        $this->command->line('✅ Questões objetivas (single e multiple choice) para avaliação automática');
        $this->command->line('✅ Explicações detalhadas baseadas no REVALIDA');
        $this->command->line('✅ Imagens médicas simuladas');
        $this->command->line('✅ Testes com configurações variadas');
        $this->command->line('✅ Módulos com preços variados (R$ 0,00 a R$ 179,90)');
        $this->command->line('✅ Cursos gratuitos e premium');

        $this->command->newLine();
        $this->command->warn('🚀 SISTEMA PRONTO PARA TESTES!');
    }

    private function createModuleContents(): void
    {
        $this->command->info('📚 Criando conteúdos educativos para os módulos...');

        // Buscar todos os módulos criados
        $modules = DB::table('plg_modules')->where('company_id', $this->companyId)->get();

        foreach ($modules as $module) {
            $this->createContentsForModule($module);
        }
    }

    private function createContentsForModule($module): void
    {
        $contents = $this->getContentsForModuleByTitle($module->title);

        foreach ($contents as $index => $content) {
            DB::table('plg_modules_contents')->insert([
                'company_id' => $this->companyId,
                'user_id' => $this->professorIds[array_rand($this->professorIds)],
                'module_id' => $module->id,
                'order' => $index + 1,
                'active' => true,
                'title' => $content['title'],
                'slug' => $this->generateSlug($content['title']),
                'content' => $content['content'],
                'description' => $content['description'],
                'content_type' => $content['type'],
                'duration' => $content['duration'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    private function generateSlug(string $title): string
    {
        return strtolower(str_replace([' ', '-', '(', ')', ':', '.'], ['_', '_', '', '', '', ''],
            iconv('UTF-8', 'ASCII//TRANSLIT', $title)));
    }

    private function getContentsForModuleByTitle(string $moduleTitle): array
    {
        // Conteúdos específicos baseados no título do módulo
        switch (true) {
            case str_contains($moduleTitle, 'Introdução ao Trauma'):
                return $this->getTraumaIntroContents();

            case str_contains($moduleTitle, 'FAST Básico'):
                return $this->getFastBasicContents();

            case str_contains($moduleTitle, 'Primeiros Socorros'):
                return $this->getPrimeirosSocorrosContents();

            case str_contains($moduleTitle, 'Colecistite'):
                return $this->getColecistiteContents();

            case str_contains($moduleTitle, 'Apendicite'):
                return $this->getApendiciteContents();

            case str_contains($moduleTitle, 'Hérnias'):
                return $this->getHerniasContents();

            case str_contains($moduleTitle, 'Fissura Anal'):
                return $this->getFissuraAnalContents();

            case str_contains($moduleTitle, 'Trauma Cervical'):
                return $this->getTraumaCervicalContents();

            case str_contains($moduleTitle, 'Laparoscópica'):
                return $this->getLaparoscopiaContents();

            case str_contains($moduleTitle, 'Queimaduras'):
                return $this->getQueimadurasContents();

            case str_contains($moduleTitle, 'Piloro'):
                return $this->getPiloroContents();

            case str_contains($moduleTitle, 'Complicações'):
                return $this->getComplicacoesContents();

            case str_contains($moduleTitle, 'Isquemia'):
                return $this->getIsquemiaContents();

            case str_contains($moduleTitle, 'Trauma Penetrante'):
                return $this->getTraumaPenetranteContents();

            case str_contains($moduleTitle, 'Cicatrização'):
                return $this->getCicatrizacaoContents();

            default:
                return $this->getGenericMedicalContents();
        }
    }

    private function getTraumaIntroContents(): array
    {
        return [
            [
                'title' => 'Introdução ao Atendimento de Trauma - Conceitos Básicos',
                'description' => 'Vídeo introdutório sobre os princípios fundamentais do atendimento ao paciente traumatizado',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '25 minutos'
            ],
            [
                'title' => 'Manual ATLS - Advanced Trauma Life Support',
                'description' => 'Manual oficial do ATLS com protocolos de atendimento ao trauma',
                'content' => 'https://www.facs.org/media/kzjnqycd/atls_manual.pdf',
                'type' => 'pdf',
                'duration' => '120 minutos'
            ],
            [
                'title' => 'Classificação de Trauma - Escalas e Índices',
                'description' => 'Apresentação sobre as principais escalas utilizadas na classificação de trauma',
                'content' => 'https://docs.google.com/presentation/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit',
                'type' => 'presentation',
                'duration' => '45 minutos'
            ],
            [
                'title' => 'Simulador de Casos Clínicos - Trauma',
                'description' => 'Ferramenta interativa para praticar casos de trauma',
                'content' => 'https://www.medicalsimulation.com/trauma-cases',
                'type' => 'simulator',
                'duration' => '60 minutos'
            ]
        ];
    }

    private function getFastBasicContents(): array
    {
        return [
            [
                'title' => 'FAST - Focused Assessment with Sonography for Trauma',
                'description' => 'Vídeo demonstrativo da técnica FAST em pacientes traumatizados',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '30 minutos'
            ],
            [
                'title' => 'Protocolo FAST - Diretrizes Atualizadas',
                'description' => 'Documento com as diretrizes mais recentes para realização do FAST',
                'content' => 'https://www.acep.org/globalassets/new-pdfs/fast-protocol-2024.pdf',
                'type' => 'pdf',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Anatomia Ultrassonográfica para FAST',
                'description' => 'Atlas de imagens ultrassonográficas para interpretação do FAST',
                'content' => 'https://radiopaedia.org/articles/fast-scan',
                'type' => 'article',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Casos Práticos - Interpretação de FAST',
                'description' => 'Casos clínicos com imagens de FAST para interpretação',
                'content' => 'https://www.ultrasoundcases.info/fast-cases/',
                'type' => 'simulator',
                'duration' => '50 minutos'
            ]
        ];
    }

    private function getPrimeirosSocorrosContents(): array
    {
        return [
            [
                'title' => 'Primeiros Socorros - Suporte Básico de Vida',
                'description' => 'Curso básico de primeiros socorros e reanimação cardiopulmonar',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '45 minutos'
            ],
            [
                'title' => 'Manual de Primeiros Socorros - Cruz Vermelha',
                'description' => 'Manual oficial da Cruz Vermelha sobre primeiros socorros',
                'content' => 'https://www.redcross.org/content/dam/redcross/atg/PDF_s/Health___Safety_Services/Training/pm321419.pdf',
                'type' => 'pdf',
                'duration' => '90 minutos'
            ],
            [
                'title' => 'Algoritmos de Emergência',
                'description' => 'Fluxogramas e algoritmos para situações de emergência',
                'content' => 'https://www.heart.org/en/professional/quality-improvement/mission-lifeline/mission-lifeline-resources',
                'type' => 'article',
                'duration' => '30 minutos'
            ]
        ];
    }

    private function getColecistiteContents(): array
    {
        return [
            [
                'title' => 'Colecistite Aguda - Diagnóstico e Tratamento',
                'description' => 'Aula completa sobre diagnóstico e manejo da colecistite aguda',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '55 minutos'
            ],
            [
                'title' => 'Diretrizes Tokyo 2018 - Colecistite Aguda',
                'description' => 'Diretrizes internacionais para diagnóstico e tratamento da colecistite',
                'content' => 'https://link.springer.com/content/pdf/10.1007/s00534-017-1492-x.pdf',
                'type' => 'pdf',
                'duration' => '75 minutos'
            ],
            [
                'title' => 'Ultrassonografia na Colecistite',
                'description' => 'Atlas de imagens ultrassonográficas da vesícula biliar',
                'content' => 'https://radiopaedia.org/articles/acute-cholecystitis',
                'type' => 'article',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Técnica Cirúrgica - Colecistectomia Laparoscópica',
                'description' => 'Vídeo cirúrgico demonstrando a técnica de colecistectomia laparoscópica',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Casos Clínicos - Colecistite Complicada',
                'description' => 'Casos clínicos de colecistite com complicações',
                'content' => 'https://www.nejm.org/doi/full/10.1056/NEJMcp1411126',
                'type' => 'article',
                'duration' => '50 minutos'
            ]
        ];
    }

    private function getApendiciteContents(): array
    {
        return [
            [
                'title' => 'Apendicite Aguda - Fisiopatologia e Diagnóstico',
                'description' => 'Aula sobre os mecanismos fisiopatológicos e diagnóstico da apendicite',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Escala de Alvarado - Ferramenta Diagnóstica',
                'description' => 'Artigo sobre a aplicação da escala de Alvarado no diagnóstico de apendicite',
                'content' => 'https://www.ncbi.nlm.nih.gov/pmc/articles/PMC3609170/pdf/WJGS-5-49.pdf',
                'type' => 'pdf',
                'duration' => '25 minutos'
            ],
            [
                'title' => 'Tomografia na Apendicite - Interpretação',
                'description' => 'Atlas de imagens tomográficas na apendicite aguda',
                'content' => 'https://radiopaedia.org/articles/acute-appendicitis',
                'type' => 'article',
                'duration' => '45 minutos'
            ],
            [
                'title' => 'Apendicectomia Laparoscópica - Técnica Cirúrgica',
                'description' => 'Demonstração da técnica de apendicectomia por via laparoscópica',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '30 minutos'
            ]
        ];
    }

    private function getHerniasContents(): array
    {
        return [
            [
                'title' => 'Hérnias Inguinais - Anatomia e Classificação',
                'description' => 'Aula sobre anatomia da região inguinal e classificação das hérnias',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '50 minutos'
            ],
            [
                'title' => 'Técnicas de Reparo - Lichtenstein vs TEP',
                'description' => 'Comparação entre técnicas de reparo de hérnias inguinais',
                'content' => 'https://www.herniasurge.com/guidelines/HerniaSurge_Guidelines_2018.pdf',
                'type' => 'pdf',
                'duration' => '60 minutos'
            ],
            [
                'title' => 'Atlas Cirúrgico - Herniorrafia',
                'description' => 'Atlas com técnicas cirúrgicas para reparo de hérnias',
                'content' => 'https://www.surgicalatlas.org/hernia-repair',
                'type' => 'article',
                'duration' => '45 minutos'
            ]
        ];
    }

    private function getFissuraAnalContents(): array
    {
        return [
            [
                'title' => 'Fissura Anal - Diagnóstico e Tratamento Conservador',
                'description' => 'Abordagem clínica da fissura anal crônica',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Esfincterotomia Lateral - Técnica Cirúrgica',
                'description' => 'Demonstração da técnica de esfincterotomia lateral interna',
                'content' => 'https://www.fascrs.org/sites/default/files/downloads/publication/anal_fissure.pdf',
                'type' => 'pdf',
                'duration' => '30 minutos'
            ],
            [
                'title' => 'Proctologia - Exame Físico',
                'description' => 'Técnicas de exame físico em proctologia',
                'content' => 'https://www.uptodate.com/contents/anal-fissure-clinical-manifestations-diagnosis-and-management',
                'type' => 'article',
                'duration' => '25 minutos'
            ]
        ];
    }

    private function getTraumaCervicalContents(): array
    {
        return [
            [
                'title' => 'Trauma Cervical - Avaliação Inicial',
                'description' => 'Protocolo de avaliação do trauma cervical',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Critérios NEXUS - Trauma Cervical',
                'description' => 'Aplicação dos critérios NEXUS na avaliação cervical',
                'content' => 'https://www.nejm.org/doi/pdf/10.1056/NEJM200007133430201',
                'type' => 'pdf',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Imobilização Cervical - Técnicas',
                'description' => 'Técnicas de imobilização da coluna cervical',
                'content' => 'https://www.naemse.org/cervical-spine-immobilization',
                'type' => 'article',
                'duration' => '30 minutos'
            ]
        ];
    }

    private function getLaparoscopiaContents(): array
    {
        return [
            [
                'title' => 'Cirurgia Laparoscópica - Princípios Básicos',
                'description' => 'Fundamentos da cirurgia minimamente invasiva',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '60 minutos'
            ],
            [
                'title' => 'Instrumentação Laparoscópica',
                'description' => 'Manual de instrumentos e equipamentos laparoscópicos',
                'content' => 'https://www.sages.org/publications/guidelines/guidelines-for-laparoscopic-surgery/',
                'type' => 'pdf',
                'duration' => '45 minutos'
            ],
            [
                'title' => 'Complicações em Laparoscopia',
                'description' => 'Prevenção e manejo de complicações laparoscópicas',
                'content' => 'https://www.uptodate.com/contents/complications-of-laparoscopic-surgery',
                'type' => 'article',
                'duration' => '50 minutos'
            ],
            [
                'title' => 'Simulador de Laparoscopia',
                'description' => 'Treinamento virtual em técnicas laparoscópicas',
                'content' => 'https://www.laparoscopicsimulator.com/training',
                'type' => 'simulator',
                'duration' => '90 minutos'
            ]
        ];
    }

    private function getQueimadurasContents(): array
    {
        return [
            [
                'title' => 'Queimaduras - Classificação e Avaliação Inicial',
                'description' => 'Classificação de queimaduras e cálculo da superfície corporal queimada',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '45 minutos'
            ],
            [
                'title' => 'Fórmula de Parkland - Reposição Volêmica',
                'description' => 'Protocolo de reposição volêmica em grandes queimados',
                'content' => 'https://www.ameriburn.org/wp-content/uploads/2017/04/burncarecriteria.pdf',
                'type' => 'pdf',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Tratamento de Queimaduras - Diretrizes',
                'description' => 'Diretrizes para tratamento de queimaduras',
                'content' => 'https://www.uptodate.com/contents/initial-management-of-severe-burns',
                'type' => 'article',
                'duration' => '55 minutos'
            ],
            [
                'title' => 'Calculadora de Queimaduras',
                'description' => 'Ferramenta para cálculo de superfície corporal queimada',
                'content' => 'https://www.burncalculator.com/',
                'type' => 'simulator',
                'duration' => '20 minutos'
            ]
        ];
    }

    private function getPiloroContents(): array
    {
        return [
            [
                'title' => 'Estenose Hipertrófica do Piloro - Diagnóstico',
                'description' => 'Diagnóstico clínico e por imagem da estenose pilórica',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Piloromiotomia de Ramstedt - Técnica Cirúrgica',
                'description' => 'Demonstração da técnica de piloromiotomia extramucosa',
                'content' => 'https://www.pediatricsurgery.org/pyloric-stenosis-guidelines.pdf',
                'type' => 'pdf',
                'duration' => '30 minutos'
            ],
            [
                'title' => 'Ultrassonografia do Piloro',
                'description' => 'Técnica e interpretação da ultrassonografia pilórica',
                'content' => 'https://radiopaedia.org/articles/hypertrophic-pyloric-stenosis',
                'type' => 'article',
                'duration' => '25 minutos'
            ]
        ];
    }

    private function getComplicacoesContents(): array
    {
        return [
            [
                'title' => 'Complicações Pós-Operatórias - Reconhecimento Precoce',
                'description' => 'Identificação e manejo de complicações cirúrgicas',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '50 minutos'
            ],
            [
                'title' => 'Infecção de Sítio Cirúrgico - Prevenção',
                'description' => 'Protocolo de prevenção de infecção de sítio cirúrgico',
                'content' => 'https://www.cdc.gov/infectioncontrol/guidelines/ssi/index.html',
                'type' => 'pdf',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Deiscência de Anastomose - Manejo',
                'description' => 'Diagnóstico e tratamento da deiscência anastomótica',
                'content' => 'https://www.uptodate.com/contents/anastomotic-leak-after-gastrointestinal-surgery',
                'type' => 'article',
                'duration' => '45 minutos'
            ]
        ];
    }

    private function getIsquemiaContents(): array
    {
        return [
            [
                'title' => 'Isquemia Mesentérica Aguda - Diagnóstico',
                'description' => 'Diagnóstico precoce da isquemia mesentérica aguda',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Angiotomografia Mesentérica',
                'description' => 'Interpretação da angiotomografia na isquemia mesentérica',
                'content' => 'https://radiopaedia.org/articles/acute-mesenteric-ischaemia',
                'type' => 'article',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Revascularização Mesentérica',
                'description' => 'Técnicas de revascularização em isquemia mesentérica',
                'content' => 'https://www.jvascsurg.org/article/S0741-5214(17)30123-4/pdf',
                'type' => 'pdf',
                'duration' => '50 minutos'
            ]
        ];
    }

    private function getTraumaPenetranteContents(): array
    {
        return [
            [
                'title' => 'Trauma Penetrante Abdominal - Avaliação',
                'description' => 'Protocolo de avaliação do trauma penetrante abdominal',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '45 minutos'
            ],
            [
                'title' => 'Laparoscopia Diagnóstica no Trauma',
                'description' => 'Uso da laparoscopia na avaliação do trauma abdominal',
                'content' => 'https://www.trauma.org/archive/penetrating/PENlaparoscopy.pdf',
                'type' => 'pdf',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Controle de Danos - Cirurgia',
                'description' => 'Princípios da cirurgia de controle de danos',
                'content' => 'https://www.uptodate.com/contents/damage-control-surgery',
                'type' => 'article',
                'duration' => '40 minutos'
            ]
        ];
    }

    private function getCicatrizacaoContents(): array
    {
        return [
            [
                'title' => 'Cicatrização de Feridas - Fisiologia',
                'description' => 'Processo fisiológico da cicatrização de feridas',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '40 minutos'
            ],
            [
                'title' => 'Curativos e Coberturas',
                'description' => 'Tipos de curativos e suas indicações',
                'content' => 'https://www.woundcare.org/wound-dressing-guidelines.pdf',
                'type' => 'pdf',
                'duration' => '35 minutos'
            ],
            [
                'title' => 'Fatores que Interferem na Cicatrização',
                'description' => 'Fatores locais e sistêmicos que afetam a cicatrização',
                'content' => 'https://www.uptodate.com/contents/wound-healing-and-repair',
                'type' => 'article',
                'duration' => '30 minutos'
            ]
        ];
    }

    private function getGenericMedicalContents(): array
    {
        return [
            [
                'title' => 'Conteúdo Médico Geral',
                'description' => 'Material educativo geral sobre medicina',
                'content' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
                'type' => 'video',
                'duration' => '30 minutos'
            ],
            [
                'title' => 'Artigo Científico',
                'description' => 'Artigo científico relevante ao tema',
                'content' => 'https://www.ncbi.nlm.nih.gov/pmc/articles/PMC123456/',
                'type' => 'article',
                'duration' => '25 minutos'
            ],
            [
                'title' => 'Manual de Referência',
                'description' => 'Manual de referência médica',
                'content' => 'https://www.medical-reference.com/manual.pdf',
                'type' => 'pdf',
                'duration' => '45 minutos'
            ]
        ];
    }
}
