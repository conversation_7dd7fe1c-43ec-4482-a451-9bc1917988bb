<x-layouts.student title="Dashboard de Aluno" class="dashboard-page dark">
    <!-- Hero Section -->
    <x-hero title="COPYEPT" subtitle="Maestria em Copy para Carrosséis no Instagram" :progress="4" :currentModule="[
        'thumbnail' => 'https://cinepop.com.br/wp-content/uploads/2025/05/casa4.jpg',
        'title' => 'Visão Geral do Curso',
        'duration' => '15 min',
    ]" />

    <!-- Seção de Carrosséis -->
    <div class="container mx-auto relative">
        <!-- Carrossel 1: Cursos Disponíveis -->
        <x-course-carousel title="Cursos Disponíveis" :courses="$courses" />

        @php
        // Organizar módulos por status estratégico
        $allModules = collect();
        $inProgressModules = collect();
        $enrolledPaidModules = collect();
        $freeModules = collect();
        $newReleaseModules = collect();
        $featuredModules = collect();
        $expiredModules = collect();

        foreach($courses as $course) {
            foreach($course->modules as $module) {
                // Adicionar referência ao curso no módulo
                $module->course_ref = $course;
                $allModules->push($module);

                // 1. Módulos em progresso (TODO: implementar lógica de progresso real)
                $hasProgress = false; // Placeholder - implementar com sistema de progresso
                if($hasProgress && $module->access_status['has_access']) {
                    $inProgressModules->push($module);
                }

                // 2. Módulos pagos com acesso ativo
                if(!$module->is_free && $module->access_status['has_access']) {
                    $enrolledPaidModules->push($module);
                }

                // 3. Módulos gratuitos
                if($module->is_free) {
                    $freeModules->push($module);
                }

                // 4. Novos lançamentos (criados nos últimos 30 dias)
                if($module->created_at && $module->created_at->isAfter(now()->subDays(30))) {
                    $newReleaseModules->push($module);
                }

                // 5. Módulos em destaque
                if($module->is_featured ?? false) {
                    $featuredModules->push($module);
                }

                // 6. Módulos expirados
                if(($module->access_status['status'] ?? '') === 'expired') {
                    $expiredModules->push($module);
                }
            }
        }

        // Remover duplicatas (um módulo pode estar em várias categorias)
        $freeModules = $freeModules->reject(function($module) use ($inProgressModules, $enrolledPaidModules) {
            return $inProgressModules->contains('id', $module->id) ||
                   $enrolledPaidModules->contains('id', $module->id);
        });

        $newReleaseModules = $newReleaseModules->reject(function($module) use ($inProgressModules, $enrolledPaidModules, $freeModules) {
            return $inProgressModules->contains('id', $module->id) ||
                   $enrolledPaidModules->contains('id', $module->id) ||
                   $freeModules->contains('id', $module->id);
        });

        $featuredModules = $featuredModules->reject(function($module) use ($inProgressModules, $enrolledPaidModules, $freeModules, $newReleaseModules) {
            return $inProgressModules->contains('id', $module->id) ||
                   $enrolledPaidModules->contains('id', $module->id) ||
                   $freeModules->contains('id', $module->id) ||
                   $newReleaseModules->contains('id', $module->id);
        });
        @endphp

        <!-- Continue Estudando -->
        @if($inProgressModules->count() > 0)
            <x-student.modules-carousel
                title="Continue Estudando"
                :modules="$inProgressModules"
                :course="null" />
        @endif

        <!-- Seus Módulos -->
        @if($enrolledPaidModules->count() > 0)
            <x-student.modules-carousel
                title="Seus Módulos"
                :modules="$enrolledPaidModules"
                :course="null" />
        @endif

        <!-- Módulos Gratuitos -->
        @if($freeModules->count() > 0)
            <x-student.modules-carousel
                title="Módulos Gratuitos"
                :modules="$freeModules"
                :course="null" />
        @endif

        <!-- Novos Lançamentos -->
        @if($newReleaseModules->count() > 0)
            <x-student.modules-carousel
                title="Novos Lançamentos"
                :modules="$newReleaseModules"
                :course="null" />
        @endif

        <!-- Destaques -->
        @if($featuredModules->count() > 0)
            <x-student.modules-carousel
                title="Destaques"
                :modules="$featuredModules"
                :course="null" />
        @endif

        <!-- Renovar Acesso -->
        @if($expiredModules->count() > 0)
            <x-student.modules-carousel
                title="Renovar Acesso"
                :modules="$expiredModules"
                :course="null" />
        @endif
    </div>


</x-layouts.student>
