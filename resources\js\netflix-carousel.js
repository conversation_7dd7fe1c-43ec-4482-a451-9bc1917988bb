// Função para atualizar visibilidade dos botões
Swiper.prototype.updateNavigationButtons = function() {
    const nextButton = this.navigation.nextEl;
    const prevButton = this.navigation.prevEl;

    if (nextButton && prevButton) {
        // Remover classe hidden e adicionar flex para funcionamento
        nextButton.classList.remove('hidden');
        prevButton.classList.remove('hidden');
        nextButton.classList.add('flex');
        prevButton.classList.add('flex');

        // Esconder botões se não há slides suficientes para navegar
        if (this.slides.length <= this.params.slidesPerView) {
            nextButton.style.visibility = 'hidden';
            prevButton.style.visibility = 'hidden';
        } else {
            nextButton.style.visibility = 'visible';
            prevButton.style.visibility = 'visible';
        }

        // Garantir que ambos os botões tenham o mesmo comportamento
        nextButton.style.pointerEvents = 'auto';
        prevButton.style.pointerEvents = 'auto';
    }
};

function initializeCarousels() {
    // Verificar se Swiper está disponível
    if (typeof Swiper === 'undefined') {
        console.warn('Swiper não está disponível. Carrosséis não podem ser inicializados.');
        return;
    }

    // Inicializar Swiper carrosséis
    const swiperElements = document.querySelectorAll('.swiper-carousel');

    if (swiperElements.length > 0) {
        swiperElements.forEach((element, index) => {
            // Adicionar IDs únicos para os botões de navegação
            const nextButton = element.querySelector('.swiper-button-next');
            const prevButton = element.querySelector('.swiper-button-prev');

            if (nextButton) nextButton.id = `swiper-next-${index}`;
            if (prevButton) prevButton.id = `swiper-prev-${index}`;

            new Swiper(element, {
                slidesPerView: 'auto',
                spaceBetween: 10,
                speed: 600,
                grabCursor: true,
                watchSlidesProgress: true,
                freeMode: false,
                breakpoints: {
                    320: {
                        slidesPerView: 1
                    },
                    480: {
                        slidesPerView: 1
                    },
                    640: {
                        slidesPerView: 2
                    },
                    768: {
                        slidesPerView: 3
                    },
                    1024: {
                        slidesPerView: 4
                    }
                },
                navigation: {
                    nextEl: `#swiper-next-${index}`,
                    prevEl: `#swiper-prev-${index}`,
                    hideOnClick: false
                },
                on: {
                    init: function() {
                        // Mostrar/esconder botões baseado no conteúdo
                        this.updateNavigationButtons();
                    },
                    slideChange: function() {
                        this.updateNavigationButtons();
                    }
                }
            });
        });
    }

    // Função para atualizar visibilidade dos botões
    Swiper.prototype.updateNavigationButtons = function() {
        const nextButton = this.navigation.nextEl;
        const prevButton = this.navigation.prevEl;

        if (nextButton && prevButton) {
            // Esconder botões se não há slides suficientes
            if (this.slides.length <= this.params.slidesPerView) {
                nextButton.style.display = 'none';
                prevButton.style.display = 'none';
            } else {
                nextButton.style.display = 'flex';
                prevButton.style.display = 'flex';
            }
        }
    };
}

// Inicializar carrosséis quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se Swiper está disponível antes de inicializar
    if (typeof Swiper !== 'undefined') {
        initializeCarousels();
    } else {
        console.warn('Swiper não está disponível. Carrosséis não serão inicializados.');
    }
});