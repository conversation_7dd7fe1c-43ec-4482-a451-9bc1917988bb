<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlgStudent;
use App\Models\PlgStudentAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class PlgStudentAddressesController extends Controller
{
    public function index()
    {
        return view('panel.admin.students-addresses.index', [
            'columns' => $this->getColumns(),
            'id' => 'students-addresses-table',
            'ajaxUrl' => route('admin.students-addresses.data'),
            'filters' => [],
            'clearFiltersBtn' => true
        ]);
    }

    public function getData()
    {
        $query = PlgStudentAddress::with('student')
            ->where('company_id', Auth::user()->company_id)
            ->orderByDesc('id');

        return DataTables::of($query)
            ->addColumn('student_name', fn($address) => $address->student->name ?? '-')
            ->addColumn('actions', fn($address) => view('panel.admin.includes.table-actions', [
                'actions' => [
                    ['type' => 'edit', 'route' => route('admin.students-addresses.edit', $address->id), 'title' => 'Editar endereço'],
                    ['type' => 'delete', 'route' => route('admin.students-addresses.destroy', $address->id), 'title' => 'Excluir endereço', 'confirm_message' => 'Deseja excluir este endereço?']
                ]
            ])->render())
            ->rawColumns(['actions'])
            ->make(true);
    }

    public function create()
    {
        $students = PlgStudent::where('company_id', Auth::user()->company_id)->pluck('name', 'id');
        return view('panel.admin.students-addresses.create', compact('students'));
    }

    public function store(Request $request)
    {
        $validated = $this->validateRequest($request);
        $validated['company_id'] = Auth::user()->company_id;

        PlgStudentAddress::create($validated);

        return redirect()->route('admin.students-addresses.index')->with('success', 'Endereço criado com sucesso!');
    }

    public function edit($id)
    {
        $address = PlgStudentAddress::findOrFail($id);
        $students = PlgStudent::where('company_id', Auth::user()->company_id)->pluck('name', 'id');
        return view('panel.admin.students-addresses.create', compact('address', 'students'));
    }

    public function update(Request $request, $id)
    {
        $address = PlgStudentAddress::findOrFail($id);
        $validated = $this->validateRequest($request);

        $address->update($validated);

        return redirect()->route('admin.students-addresses.index')->with('success', 'Endereço atualizado com sucesso!');
    }

    public function destroy($id)
    {
        $address = PlgStudentAddress::findOrFail($id);
        $address->delete();

        return redirect()->route('admin.students-addresses.index')->with('success', 'Endereço deletado com sucesso!');
    }

    private function getColumns()
    {
        return [
            ['data' => 'id', 'name' => 'id', 'label' => 'ID'],
            ['data' => 'student_name', 'name' => 'student.name', 'label' => 'Estudante'],
            ['data' => 'cep', 'name' => 'cep', 'label' => 'CEP'],
            ['data' => 'street', 'name' => 'street', 'label' => 'Rua'],
            ['data' => 'city', 'name' => 'city', 'label' => 'Cidade'],
            ['data' => 'state', 'name' => 'state', 'label' => 'Estado'],
            ['data' => 'actions', 'name' => 'actions', 'label' => 'Ações', 'orderable' => false, 'searchable' => false],
        ];
    }

    private function validateRequest(Request $request)
    {
        return $request->validate([
            'student_id' => 'required|exists:plg_students,id',
            'country' => 'nullable|string|max:50',
            'cep' => 'nullable|string|max:10',
            'street' => 'nullable|string|max:100',
            'number' => 'nullable|string|max:20',
            'neighborhood' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:50',
        ]);
    }
}
