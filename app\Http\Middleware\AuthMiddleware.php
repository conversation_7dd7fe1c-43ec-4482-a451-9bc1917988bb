<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class AuthMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $guard = null, ...$roles): Response
    {
        // Determinar o guard a ser usado
        $guard = $guard ?: config('auth.defaults.guard', 'web');

        // Verificar autenticação
        if (!Auth::guard($guard)->check()) {
            Log::info("Acesso não autenticado", [
                'guard' => $guard,
                'url' => $request->url()
            ]);

            return $this->redirectToLogin($request, $guard);
        }

        // Se não há papéis especificados, permitir acesso
        if (empty($roles)) {
            return $next($request);
        }

        // Verificar papéis
        $user = Auth::guard($guard)->user();
        $userRole = $guard === 'students' ? 'student' : $user->role;

        // Verificar se o usuário tem o papel necessário
        if (!$this->hasRequiredRole($guard, $user, $roles)) {
            Log::warning("Acesso não autorizado", [
                'guard' => $guard,
                'user_id' => $user->id,
                'user_role' => $userRole,
                'url' => $request->url(),
                'required_roles' => $roles
            ]);

            return $this->redirectToDashboard($guard);
        }

        return $next($request);
    }

    /**
     * Verificar se o usuário tem o papel necessário
     */
    private function hasRequiredRole(string $guard, $user, array $roles): bool
    {
        if ($guard === 'students') {
            return in_array('student', $roles);
        }

        return isset($user->role) && in_array($user->role, $roles);
    }

    /**
     * Redirecionar para a página de login apropriada
     */
    private function redirectToLogin(Request $request, string $guard): Response
    {
        $path = $request->path();
        
        if (str_contains($path, 'panel/student') || $guard === 'students') {
            return redirect()->route('student.login')
                ->with('error', 'Você precisa estar logado para acessar esta página.');
        }
        
        if (str_contains($path, 'panel/admin') || str_contains($path, 'panel')) {
            return redirect()->route('admin.login')
                ->with('error', 'Você precisa estar logado para acessar esta página.');
        }

        return redirect()->route('admin.login')
            ->with('error', 'Você precisa estar logado para acessar esta página.');
    }

    /**
     * Redirecionar para o dashboard apropriado
     */
    private function redirectToDashboard(string $guard): Response
    {
        $route = $guard === 'students' ? 'student.dashboard' : 'admin.dashboard';
        
        return redirect()->route($route)
            ->with('error', 'Você não tem permissão para acessar esta página.');
    }
}