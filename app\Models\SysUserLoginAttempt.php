<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SysUserLoginAttempt extends Model
{
    use HasFactory;

    protected $table = 'sys_user_login_attempts';

    protected $fillable = [
        'company_id',
        'user_id',
        'current_session_id',
        'attempt_ip',
        'attempt_user_agent',
        'attempt_device_info',
        'attempt_location',
        'attempt_at',
        'status',
        'resolved_at',
        'resolution_note',
    ];

    protected $casts = [
        'attempt_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    /**
     * Relacionamento com a empresa
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(SysCompany::class, 'company_id');
    }

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(SysUser::class, 'user_id');
    }

    /**
     * Verificar se a tentativa está pendente
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Verificar se a tentativa foi aprovada
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Verificar se a tentativa foi negada
     */
    public function isDenied(): bool
    {
        return $this->status === 'denied';
    }

    /**
     * Verificar se a tentativa expirou
     */
    public function isTimeout(): bool
    {
        return $this->status === 'timeout';
    }

    /**
     * Aprovar a tentativa
     */
    public function approve(?string $note = null): bool
    {
        return $this->update([
            'status' => 'approved',
            'resolved_at' => now(),
            'resolution_note' => $note,
        ]);
    }

    /**
     * Negar a tentativa
     */
    public function deny(?string $note = null): bool
    {
        return $this->update([
            'status' => 'denied',
            'resolved_at' => now(),
            'resolution_note' => $note,
        ]);
    }

    /**
     * Marcar como timeout
     */
    public function timeout(?string $note = null): bool
    {
        return $this->update([
            'status' => 'timeout',
            'resolved_at' => now(),
            'resolution_note' => $note ?? 'Timeout - usuário não respondeu',
        ]);
    }

    /**
     * Scope para tentativas pendentes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope para tentativas por empresa
     */
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }
}
