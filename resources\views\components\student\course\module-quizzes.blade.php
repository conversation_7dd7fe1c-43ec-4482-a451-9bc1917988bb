@props(['quizzes', 'course', 'module'])

@if($quizzes->count() > 0)
    <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6 border border-red-200 dark:border-red-800">
        <div class="flex items-center space-x-2 mb-4">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white text-lg">Quizzes do Módulo</h4>
            <x-ui.badge variant="default" size="xs">{{ $quizzes->count() }} quiz{{ $quizzes->count() > 1 ? 'zes' : '' }}</x-ui.badge>
        </div>

        <div class="space-y-4">
            @foreach ($quizzes as $quiz)
                @php
                // Verificar se o aluno já fez o quiz
                $lastQuizAttempt = auth('students')->user()->attempts()
                    ->where('test_id', $quiz->id)
                    ->where('status', 'completed')
                    ->latest('finished_at')
                    ->first();
                @endphp

                <div class="border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h5 class="font-semibold text-gray-900 dark:text-white">Quiz - {{ $quiz->name }}</h5>
                                @if($lastQuizAttempt)
                                    <x-ui.badge variant="completed" size="xs">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Concluído
                                    </x-ui.badge>
                                @endif
                            </div>
                            
                            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ is_array($quiz->questions) ? count($quiz->questions) : 0 }} questões
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ $quiz->time_limit_minutes ? $quiz->time_limit_minutes . ' min' : '15 min' }}
                                </span>
                            </div>

                            @if($lastQuizAttempt)
                                <!-- Mostrar pontuação da última tentativa -->
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">Pontuação:</span>
                                    <span class="font-medium text-green-600 dark:text-green-400">
                                        {{ $lastQuizAttempt->score_percentage }}%
                                    </span>
                                    @if($lastQuizAttempt->finished_at)
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            • {{ $lastQuizAttempt->finished_at->format('d/m/Y') }}
                                        </span>
                                    @endif
                                </div>
                            @endif
                        </div>

                        <!-- Botões de Ação -->
                        <div class="flex items-center gap-2 ml-4">
                            @if($module->slug && $quiz->slug)
                                @if($lastQuizAttempt)
                                    <!-- Quiz já foi realizado -->
                                    <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $quiz->slug]) }}"
                                       class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Refazer
                                    </a>
                                @else
                                    <!-- Quiz ainda não foi realizado -->
                                    <a href="{{ route('student.quiz.start', [$course->slug, $module->slug, $quiz->slug]) }}"
                                       class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                                        </svg>
                                        Iniciar Quiz
                                    </a>
                                @endif
                            @else
                                <button disabled 
                                        class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-gray-400 text-white text-sm font-medium rounded-lg cursor-not-allowed">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    Em Configuração
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endif
