<?php

namespace App\Models;

use App\Models\PlgModule;
use App\Models\PlgCategories;
use App\Models\SysCompany;
use App\Models\SysUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Database\Factories\PlgCourseFactory;


use Plank\Mediable\Mediable;
use Plank\Mediable\MediableInterface;

class PlgCourse extends Model implements MediableInterface
{
    use HasFactory, SoftDeletes, Mediable;

    protected $table = 'plg_courses';
    
    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return PlgCourseFactory::new();
    }
    
    protected $mediableCollections = ['thumbnail'];

    
    protected $fillable = [
        'title', 'slug', 'description', 'short_description',
        'category_id', 'user_id', 'status',
        'featured', 'order', 'company_id'
    ];
    
    public function category()
    {
        return $this->belongsTo(PlgCategories::class, 'category_id');
    }
    
    public function teacher()
    {
        return $this->belongsTo(SysUser::class, 'user_id');
    }
    
    public function students()
    {
        return $this->belongsToMany(PlgStudent::class, 'plg_enrollments', 'course_id', 'student_id')
                    ->withTimestamps()
                    ->withPivot(['company_id', 'status', 'enrolled_at', 'expires_at', 'enrollment_method', 'approval_status', 'requested_at', 'approved_at', 'approved_by', 'rejection_reason']);
    }
    
    public function company()
    {
        return $this->belongsTo(SysCompany::class, 'company_id');
    }
    
    /**
     * Relacionamento com os módulos
     */
    public function modules()
    {
        return $this->hasMany(PlgModule::class, 'course_id')
                    ->orderBy('order');
    }
    
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($course) {
            if (empty($course->slug)) {
                $course->slug = Str::slug($course->title);
            }
        });
    }
} 