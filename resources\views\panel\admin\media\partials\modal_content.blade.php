<!-- Corpo -->
<div class="flex flex-1 overflow-auto">
    <!-- Sidebar (no desktop) -->
    <div id="mediaSidebar" class="w-64 bg-white dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-800 p-4 hidden md:flex flex-col flex-shrink-0">
        <div class="flex-1 overflow-y-auto">
            <ul class="space-y-1">
                <!-- Categorias serão renderizadas dinamicamente -->
            </ul>
        </div>
        
        <!-- Botão para limpar mídias órfãs -->
        <div class="mt-6 pt-4 border-t border-zinc-200 dark:border-zinc-800 flex-shrink-0">
            <button id="cleanOrphanedBtn" class="w-full px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center justify-center text-sm">
                <i class="fas fa-broom mr-2" id="cleanOrphanedBtnIcon"></i>
                <span id="cleanOrphanedBtnText"><PERSON><PERSON> (0)</span>
            </button>
            <p class="text-xs text-zinc-500 mt-2 text-center">
                Remove registros sem arquivo físico
            </p>
        </div>
    </div>

    <!-- Conteúdo Principal -->
    <div id="media-content" class="flex-1 flex flex-col min-h-0">
        <!-- Sidebar mobile e botão de fechar serão criados dinamicamente pelo JavaScript -->

        <!-- Barra de Ferramentas -->
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center p-4 border-b border-zinc-200 dark:border-zinc-800 gap-2 sm:gap-4 flex-shrink-0 bg-white dark:bg-zinc-900">
            <div class="flex items-center gap-3 flex-1">
                <!-- Botão hamburger para toggle da sidebar -->
                <button id="toggleSidebarBtn" class="md:hidden p-2 text-zinc-600 dark:text-zinc-400 hover:text-trends-primary hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-md transition-colors focus:outline-none">
                    <i class="fas fa-bars text-lg"></i>
                </button>
                
                <!-- Campo de busca -->
                <div class="relative flex-1">
                    <input type="text" id="mediaSearch" placeholder="Buscar arquivos..." 
                           class="w-full pl-10 pr-3 py-3 sm:py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-100 text-base sm:text-sm">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-zinc-400">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>
            
            <!-- Botão de upload -->
            <div class="flex items-center gap-2">
                <label class="media-upload-btn px-5 py-3 sm:px-4 sm:py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors inline-flex items-center gap-2 cursor-pointer text-base sm:text-sm">
                    <i class="fas fa-upload"></i>
                    <span>Upload</span>
                    <input type="file" class="media-upload-input hidden" accept="image/*,application/pdf" multiple>
                </label>
            </div>

            <!-- listar por data -->
            <div class="flex items-center gap-2">
                <select id="mediaSort" class="w-36 p-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-100 text-base sm:text-sm">
                    <option value="newest">Mais recentes</option>
                    <option value="oldest">Mais antigas</option>
                </select>
            </div>

            <!-- Selecione a quantidade de midias para listar -->
            <div class="flex items-center gap-2">
                <select id="mediaPerPage" class="w-24 p-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-100 text-base sm:text-sm">
                    <option value="18">18</option>
                    <option value="36">36</option>
                    <option value="72">72</option>
                    <option value="all">Todos</option>
                </select>
            </div>
        </div>

        <!-- Grid de Mídia com scroll próprio -->
        <div id="mediaGridContainer" class="flex-1 overflow-y-auto p-2 sm:p-4">
            <div id="{{ isset($isStandalone) && $isStandalone ? 'mediaGrid' : 'modalMediaGrid' }}"
                 data-standalone="{{ isset($isStandalone) && $isStandalone ? 'true' : 'false' }}"
                 class="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4"
                 data-load-url="{{ route('admin.media.load-more') }}"
                 data-multiple="true">
                <!-- Todo o conteúdo será renderizado dinamicamente via JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Controles de Seleção - Sticky Bottom -->
<div id="selectionControlsContainer" class="hidden sticky bottom-0 border-t border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-3 sm:p-4 z-10 {{ isset($isStandalone) && $isStandalone ? 'bottom-[-24px]' : '' }}">
    <!-- Controles serão renderizados dinamicamente aqui -->
</div>
<!-- Paginação - Fora do conteúdo principal -->
<div class="media-pagination"></div>

<!-- Modal de Preview -->
@include('panel.admin.media.partials.media_preview')