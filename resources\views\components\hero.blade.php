@props(['title', 'subtitle', 'progress' => 4, 'currentModule' => null])

<div class="relative w-full h-[40vh] md:h-[50vh]">
    <!-- Imagem de fundo com efeito blur -->
    <div class="absolute inset-0 w-full h-full">
        <img src="https://cinepop.com.br/wp-content/uploads/2025/05/casa4.jpg" alt="Fundo" class="w-full object-cover blur-sm brightness-50">
        <div class="absolute inset-0 bg-gradient-to-b from-black/80 to-transparent"></div>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> do Hero -->
    <div class="relative z-10 container mx-auto px-4 h-full flex flex-col justify-center">
        <div class="max-w-2xl">
            <h1 class="font-extrabold tracking-wide text-3xl md:text-5xl text-white mb-3">{{ $title }}</h1>
            <p class="text-sm md:text-base text-gray-200 mb-4">{{ $subtitle }}</p>
            <button class="bg-red-600 hover:bg-red-700 text-white px-5 py-2 rounded-lg shadow-lg transition focus:outline-none focus:ring focus:ring-red-600/50" aria-label="Começar o curso agora">
                Começar agora
            </button>
        </div>
    </div>

    <!-- Indicador de progresso
    <div class="absolute top-4 right-4">
        <x-progress-circle :progress="$progress" />
    </div>
    -->
    
    <!-- Mini-preview do módulo atual -->
    @if($currentModule)
    <div class="absolute bottom-4 right-4 md:right-8 backdrop-blur-md bg-black/30 border border-white/10 rounded-lg p-2 flex gap-2 items-center max-w-xs">
        <div class="w-16 h-10 rounded overflow-hidden flex-shrink-0">
            <img src="{{ $currentModule['thumbnail'] }}" alt="Preview" class="w-full h-full object-cover">
        </div>
        <div class="flex-1">
            <p class="text-white text-xs font-medium truncate">{{ $currentModule['title'] }}</p>
            <p class="text-gray-300 text-xs">{{ $currentModule['duration'] }}</p>
        </div>
        <div class="flex-shrink-0">
            <button class="text-white hover:text-red-500 transition" aria-label="Próximo módulo">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>
    </div>
    @endif
</div> 