<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class PlgModuleContent extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'plg_modules_contents';

    protected $fillable = [
        'company_id',
        'user_id', // Campo adicionado para rastreabilidade
        'module_id',
        'title',
        'slug',
        'description',
        'content',
        'content_type',
        'duration',
        'order',
        'active',
    ];

    /**
     * Relacionamento com o módulo.
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(PlgModule::class, 'module_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($content) {
            if (empty($content->slug)) {
                $content->slug = Str::slug($content->title);
            }
        });

        static::updating(function ($content) {
            if ($content->isDirty('title') && empty($content->slug)) {
                $content->slug = Str::slug($content->title);
            }
        });
    }
}