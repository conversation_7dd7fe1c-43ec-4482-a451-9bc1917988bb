<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PlgEnrollment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'plg_enrollments';
    
    protected $fillable = [
        'company_id',
        'user_id', // Campo adicionado para rastreabilidade
        'student_id',
        'module_id',
        'status',
        'module_price_at_time',
        'enrolled_at',
        'expires_at',
        'enrollment_method',
        'approval_status',
        'requested_at',
        'approved_at',
        'approved_by',
        'assigned_teacher_id',
        'rejection_reason'
    ];

    protected $casts = [
        'module_price_at_time' => 'decimal:2',
        'enrolled_at' => 'datetime',
        'expires_at' => 'datetime',
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Relacionamento com o estudante
     */
    public function student()
    {
        return $this->belongsTo(PlgStudent::class, 'student_id');
    }

    /**
     * Relacionamento com o curso
     */
    public function course()
    {
        return $this->belongsTo(PlgCourse::class, 'course_id');
    }

    /**
     * Relacionamento com o módulo
     */
    public function module()
    {
        return $this->belongsTo(PlgModule::class, 'module_id');
    }

    /**
     * Relacionamento com a empresa
     */
    public function company()
    {
        return $this->belongsTo(SysCompany::class, 'company_id');
    }

    /**
     * Relacionamento com o usuário que criou a matrícula
     */
    public function user()
    {
        return $this->belongsTo(SysUser::class, 'user_id');
    }

    /**
     * Relacionamento com o usuário que aprovou a matrícula
     */
    public function approvedBy()
    {
        return $this->belongsTo(SysUser::class, 'approved_by');
    }

    /**
     * Relacionamento com o professor atribuído
     */
    public function assignedTeacher()
    {
        return $this->belongsTo(SysUser::class, 'assigned_teacher_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeCanceled($query)
    {
        return $query->where('status', 'canceled');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    /**
     * Verificar se a matrícula está expirada
     */
    public function isExpired(): bool
    {
        if (!$this->expires_at) {
            return false;
        }
        
        return $this->expires_at->isPast();
    }

    /**
     * Verificar se a matrícula está ativa
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    /**
     * Ativar matrícula
     */
    public function activate()
    {
        $this->update([
            'enrolled_at' => $this->enrolled_at ?? now(),
            'status' => 'active'
        ]);
    }

    /**
     * Cancelar matrícula
     */
    public function cancel()
    {
        $this->update([
            'status' => 'canceled'
        ]);
    }

    /**
     * Expirar matrícula
     */
    public function expire()
    {
        $this->update([
            'status' => 'expired'
        ]);
    }

    /**
     * Verificar se já existe matrícula para o estudante no módulo
     */
    public static function existsForStudentAndModule($studentId, $moduleId)
    {
        return self::where('student_id', $studentId)
                   ->where('module_id', $moduleId)
                   ->whereIn('status', ['active', 'pending'])
                   ->whereIn('approval_status', ['pending', 'approved'])
                   ->exists();
    }

    /**
     * Atributos formatados para DataTable
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Ativo</span>',
            'expired' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Expirado</span>',
            'canceled' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Cancelado</span>',
        ];

        return $badges[$this->status] ?? $this->status;
    }

    public function getApprovalStatusBadgeAttribute()
    {
        $badges = [
            'pending' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pendente</span>',
            'approved' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Aprovado</span>',
            'rejected' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejeitado</span>',
        ];

        return $badges[$this->approval_status] ?? $this->approval_status;
    }

    public function getEnrollmentMethodBadgeAttribute()
    {
        $badges = [
            'self_registration' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Auto-matrícula</span>',
            'admin_created' => '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Admin</span>',
        ];

        return $badges[$this->enrollment_method] ?? $this->enrollment_method;
    }

    public function getPriceFormattedAttribute()
    {
        if ($this->module_price_at_time > 0) {
            return 'R$ ' . number_format($this->module_price_at_time, 2, ',', '.');
        }
        return '<span class="text-green-600 font-semibold">Gratuito</span>';
    }

    public function getEnrolledAtFormattedAttribute()
    {
        return $this->enrolled_at ? $this->enrolled_at->format('d/m/Y H:i') : '-';
    }

    public function getExpiresAtFormattedAttribute()
    {
        if (!$this->expires_at) {
            return '<span class="text-green-600">Sem expiração</span>';
        }
        
        $formatted = $this->expires_at->format('d/m/Y');
        
        if ($this->expires_at->isPast()) {
            return '<span class="text-red-600">' . $formatted . ' (Expirado)</span>';
        }
        
        return $formatted;
    }

    public function getStudentNameAttribute()
    {
        return $this->student ? $this->student->name : '-';
    }

    public function getStudentEmailAttribute()
    {
        return $this->student ? $this->student->email : '-';
    }

    public function getCourseTitleAttribute()
    {
        return $this->module && $this->module->course ? $this->module->course->title : '-';
    }

    public function getModuleTitleAttribute()
    {
        return $this->module ? $this->module->title : '-';
    }



    /**
     * Verifica se tem acesso válido (ativo ou vitalício)
     */
    public function hasValidAccess(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        // Se não tem data de expiração, é vitalício
        if (!$this->expires_at) {
            return true;
        }

        // Verifica se não expirou
        return !$this->isExpired();
    }

    /**
     * Retorna status detalhado da matrícula
     */
    public function getAccessStatus(): array
    {
        if ($this->status !== 'active') {
            return [
                'has_access' => false,
                'status' => $this->status,
                'message' => 'Matrícula ' . $this->status
            ];
        }

        if (!$this->expires_at) {
            return [
                'has_access' => true,
                'status' => 'lifetime',
                'message' => 'Acesso vitalício'
            ];
        }

        if ($this->isExpired()) {
            return [
                'has_access' => false,
                'status' => 'expired',
                'message' => 'Matrícula expirada em ' . $this->expires_at->format('d/m/Y'),
                'expired_at' => $this->expires_at
            ];
        }

        return [
            'has_access' => true,
            'status' => 'active',
            'message' => 'Acesso válido até ' . $this->expires_at->format('d/m/Y'),
            'expires_at' => $this->expires_at
        ];
    }
}
