<x-layouts.student title="Quizzes - {{ $module->title }} - {{ $course->title }}">
    <!-- Breadcrumb -->
    <x-student.breadcrumb :course="$course" :module="$module" :customItems="[['url' => null, 'text' => 'Quizzes e Testes', 'icon' => 'fas fa-clipboard-list']]" />

    <div class="min-h-screen">
        <!-- Main Content Area -->
        <div class="container mx-auto py-6 px-6">
            <!-- Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-clipboard-list text-purple-600 text-3xl"></i>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Quizzes e Testes</h1>
                            <p class="text-gray-600 dark:text-gray-400">{{ $module->title }}</p>
                        </div>
                    </div>
                    <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar ao Módulo
                    </a>
                </div>
                
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-blue-600 text-lg mt-0.5"></i>
                        <div>
                            <h3 class="font-semibold text-blue-900 dark:text-blue-100 mb-1">Instruções Importantes</h3>
                            <p class="text-blue-800 dark:text-blue-200 text-sm">
                                Você precisa de pelo menos 70% de aproveitamento para ser aprovado nos testes. 
                                Certifique-se de estudar todo o material antes de iniciar.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grid de Conteúdo -->
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
                <!-- Seção Principal - Lista de Quizzes (8 colunas no desktop, full width no mobile) -->
                <div class="lg:col-span-8">
                    <!-- Quizzes de Treino -->
                    @if($quizzes->count() > 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-dumbbell text-green-600 text-xl"></i>
                                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Quizzes de Treino</h2>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm mt-2">
                                    Pratique seus conhecimentos com estes quizzes. Você pode refazer quantas vezes quiser.
                                </p>
                            </div>

                            <div class="p-6">
                                <div class="space-y-4">
                                    @foreach($quizzes as $quiz)
                                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                                <div class="flex items-center space-x-4">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                                            <i class="fas fa-play text-green-600 text-lg"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                                            {{ $quiz->title }}
                                                        </h3>
                                                        @if($quiz->description)
                                                            <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">
                                                                {{ $quiz->description }}
                                                            </p>
                                                        @endif
                                                        <div class="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mt-2">
                                                            <span>
                                                                <i class="fas fa-clock mr-1"></i>
                                                                {{ $quiz->time_limit ?? 15 }} minutos
                                                            </span>
                                                            <span>
                                                                <i class="fas fa-question-circle mr-1"></i>
                                                                {{ is_array($quiz->questions) ? count($quiz->questions) : 0 }} questões
                                                            </span>
                                                            <span>
                                                                <i class="fas fa-redo mr-1"></i>
                                                                Tentativas ilimitadas
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-3">
                                                    <button onclick="startTest('{{ $quiz->slug }}', 'quiz')"
                                                            class="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors">
                                                        <i class="fas fa-play mr-2"></i>
                                                        Iniciar Quiz
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Testes Finais -->
                    @if($exams->count() > 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-certificate text-orange-600 text-xl"></i>
                                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Testes de Certificação</h2>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm mt-2">
                                    Testes oficiais para obter sua certificação. Atenção: tentativas limitadas.
                                </p>
                            </div>

                            <div class="p-6">
                                <div class="space-y-4">
                                    @foreach($exams as $exam)
                                        <div class="border border-orange-200 dark:border-orange-800 rounded-lg p-6 bg-orange-50 dark:bg-orange-900/10">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                                                            <i class="fas fa-certificate text-orange-600 text-lg"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                                            {{ $exam->title }}
                                                        </h3>
                                                        @if($exam->description)
                                                            <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">
                                                                {{ $exam->description }}
                                                            </p>
                                                        @endif
                                                        <div class="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mt-2">
                                                            <span>
                                                                <i class="fas fa-clock mr-1"></i>
                                                                {{ $exam->time_limit ?? 30 }} minutos
                                                            </span>
                                                            <span>
                                                                <i class="fas fa-question-circle mr-1"></i>
                                                                {{ $exam->questions->count() }} questões
                                                            </span>
                                                            <span>
                                                                <i class="fas fa-exclamation-triangle mr-1 text-orange-500"></i>
                                                                {{ $exam->max_attempts ?? 3 }} tentativas
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-3">
                                                    <button onclick="startTest('{{ $exam->slug }}', 'exam')"
                                                            class="px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors">
                                                        <i class="fas fa-certificate mr-2"></i>
                                                        Iniciar Teste
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Estado Vazio -->
                    @if($quizzes->count() === 0 && $exams->count() === 0)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-12 text-center">
                                <i class="fas fa-clipboard-list text-gray-400 text-5xl mb-6"></i>
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    Nenhum teste disponível
                                </h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-6">
                                    Este módulo ainda não possui quizzes ou testes configurados.
                                </p>
                                <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Voltar ao Módulo
                                </a>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar Direita (4 colunas no desktop, full width no mobile) -->
                <div class="lg:col-span-4">
                    <!-- Card de Progresso -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Seu Progresso</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-blue-600 mb-1">0%</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Testes Concluídos</div>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
                                    0 de {{ $quizzes->count() + $exams->count() }} testes concluídos
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card de Dicas -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Dicas de Estudo</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                                    <span>Estude todo o material antes de fazer os testes</span>
                                </div>
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-clock text-blue-500 mt-0.5"></i>
                                    <span>Gerencie bem seu tempo durante os testes</span>
                                </div>
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-redo text-green-500 mt-0.5"></i>
                                    <span>Use os quizzes de treino para praticar</span>
                                </div>
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-exclamation-triangle text-orange-500 mt-0.5"></i>
                                    <span>Testes de certificação têm tentativas limitadas</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function startTest(testSlug, testType) {
        const courseSlug = '{{ $course->slug }}';
        const moduleSlug = '{{ $module->slug }}';
        
        // Navegar para o teste baseado no tipo
        if (testType === 'quiz') {
            window.location.href = `/student/${courseSlug}/${moduleSlug}/quiz/${testSlug}`;
        } else {
            window.location.href = `/student/${courseSlug}/${moduleSlug}/teste/${testSlug}`;
        }
    }
    </script>
</x-layouts.student>
