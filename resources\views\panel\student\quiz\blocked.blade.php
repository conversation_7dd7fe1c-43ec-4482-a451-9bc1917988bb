<x-layouts.quiz :title="'Teste Bloqueado - ' . $test->name">
    <!-- Breadcrumb -->
    <x-student.breadcrumb
        :course="$course"
        :module="$module"
        :test="$test" />

    <div class="max-w-2xl mx-auto px-4">
        <div class="quiz-container p-8 text-center fade-in">
            <!-- <PERSON><PERSON><PERSON> de bloqueio -->
            <div class="mb-6">
                <i class="fas fa-lock text-6xl text-red-500 mb-4"></i>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Teste Temporariamente Bloqueado</h1>
                <p class="text-gray-600">{{ $course->title }} - {{ $module->title }}</p>
            </div>

            <!-- Informações do teste -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-red-800 mb-4">{{ $test->name }}</h2>
                
                @if($student->testAttempts()->where('test_id', $test->id)->where('passed', true)->exists())
                    <!-- Já passou no teste -->
                    <div class="flex items-center justify-center space-x-2 text-green-700 mb-4">
                        <i class="fas fa-check-circle text-2xl"></i>
                        <span class="text-lg font-semibold">Você já foi aprovado neste teste!</span>
                    </div>
                    
                    @php
                        $passedAttempt = $student->testAttempts()
                            ->where('test_id', $test->id)
                            ->where('passed', true)
                            ->first();
                    @endphp
                    
                    <div class="bg-white rounded-lg p-4 mb-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-green-600">{{ $passedAttempt->score }}%</div>
                                <div class="text-sm text-gray-600">Sua Nota</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-blue-600">{{ $test->passing_score }}%</div>
                                <div class="text-sm text-gray-600">Nota Mínima</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-gray-600">{{ $passedAttempt->completed_at ? $passedAttempt->completed_at->format('d/m/Y') : 'N/A' }}</div>
                                <div class="text-sm text-gray-600">Data de Aprovação</div>
                            </div>
                        </div>
                    </div>
                    
                    <p class="text-gray-700">
                        Parabéns! Você já foi aprovado neste teste e não precisa refazê-lo.
                    </p>
                @else
                    <!-- Ainda não passou -->
                    <div class="flex items-center justify-center space-x-2 text-red-700 mb-4">
                        <i class="fas fa-clock text-2xl"></i>
                        <span class="text-lg font-semibold">Tempo de espera ativo</span>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 mb-4">
                        <p class="text-gray-700 mb-2">
                            <strong>Tempo restante:</strong> {{ $cooldownRemaining }}
                        </p>
                        <p class="text-sm text-gray-600">
                            Você poderá tentar novamente após o período de espera de {{ $test->cooldown_hours }} horas.
                        </p>
                    </div>

                    @php
                        $lastAttempt = $student->attempts()
                            ->where('test_id', $test->id)
                            ->where('status', 'completed')
                            ->latest('finished_at')
                            ->first();
                    @endphp

                    @if($lastAttempt)
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <h3 class="font-semibold text-gray-800 mb-2">Sua última tentativa:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="text-xl font-bold text-gray-600">{{ $lastAttempt->score }}%</div>
                                    <div class="text-sm text-gray-600">Nota Obtida</div>
                                </div>
                                <div>
                                    <div class="text-xl font-bold text-blue-600">{{ $test->passing_score }}%</div>
                                    <div class="text-sm text-gray-600">Nota Necessária</div>
                                </div>
                                <div>
                                    <div class="text-xl font-bold text-gray-600">{{ $lastAttempt->completed_at ? $lastAttempt->completed_at->format('d/m/Y H:i') : 'Em andamento' }}</div>
                                    <div class="text-sm text-gray-600">Data da Tentativa</div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-semibold text-blue-800 mb-2">Dicas para a próxima tentativa:</h3>
                        <ul class="text-sm text-blue-700 text-left space-y-1">
                            <li>• Revise o conteúdo do módulo antes de tentar novamente</li>
                            <li>• Certifique-se de estar em um ambiente tranquilo</li>
                            <li>• Leia cada questão com atenção</li>
                            <li>• Não mude de aba durante o teste</li>
                        </ul>
                    </div>
                @endif
            </div>

            <!-- Botões de ação -->
            <div class="space-y-4">
                <a href="{{ route('student.module.show', [$course->slug, $module->slug]) }}" 
                   class="btn-primary inline-block">
                    <i class="fas fa-book mr-2"></i>
                    Revisar Conteúdo do Módulo
                </a>
                
                <div>
                    <a href="{{ route('student.course.show', $course->slug) }}"
                       class="btn-secondary inline-block">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar ao Curso
                    </a>
                </div>
            </div>

            <!-- Informações adicionais -->
            <div class="mt-8 text-sm text-gray-500">
                <p>
                    <i class="fas fa-info-circle mr-1"></i>
                    O tempo de espera é necessário para garantir que você tenha tempo adequado para estudar entre as tentativas.
                </p>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Atualizar página automaticamente quando o cooldown expirar
        @if(!$student->attempts()->where('test_id', $test->id)->where('passed', true)->exists())
            // Calcular tempo restante em milissegundos
            @php
                $lastAttempt = $student->attempts()
                    ->where('test_id', $test->id)
                    ->where('status', 'completed')
                    ->latest('finished_at')
                    ->first();

                if ($lastAttempt) {
                    $canTakeAgainAt = $lastAttempt->finished_at->addHours($test->cooldown_hours);
                    $remainingMs = $canTakeAgainAt->diffInMilliseconds(now());
                } else {
                    $remainingMs = 0;
                }
            @endphp
            
            @if($remainingMs > 0)
                setTimeout(function() {
                    location.reload();
                }, {{ $remainingMs }});
                
                // Mostrar countdown
                let remainingTime = {{ floor($remainingMs / 1000) }};
                
                function updateCountdown() {
                    if (remainingTime <= 0) {
                        location.reload();
                        return;
                    }
                    
                    const hours = Math.floor(remainingTime / 3600);
                    const minutes = Math.floor((remainingTime % 3600) / 60);
                    const seconds = remainingTime % 60;
                    
                    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    // Atualizar display se existir elemento para isso
                    const countdownElement = document.getElementById('countdown');
                    if (countdownElement) {
                        countdownElement.textContent = timeString;
                    }
                    
                    remainingTime--;
                }
                
                // Atualizar a cada segundo
                setInterval(updateCountdown, 1000);
                updateCountdown(); // Executar imediatamente
            @endif
        @endif
    </script>
    @endpush
</x-layouts.quiz>
