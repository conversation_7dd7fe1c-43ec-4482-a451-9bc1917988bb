@props(['course', 'student'])

@php
// Calcular estatísticas do aluno no curso
$totalQuizzes = $course->modules->sum(function($module) {
    return $module->tests->where('test_type', 'quiz')->count();
});

$completedQuizzes = 0;
$totalScore = 0;
$scoreCount = 0;

// Calcular pontuação média dos quizzes completados
foreach($course->modules as $module) {
    foreach($module->tests->where('test_type', 'quiz') as $test) {
        $lastAttempt = $student->attempts()
            ->where('test_id', $test->id)
            ->where('status', 'completed')
            ->latest('finished_at')
            ->first();
        
        if($lastAttempt) {
            $completedQuizzes++;
            $totalScore += $lastAttempt->score_percentage ?? 0;
            $scoreCount++;
        }
    }
}

$averageScore = $scoreCount > 0 ? round($totalScore / $scoreCount) : 0;
$progressPercentage = $totalQuizzes > 0 ? round(($completedQuizzes / $totalQuizzes) * 100) : 0;

// Determinar nível de desempenho
$performanceLevel = 'Iniciante';
$performanceBadge = 'beginner';
if($averageScore >= 90) {
    $performanceLevel = 'Excelente';
    $performanceBadge = 'completed';
} elseif($averageScore >= 80) {
    $performanceLevel = 'Muito Bom';
    $performanceBadge = 'available';
} elseif($averageScore >= 70) {
    $performanceLevel = 'Bom';
    $performanceBadge = 'in-progress';
}
@endphp

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4"></path>
            </svg>
            Meu Progresso
        </h3>

        <!-- Pontuação Média -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Pontuação Média</span>
                <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ $averageScore }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: {{ $averageScore }}%"></div>
            </div>
        </div>

        <!-- Progresso do Curso -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Progresso do Curso</span>
                <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ $completedQuizzes }}/{{ $totalQuizzes }} quizzes</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: {{ $progressPercentage }}%"></div>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $progressPercentage }}% concluído</p>
        </div>

        <!-- Nível de Desempenho -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Nível de Desempenho</span>
                <x-ui.badge :variant="$performanceBadge">{{ $performanceLevel }}</x-ui.badge>
            </div>
        </div>

        <!-- Estatísticas Rápidas -->
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-lg font-bold text-gray-900 dark:text-white">{{ $completedQuizzes }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Quizzes Concluídos</div>
            </div>
            <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-lg font-bold text-gray-900 dark:text-white">{{ $totalQuizzes - $completedQuizzes }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Quizzes Restantes</div>
            </div>
        </div>

        @if($averageScore >= 70 && $progressPercentage >= 80)
            <!-- Conquista de Destaque -->
            <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Parabéns!</p>
                        <p class="text-xs text-yellow-700 dark:text-yellow-300">Você está indo muito bem neste curso!</p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
