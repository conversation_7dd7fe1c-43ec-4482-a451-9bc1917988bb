<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->string('log_name')->nullable(); // Nome do log (auth, quiz, admin, etc.)
            $table->text('description'); // Descrição da atividade
            $table->unsignedBigInteger('subject_id')->nullable(); // ID do objeto afetado
            $table->string('subject_type')->nullable(); // Tipo do objeto (User, Course, etc.)
            $table->unsignedBigInteger('causer_id')->nullable(); // Quem causou a ação
            $table->string('causer_type')->nullable(); // Tipo de quem causou (User, System, etc.)
            $table->json('properties')->nullable(); // Dados adicionais da atividade
            $table->string('event')->nullable(); // Tipo de evento (created, updated, deleted, etc.)
            $table->string('ip_address', 45)->nullable(); // IP do usuário
            $table->string('user_agent')->nullable(); // User agent
            $table->timestamps();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            
            // Índices para performance
            $table->index(['company_id', 'log_name']); // Multi-tenancy + nome do log
            $table->index(['subject_type', 'subject_id']); // Objeto afetado
            $table->index(['causer_type', 'causer_id']); // Quem causou
            $table->index(['log_name', 'created_at']); // Log + data
            $table->index(['event', 'created_at']); // Evento + data
            $table->index(['company_id', 'created_at']); // Multi-tenancy + data
            $table->index(['created_at']); // Data
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_activity_logs');
    }
};
