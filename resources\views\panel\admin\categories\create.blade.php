@extends('layouts.panel')

@section('title', isset($category) ? 'Editar Categoria' : 'Nova Categoria')
@section('page_title', isset($category) ? 'Editar Categoria' : 'Nova Categoria')

@section('content')
    <div class="animate-fade-in">
        @if ($errors->any())
            <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <strong class="font-medium">Atenção!</strong>
                <ul class="mt-3 list-disc list-inside text-sm">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form
            action="{{ isset($category) ? route('admin.categories.update', $category->id) : route('admin.categories.store') }}"
            method="POST" id="categoryForm">
            @csrf
            @if (isset($category))
                @method('PUT')
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div
                        class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg mb-6">
                        <div
                            class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
                            <div class="flex items-center gap-2">
                                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                                <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Informações da Categoria
                                </h1>
                            </div>
                            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                                Preencha os dados para {{ isset($category) ? 'editar' : 'criar' }} uma categoria na
                                plataforma.
                            </p>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Título da Categoria -->
                            <div>
                                <label for="title"
                                    class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Título <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="title" name="title"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                    value="{{ old('title', $category->title ?? '') }}" required>
                            </div>

                            <!-- Descrição -->
                            <div>
                                <label for="description"
                                    class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Descrição
                                </label>
                                <textarea name="description" id="description" rows="4"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                    placeholder="Digite uma descrição para a categoria...">{{ old('description', $category->description ?? '') }}</textarea>
                            </div>

                           










                            <!-- Thumbnail da categoria -->

                            <div class="media-selector">
                                <div class="flex justify-between items-center mb-2">
                       
                                <button type="button" 
                                class="{{ isset($category) && $category->hasMedia('thumbnail') ? 'hidden' : '' }} open-media-modal w-full px-4 py-8 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg hover:border-trends-primary dark:hover:border-trends-primary transition-colors group"
                               data-mediable-type="App\Models\PlgCategory"
                                        data-tag="thumbnail"
                                        data-input-id="thumbnail"
                                        data-preview-id="thumbnail-preview" 
                                        data-multiple="false">
                                <div class="text-center">
                                    <i class="fas fa-image text-2xl text-zinc-400 dark:text-zinc-600 group-hover:text-trends-primary transition-colors"></i>
                                    <p class="mt-2 text-sm text-zinc-500 dark:text-zinc-400 group-hover:text-trends-primary transition-colors">
                                        Clique para selecionar uma imagem para a capa da categoria!
                                    </p>
                                </div>
                            </button>

                                </div>

                                <input type="hidden" name="thumbnail" class="media-input" data-preview-id="thumbnail-preview" id="thumbnail" value="{{ old('thumbnail', isset($category) && $category->hasMedia('thumbnail') ? $category->firstMedia('thumbnail')->id : '') }}">

                                <div class="media-preview {{ isset($category) && $category->hasMedia('thumbnail') ? '' : 'hidden' }}" id="thumbnail-preview">
                                    @if (isset($category) && $category->hasMedia('thumbnail'))
                                        @php
                                            $thumbnailMedia = $category->firstMedia('thumbnail');
                                            $thumbnailUrl = route('media.serve', ['path' => $thumbnailMedia->getDiskPath()]) . '?w=250&fit=crop&fm=webp';
                                        @endphp
                                            <div class="relative w-64">
                                            <img src="{{ $thumbnailUrl }}" alt="{{ $category->title ?? 'Thumbnail da categoria' }}" class="w-full h-32 object-cover rounded-lg">
                                                <button type="button" class="preview-remove-btn absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600">
                                                    <i class="fas fa-times text-xs"></i>
                                                </button>
                                            </div>
                                            <p class="text-sm text-zinc-500 mt-2 truncate">{{ $thumbnailMedia->filename }}</p>
                                    @endif
                                </div>
                                <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendado: 1280x720px</p>
                            </div>









                            <!-- Galeria da Categoria -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Galeria da Categoria
                                    </label>
                                    <div class="flex items-center gap-2">
                                        <button type="button"
                                            class="open-media-modal inline-flex items-center gap-2 px-3 py-1.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                                            data-target="gallery"
                                            data-multiple="true">
                                            <i class="fas fa-images"></i>
                                            <span>Adicionar à Galeria</span>
                                        </button>
                                        <button type="button" id="clear-gallery-btn"
                                            class="hidden inline-flex items-center gap-2 px-3 py-1.5 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                                            onclick="clearGallery()">
                                            <i class="fas fa-trash"></i>
                                            <span>Limpar Galeria</span>
                                        </button>
                                    </div>
                                </div>

                                <input type="hidden" name="gallery" id="gallery"
                                    value="{{ old('gallery', isset($category) && $category->hasMedia('gallery') ? json_encode($category->getMedia('gallery')->pluck('id')->toArray()) : '[]') }}">

                                <div class="gallery-preview" id="gallery-preview">
                                    @if (isset($category))
                                        @php
                                            $galleryMedia = $category->getMedia('gallery');
                                        @endphp
                                        @if ($galleryMedia->count() > 0)
                                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                                @foreach ($galleryMedia as $media)
                                                    <div class="relative group">
                                                        <img src="{{ url('/media/' . $media->getDiskPath()) }}?w=200&h=150&fit=crop&fm=webp"
                                                            alt="Imagem da galeria"
                                                            class="w-full h-24 object-cover rounded-lg"
                                                            onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'flex items-center justify-center w-full h-24 bg-zinc-100 rounded-lg\'><i class=\'fas fa-image text-red-400 text-2xl\'></i></div>';">
                                                        <button type="button"
                                                            class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                                                            onclick="removeGalleryImage(this, {{ $media->id }})">
                                                            <i class="fas fa-times text-xs"></i>
                                                        </button>
                                                        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg truncate">
                                                            {{ $media->filename }}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @else
                                            <div class="text-center py-8 text-zinc-500 dark:text-zinc-400 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg">
                                                <i class="fas fa-images text-3xl mb-2"></i>
                                                <p>Nenhuma imagem na galeria</p>
                                                <p class="text-xs">Use o botão "Adicionar à Galeria" para começar</p>
                                            </div>
                                        @endif
                                    @else
                                        <div class="text-center py-8 text-zinc-500 dark:text-zinc-400 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg">
                                            <i class="fas fa-images text-3xl mb-2"></i>
                                            <p>Nenhuma imagem na galeria</p>
                                            <p class="text-xs">Use o botão "Adicionar à Galeria" para começar</p>
                                        </div>
                                    @endif
                                </div>
                                <p class="text-xs text-zinc-500 mt-1 text-right">Múltiplas imagens para showcase da categoria</p>
                            </div>

                            <!-- Ícone e Ordem -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    @include('panel.admin.categories.partials.icon-selector', [
                                        'inputName' => 'icon',
                                        'inputId' => 'icon',
                                        'currentValue' => old('icon', $category->icon ?? ''),
                                        'label' => 'Ícone da Categoria'
                                    ])
                                </div>

                                <div class="space-y-2">
                                    <label for="order" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Ordem
                                    </label>
                                    <input type="number" name="order" id="order" min="0"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('order', $category->order ?? 0) }}">
                                    <p class="text-xs text-zinc-500 dark:text-zinc-400">
                                        Ordem de exibição da categoria (0 = primeiro)
                                    </p>
                                </div>
                            </div>

                            <!-- Configurações -->
                            <div class="flex items-center gap-4">
                                <label class="inline-flex items-center cursor-pointer relative">
                                    <input type="checkbox" name="active" value="1" class="sr-only peer"
                                        {{ old('active', isset($category) ? $category->active : true) ? 'checked' : '' }}>
                                    <div
                                        class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary">
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Categoria
                                        ativa</span>
                                </label>
                            </div>
                        </div>

                        <div
                            class="px-6 py-4 bg-zinc-50 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800">
                            <div class="flex justify-end gap-4">
                                <a href="{{ route('admin.categories.index') }}"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                                    <i class="fas fa-times"></i>
                                    <span>Cancelar</span>
                                </a>
                                <button type="submit" id="submit-button"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                                    <i class="fas fa-save"></i>
                                    <span>{{ isset($category) ? 'Atualizar' : 'Salvar' }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                @include('panel.admin.categories.partials.preview')
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // ===== ELEMENTOS DO DOM =====
            const galleryInput = document.getElementById('gallery');
            const galleryPreview = document.getElementById('gallery-preview');

            // Preview da galeria quando mídias são selecionadas
            if (galleryInput) {
                galleryInput.addEventListener('mediaSelected', (e) => {
                    const media = e.detail.media;
                    updateGalleryPreview(media);
                });
            }

            // Função para atualizar preview da galeria
            function updateGalleryPreview(mediaArray) {
                if (!galleryPreview || !galleryInput) return;

                // Converter para array se não for
                const newMediaList = Array.isArray(mediaArray) ? mediaArray : [mediaArray];
                
                console.log('updateGalleryPreview - Iniciando:', {
                    newMediaReceived: newMediaList.length,
                    newMediaIds: newMediaList.map(m => m.id)
                });
                
                // Obter IDs atuais da galeria do input hidden
                let currentIds = [];
                try {
                    const inputValue = galleryInput.value.trim();
                    currentIds = inputValue ? JSON.parse(inputValue) : [];
                } catch (e) {
                    console.warn('Erro ao parsear gallery input:', e);
                    currentIds = [];
                }
                
                console.log('IDs atuais no input:', currentIds);
                
                // Criar mapa de mídias existentes baseado no DOM atual
                let allMediaMap = new Map();
                
                // Primeiro, adicionar mídias que já estão no DOM (preservar estado visual)
                const existingImages = galleryPreview.querySelectorAll('.relative.group');
                existingImages.forEach(container => {
                    const button = container.querySelector('button[onclick*="removeGalleryImage"]');
                    if (button) {
                        const onclickStr = button.getAttribute('onclick');
                        const mediaIdMatch = onclickStr.match(/removeGalleryImage\(this,\s*(\d+)\)/);
                        if (mediaIdMatch) {
                            const mediaId = parseInt(mediaIdMatch[1]);
                            const img = container.querySelector('img');
                            const filenameEl = container.querySelector('.absolute.bottom-0');
                            
                            if (img && filenameEl) {
                                allMediaMap.set(mediaId, {
                                    id: mediaId,
                                    url: img.src,
                                    thumbnail_url: img.src,
                                    filename: filenameEl.textContent.trim()
                                });
                                
                                // Garantir que o ID está na lista atual
                                if (!currentIds.includes(mediaId)) {
                                    currentIds.push(mediaId);
                                }
                            }
                        }
                    }
                });
                
                console.log('Mídias existentes no DOM:', Array.from(allMediaMap.keys()));
                
                // Agora adicionar novas mídias (apenas as que não existem)
                let newlyAdded = 0;
                newMediaList.forEach(media => {
                    if (media && media.id && !allMediaMap.has(media.id)) {
                        allMediaMap.set(media.id, media);
                        if (!currentIds.includes(media.id)) {
                            currentIds.push(media.id);
                            newlyAdded++;
                        }
                    }
                });
                
                console.log('Novas mídias adicionadas:', newlyAdded);
                console.log('IDs finais:', currentIds);
                
                // Atualizar input hidden
                galleryInput.value = JSON.stringify(currentIds);
                
                // Criar array com todas as mídias para renderizar
                const allMediaList = Array.from(allMediaMap.values());
                
                console.log('updateGalleryPreview - Final:', {
                    totalMedias: allMediaList.length,
                    allMediaIds: allMediaList.map(m => m.id),
                    inputValue: galleryInput.value
                });
                
                // Renderizar preview com todas as mídias
                renderGalleryPreview(allMediaList, galleryPreview);
                
                // Atualizar visibilidade do botão "Limpar Galeria"
                updateClearGalleryButton(allMediaList.length);
            }

            // Função para renderizar preview da galeria
            function renderGalleryPreview(mediaList, container) {
                if (!mediaList || mediaList.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-8 text-zinc-500 dark:text-zinc-400 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg">
                            <i class="fas fa-images text-3xl mb-2"></i>
                            <p>Nenhuma imagem na galeria</p>
                            <p class="text-xs">Use o botão "Adicionar à Galeria" para começar</p>
                        </div>
                    `;
                    return;
                }

                const gridHtml = mediaList.map(media => {
                    // Usar URL consistente com parâmetros de redimensionamento
                    let imageUrl = media.thumbnail_url || media.url;
                    
                    // Se a URL não tem parâmetros de redimensionamento, adicionar
                    if (imageUrl && !imageUrl.includes('?w=') && !imageUrl.includes('&w=')) {
                        imageUrl += (imageUrl.includes('?') ? '&' : '?') + 'w=200&h=150&fit=crop&fm=webp';
                    }
                    
                    const filename = media.filename || media.original_filename || 'Sem nome';
                    
                    return `
                        <div class="relative group">
                            <img src="${imageUrl}" 
                                 alt="Imagem da galeria"
                                 class="w-full h-24 object-cover rounded-lg"
                                 onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\\'flex items-center justify-center w-full h-24 bg-zinc-100 rounded-lg\\'><i class=\\'fas fa-image text-red-400 text-2xl\\'></i></div>';">
                            <button type="button"
                                    class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                                    onclick="removeGalleryImage(this, ${media.id})">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                            <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg truncate">
                                ${filename}
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = `<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">${gridHtml}</div>`;
                
                console.log('Galeria renderizada com', mediaList.length, 'itens:', mediaList.map(m => m.id));
            }

            // Função para atualizar visibilidade do botão "Limpar Galeria"
            function updateClearGalleryButton(imageCount) {
                const clearBtn = document.getElementById('clear-gallery-btn');
                if (clearBtn) {
                    if (imageCount >= 2) {
                        clearBtn.classList.remove('hidden');
                        clearBtn.classList.add('inline-flex');
                    } else {
                        clearBtn.classList.add('hidden');
                        clearBtn.classList.remove('inline-flex');
                    }
                }
            }
            
            // ===== INICIALIZAÇÃO DA GALERIA =====
            // Verificar se há imagens na galeria ao carregar a página
            if (galleryInput && galleryInput.value) {
                try {
                    const galleryIds = JSON.parse(galleryInput.value || '[]');
                    const existingImages = galleryPreview ? galleryPreview.querySelectorAll('.relative.group').length : 0;
                    updateClearGalleryButton(Math.max(galleryIds.length, existingImages));
                    
                    // Forçar re-renderização com layout padrão se há imagens no DOM
                    if (existingImages > 0 && galleryPreview) {
                        standardizeGalleryLayout();
                    }
                } catch (e) {
                    updateClearGalleryButton(0);
                }
            }
            
            // Função para padronizar o layout da galeria
            function standardizeGalleryLayout() {
                const container = galleryPreview;
                if (!container) return;
                
                const existingImages = container.querySelectorAll('.relative.group');
                if (existingImages.length === 0) return;
                
                // Extrair dados das imagens existentes
                const mediaList = [];
                existingImages.forEach(imageContainer => {
                    const img = imageContainer.querySelector('img');
                    const button = imageContainer.querySelector('button[onclick*="removeGalleryImage"]');
                    const filenameEl = imageContainer.querySelector('.absolute.bottom-0');
                    
                    if (img && button && filenameEl) {
                        const onclickStr = button.getAttribute('onclick');
                        const mediaIdMatch = onclickStr.match(/removeGalleryImage\(this,\s*(\d+)\)/);
                        
                        if (mediaIdMatch) {
                            mediaList.push({
                                id: parseInt(mediaIdMatch[1]),
                                url: img.src,
                                thumbnail_url: img.src,
                                filename: filenameEl.textContent.trim()
                            });
                        }
                    }
                });
                
                // Re-renderizar com layout padrão
                if (mediaList.length > 0) {
                    console.log('Padronizando layout da galeria com', mediaList.length, 'itens');
                    renderGalleryPreview(mediaList, container);
                }
            }
        });

        // Funções globais para galeria
        window.removeGalleryImage = function(button, mediaId) {
            const currentGalleryInput = document.getElementById('gallery');
            if (!currentGalleryInput) return;
            
            // Remover do input hidden
            let currentIds = [];
            try {
                currentIds = JSON.parse(currentGalleryInput.value || '[]');
            } catch (e) {
                currentIds = [];
            }
            
            currentIds = currentIds.filter(id => id !== mediaId);
            currentGalleryInput.value = JSON.stringify(currentIds);
            
            // Remover do DOM
            button.closest('.relative').remove();
            
            // Verificar se ainda há imagens
            const container = document.getElementById('gallery-preview');
            const remainingImages = container.querySelectorAll('.relative.group');
            
            if (remainingImages.length === 0) {
                // Mostrar estado vazio
                container.innerHTML = `
                    <div class="text-center py-8 text-zinc-500 dark:text-zinc-400 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg">
                        <i class="fas fa-images text-3xl mb-2"></i>
                        <p>Nenhuma imagem na galeria</p>
                        <p class="text-xs">Use o botão "Adicionar à Galeria" para começar</p>
                    </div>
                `;
            }
            
            // Atualizar visibilidade do botão "Limpar Galeria"
            updateClearGalleryButton(remainingImages.length);
        };

        // Função global para limpar toda a galeria
        window.clearGallery = function() {
            if (!confirm('Tem certeza que deseja remover todas as imagens da galeria?')) {
                return;
            }
            
            const currentGalleryInput = document.getElementById('gallery');
            const galleryPreview = document.getElementById('gallery-preview');
            
            if (currentGalleryInput) {
                currentGalleryInput.value = '[]';
            }
            
            if (galleryPreview) {
                galleryPreview.innerHTML = `
                    <div class="text-center py-8 text-zinc-500 dark:text-zinc-400 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg">
                        <i class="fas fa-images text-3xl mb-2"></i>
                        <p>Nenhuma imagem na galeria</p>
                        <p class="text-xs">Use o botão "Adicionar à Galeria" para começar</p>
                    </div>
                `;
            }
            
            // Esconder botão "Limpar Galeria"
            updateClearGalleryButton(0);
        };


    </script>
@endpush
