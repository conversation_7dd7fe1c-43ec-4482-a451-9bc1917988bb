# Sistema de Gestão de Cursos e Quizzes

## 📋 Sobre o Projeto

Sistema completo para gestão de cursos online com módulos de quizzes e simulados, desenvolvido com Laravel. O sistema possui dois painéis principais: Administrativo e <PERSON>uno, oferecendo uma experiência completa de ensino e aprendizagem.

## 🚀 Funcionalidades

### Panel Administrativo

#### 👥 Gestão de Usuários
- Administradores: CRUD completo
- Alunos: CRUD completo
- Matrículas: Gerenciamento com controle de expiração

#### 📚 Gestão de Cursos
- CRUD completo com função de duplicação
- Categorização de cursos
- Suporte a múltiplos tipos de conteúdo:
  - Textos
  - Imagens
  - PDFs
  - Vídeos (YouTube/Vimeo)

#### 📝 Módulos
- CRUD completo com duplicação
- Conteúdo multimídia integrado
- Organização hierárquica

#### ✅ Sistema de Quizzes
- Criação de questionários por módulo
- Tipos de questões:
  - Múltipla escolha
  - Verdadeiro/Falso
- Importação via Excel (.xls)
- Configurações personalizáveis:
  - Quantidade de questões por página
  - Aleatorização de perguntas
  - Limite de tentativas
  - Feedback de respostas

#### 📊 Simulados
- Banco de questões dedicado
- Integração com módulos
- Sistema de avaliação final

#### 📈 Relatórios
- Progresso dos alunos
- Desempenho em quizzes
- Estatísticas gerais

### Panel do Aluno

- Autenticação e gestão de perfil
- Visualização de cursos matriculados
- Realização de quizzes
- Acompanhamento de progresso
- Sistema de recuperação de senha

## 🔄 Fluxo do Sistema

1. Administrador configura cursos e conteúdos
2. Aluno realiza cadastro e matrícula
3. Sistema libera módulos progressivamente
4. Aluno completa quizzes e avaliações
5. Sistema calcula e apresenta resultados

## 🛠 Recursos Técnicos

### Segurança
- Hash de senhas
- Autenticação JWT
- Proteção contra ataques comuns

### Integrações
- API WordPress para sincronização de alunos
- Suporte a vídeos externos (YouTube/Vimeo)

### Interface
- Design responsivo
- Compatível com dispositivos móveis
- UX/UI intuitiva

## 💾 Banco de Dados

- MySQL
- Estrutura otimizada para performance
- Backup automático

## 🚀 Instalação

```bash
# Clone o repositório
git clone [url-do-repositório]

# Instale as dependências
composer install
npm install

# Configure o ambiente
cp .env.example .env
php artisan key:generate

# Configure o banco de dados no arquivo .env

# Execute as migrações
php artisan migrate

# Inicie o servidor
php artisan serve
```

## 📋 Requisitos

- PHP >= 8.1
- MySQL >= 5.7
- Composer
- Node.js >= 14.x

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie sua feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 📧 Suporte

Para suporte, envie um email para [<EMAIL>]
