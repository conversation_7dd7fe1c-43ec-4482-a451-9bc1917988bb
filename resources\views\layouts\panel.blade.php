<!DOCTYPE html>
<html lang="pt-BR" class="h-full">
<head>
    <!-- Anti-flash snippet -->
    <script>
        (function() {
            try {
                const theme = localStorage.getItem('theme') || 'dark';
                const root = document.documentElement;
                root.classList.add(theme);
                
                // Aplicar cores críticas imediatamente
                const style = document.createElement('style');
                style.textContent = `
                    :root { background: #ffffff !important; }
                    .dark { background: #121212 !important; }
                    body { opacity: 0; }
                    .theme-loaded body { opacity: 1; transition: opacity .2s ease; }
                `;
                document.head.appendChild(style);
                
                // Marcar como carregado após um pequeno delay
                setTimeout(() => root.classList.add('theme-loaded'), 50);
            } catch (e) {
                console.error('Theme initialization error:', e);
            }
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Trends Quiz') }} - @yield('title')</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @stack('styles')
</head>
<body class="bg-dark-100 text-accent-700 h-full overflow-hidden">
    <div class="h-full flex">
        {{-- Sidebar Desktop --}}
        <aside class="sidebar transition-all duration-300 ease-in-out bg-[#1e1e1e] shadow-lg hidden md:flex flex-col border-r border-[#2c2c2c] text-gray-100 flex-shrink-0 w-64">
            @include('panel.admin.includes.sidebar', ['mobile' => false])
        </aside>

        {{-- Main Content --}}
        <main class="flex-1 flex flex-col h-full overflow-hidden transition-all duration-300 ease-in-out">
            
            {{-- Top Bar --}}
            <header class="bg-dark-200 shadow-sm z-10 border-b border-dark-300 flex-shrink-0">
                <div class="flex items-center justify-between p-4">
                    {{-- Mobile Menu Button - Only visible on mobile --}}
                    <div class="flex items-center gap-3">
                        <button id="mobile-menu-button" class="md:hidden text-accent-600 hover:text-accent-400 focus:outline-none">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        
                        {{-- Desktop Only Toggle Button --}}
                        <button id="sidebar-toggle" class="sidebar-toggle text-accent-600 hover:text-accent-400 focus:outline-none hidden md:block">
                            <i class="fas fa-chevron-left text-xl"></i>
                        </button>
                        
                        <h1 class="text-xl font-semibold text-accent-500">@yield('page_title')</h1>
                    </div>


                    {{-- Right Actions --}}
                    <div class="flex items-center gap-4">
                        {{-- Theme Toggle --}}
                        <button id="theme-toggle" class="text-accent-600 hover:text-accent-400">
                            <i id="theme-toggle-dark-icon" class="fas fa-moon text-xl hidden"></i>
                            <i id="theme-toggle-light-icon" class="fas fa-sun text-xl text-yellow-500"></i>
                        </button>
                        <button class="text-accent-600 hover:text-accent-400">
                            <i class="fas fa-bell text-xl"></i>
                        </button>
                        <div class="relative group">
                            <button class="flex items-center gap-2 text-accent-600 hover:text-accent-400">
                                <div class="w-8 h-8 rounded-full bg-dark-300 flex items-center justify-center relative">
                                    <svg class="absolute inset-0 w-8 h-8" viewBox="0 0 32 32">
                                        <circle class="text-primary-300" cx="16" cy="16" r="14" fill="none" stroke-width="2" stroke="currentColor" stroke-dasharray="88" stroke-dashoffset="0" style="transform: rotate(-90deg); transform-origin: 16px 16px;">
                                            <animate attributeName="stroke-dashoffset" from="0" to="88" dur="{{ env('SESSION_LIFETIME', 120) * 60 }}s" repeatCount="1" fill="freeze" />
                                        </circle>
                                    </svg>
                                    <i class="fas fa-user text-primary-300"></i>
                                </div>
                                <span class="hidden md:block text-sm font-medium">{{ Auth::check() ? Auth::user()->name : 'Visitante' }}</span>
                                <i class="fas fa-chevron-down text-xs hidden md:block"></i>
                            </button>
                            <!-- Dropdown Menu -->
                            <div class="absolute right-0 mt-2 w-48 bg-dark-200 rounded-lg shadow-lg border border-dark-300 hidden group-hover:block">
                                <div class="p-2">
                                    @if(Auth::check())
                                    <div class="px-4 py-2 text-sm text-accent-400">
                                        {{ Auth::user()->email }}
                                    </div>
                                    <hr class="border-dark-300 my-2">
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="w-full flex items-center gap-2 px-4 py-2 text-sm text-accent-600 hover:text-accent-400 hover:bg-dark-300 rounded-md">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>Sair</span>
                                        </button>
                                    </form>
                                    @else
                                    <a href="{{ route('admin.login') }}" class="w-full flex items-center gap-2 px-4 py-2 text-sm text-accent-600 hover:text-accent-400 hover:bg-dark-300 rounded-md">
                                        <i class="fas fa-sign-in-alt"></i>
                                        <span>Entrar</span>
                                    </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {{-- Page Content --}}
            <div class="flex-1 p-4 md:p-6 overflow-auto">
                @yield('content')
            </div>
        </main>
    </div>

    {{-- Overlay & Mobile Sidebar --}}
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden" aria-hidden="true"></div>

    <aside id="mobile-sidebar" class="fixed inset-y-0 left-0 w-64 bg-[#1e1e1e] shadow-lg hidden z-30 transform transition-transform duration-150 ease-in-out text-gray-100 overflow-y-auto">
        <div class="p-4 flex items-center justify-between border-b border-[#2c2c2c]">
            <div class="logo-container">
                <div class="logo-full font-bold text-2xl">
                    trends <span class="text-red-500">quiz</span>
                </div>
            </div>
            <button id="close-menu-button" class="text-accent-600 hover:text-accent-400 focus:outline-none">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="flex-1 overflow-y-auto py-4 px-2">
            @include('panel.admin.includes.admin_menu')
        </div>
    </aside>

    @stack('scripts')

    {{-- Componentes Extras Específicos por Página --}}
    @yield('extra_components', '')

    @yield('modals')

    
</body>
</html>
