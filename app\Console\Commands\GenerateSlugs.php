<?php

namespace App\Console\Commands;

use App\Models\PlgModule;
use App\Models\PlgTest;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for modules and tests that don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating slugs for modules...');
        
        // Gerar slugs para módulos
        $modules = PlgModule::whereNull('slug')->orWhere('slug', '')->get();
        $moduleCount = 0;
        
        foreach ($modules as $module) {
            $slug = Str::slug($module->title);
            
            // Verificar se o slug já existe
            $originalSlug = $slug;
            $counter = 1;
            while (PlgModule::where('slug', $slug)->where('id', '!=', $module->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            
            $module->slug = $slug;
            $module->save();
            $moduleCount++;
            
            $this->line("Module: {$module->title} -> {$slug}");
        }
        
        $this->info("Generated slugs for {$moduleCount} modules.");
        
        $this->info('Generating slugs for tests...');
        
        // Gerar slugs para testes
        $tests = PlgTest::whereNull('slug')->orWhere('slug', '')->get();
        $testCount = 0;
        
        foreach ($tests as $test) {
            $slug = Str::slug($test->name);
            
            // Verificar se o slug já existe
            $originalSlug = $slug;
            $counter = 1;
            while (PlgTest::where('slug', $slug)->where('id', '!=', $test->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            
            $test->slug = $slug;
            $test->save();
            $testCount++;
            
            $this->line("Test: {$test->name} -> {$slug}");
        }
        
        $this->info("Generated slugs for {$testCount} tests.");
        $this->info('Done!');
        
        return 0;
    }
}
