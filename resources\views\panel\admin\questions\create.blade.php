@extends('layouts.panel')

@section('title', isset($question) ? 'Editar Questão' : 'Nova Questão')
@section('page_title', isset($question) ? 'Editar Questão' : 'Nova Questão')

@push('styles')
    @vite(['resources/css/questions.css', 'resources/css/quill-editor.css'])
@endpush

@section('content')
    <div class="animate-fade-in">
        @if ($errors->any())
            <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <strong class="font-medium">Atenção!</strong>
                <ul class="mt-3 list-disc list-inside text-sm">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form
            action="{{ isset($question) ? route('admin.questions.update', $question->id) : route('admin.questions.store') }}"
            method="POST" id="questionForm" enctype="multipart/form-data">
            @csrf
            @if (isset($question))
                @method('PUT')
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Coluna da esquerda - Editor de questão -->
                <div class="lg:col-span-2">
                    <div
                        class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg mb-6">
                        <div
                            class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
                            <div class="flex items-center gap-2">
                                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                                <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Informações da Questão
                                </h1>
                            </div>
                            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                                Preencha os dados para criar uma nova questão na plataforma.
                            </p>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Questão -->
                            <div>
                                <label for="question"
                                    class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Questão <span class="text-red-500">*</span>
                                </label>
                                <div id="question-editor" class="editor" data-input="question" data-placeholder="Digite o enunciado da questão..."></div>
                                <input type="hidden" name="question" id="question" value="{{ old('question', $question->question ?? '') }}" required>
                            </div>

                            <!-- Imagem da questão -->
                            <div class="media-selector">
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Imagem da Questão
                                        
                                    </label>
                                    <button type="button"
                                        class="open-media-modal inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors"
                                        data-target="question_image">
                                        <i class="fas fa-image"></i>
                                        <span>Selecionar Imagem</span>
                                    </button>
                                </div>

                                <input type="hidden" name="question_image" id="question_image" value="{{ old('question_image', isset($question) && $question->hasMedia('question_image') ? $question->firstMedia('question_image')->id : '') }}">
                                

                                <div class="media-preview" id="question_image-preview">
                                    @if (isset($question) && $question->hasMedia('question_image'))
                                        @php
                                            $media = $question->firstMedia('question_image');
                                            $imageUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=250&h=150&fit=crop&fm=webp';
                                        @endphp
                                        <div class="relative">
                                            <img src="{{ $imageUrl }}" alt="{{ $media->filename }}" title="{{ $media->filename }}" 
                                                class="w-full h-32 object-cover rounded-lg"
                                                onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'flex items-center justify-center w-full h-32 bg-zinc-100 rounded-lg\'><i class=\'fas fa-image text-red-400 text-2xl\'></i></div>';">
                                            <button type="button" class="preview-remove-btn absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600">
                                                <i class="fas fa-times text-xs"></i>
                                            </button>

                                           <input type="text" title="Legenda da Mídia" name="question_image_caption" class="border rounded px-1 py-0.5 mt-1 media-caption-input" placeholder="Legenda da Mídia..." value="{{ old('caption', $media->pivot->caption ?? '') }}" data-media-id="{{ $media->id }}">
                                    

                                        </div>
                                        <!-- <p class="text-sm text-zinc-500 mt-2 truncate">{{ $media->filename }}</p>-->
                                    @endif
                                </div>
                                <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendado: 400x300px</p>
                            </div>

                            <!-- Categorias e Tipo -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="space-y-2">
                                    <label for="categories_select"
                                        class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Categorias <span class="text-red-500">*</span>
                                    </label>
                                    <select name="category_id[]" id="categories_select" class="w-full categories-select"
                                        multiple="multiple" required>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ in_array($category->id, old('category_id', isset($question) ? $question->categories->pluck('id')->toArray() : [])) ? 'selected' : '' }}>
                                                {{ $category->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="space-y-2">
                                    <label for="question_type" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Tipo de alternativa <span class="text-red-500">*</span>
                                    </label>
                                    <select name="question_type" id="question_type"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        required>
                                        <option value="single"
                                            {{ old('question_type', isset($question) ? $question->question_type ?? 'single' : '') == 'single' ? 'selected' : '' }}>
                                            Escolha Única
                                        </option>
                                        <option value="multiple"
                                            {{ old('question_type', isset($question) ? $question->question_type ?? '' : '') == 'multiple' ? 'selected' : '' }}>
                                            Múltipla Escolha
                                        </option>
                                        <option value="essay"
                                            {{ old('question_type', isset($question) ? $question->question_type ?? '' : '') == 'essay' ? 'selected' : '' }}>
                                            Dissertativa
                                        </option>
                                    </select>
                                </div>

                                <div class="space-y-2">
                                    <label for="answer_format" class="text-sm text-zinc-500 dark:text-zinc-400">
                                        Formato:
                                    </label>
                                    <select id="answer_format" name="answer_format"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                                        <option value="numeric"
                                            {{ isset($question) && $question->answer_format === 'numeric' ? 'selected' : '' }}>
                                            1) 2) 3)</option>
                                        <option value="alpha"
                                            {{ isset($question) && $question->answer_format === 'alpha' ? 'selected' : '' }}>
                                            A) B) C)</option>
                                        <option value="roman"
                                            {{ isset($question) && $question->answer_format === 'roman' ? 'selected' : '' }}>
                                            i. ii. iii.</option>
                                        <option value="none"
                                            {{ isset($question) && $question->answer_format === 'none' ? 'selected' : '' }}>
                                            Sem numeração</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Alternativas -->
                            <div id="answers-section" class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <label class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Alternativas <span class="text-red-500">*</span>
                                    </label>
                                </div>

                                <!-- Campos hidden para preservar explicações -->
                                @if (isset($question) && $question->answers->count() > 0)
                                    @foreach ($question->answers->where('correct', 1) as $answer)
                                        <input type="hidden" class="explanation-hidden-value" data-answer="{{ $answer->answer_number }}" value="{{ $answer->explanation }}" />
                                        <input type="hidden" name="explanation" id="explanation-hidden"> <!--CAMPO HIDDEN QUE RECEBE VIA JAVASCRIPT O EXPLANATION -->
                                    @endforeach
                                @endif

                                <div id="answers-container" class="space-y-4">
                                    @if (isset($question) && $question->answers->count() > 0)
                                        @foreach ($question->answers->sortBy('answer_number') as $index => $answer)
                                            <div class="flex items-start gap-4 answer-item rounded-lg border bg-dark-200 p-3"
                                                data-answer-index="{{ $index }}">
                                                <div class="flex-shrink-0 pt-2">
                                                    <input type="radio" name="correct_answer"
                                                        value="{{ $answer->answer_number }}"
                                                        {{ old('correct_answer', $correctAnswerNumber ?? '') == $answer->answer_number ? 'checked' : '' }}
                                                        class="h-4 w-4 text-trends-primary border-zinc-300 focus:ring-trends-primary/30 answer-radio">
                                                    <input type="checkbox" name="correct_answers[]"
                                                        value="{{ $answer->answer_number }}"
                                                        {{ in_array($answer->answer_number, old('correct_answers', $correctAnswersArray ?? [])) ? 'checked' : '' }}
                                                        class="h-4 w-4 text-trends-primary border-zinc-300 rounded focus:ring-trends-primary/30 answer-checkbox hidden">
                                                </div>
                                                <div class="flex-grow">
                                                    <div class="mb-2">
                                                        <label
                                                            class="text-sm font-medium text-zinc-700 dark:text-zinc-300 answer-label">
                                                            Alternativa {{ $answer->answer_number }}
                                                        </label>
                                                    </div>
                                                    <div class="flex items-center gap-2">
                                                        <input type="text" name="answers[{{ $index }}][answer]"
                                                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 answer-text"
                                                            value="{{ old("answers.{$index}.answer", $answer->answer) }}">

                                                        <input type="hidden" name="answers[{{ $index }}][image]"
                                                            id="answer_image_{{ $answer->answer_number }}"
                                                            class="answer-image-input"
                                                            value="{{ old("answers.{$index}.image", $answer->hasMedia('answer_image') ? $answer->firstMedia('answer_image')->id : '') }}">
                                                        <input type="hidden" name="answers[{{ $index }}][caption]"
                                                            id="answer_caption_{{ $answer->answer_number }}"
                                                            class="answer-image-caption-input"
                                                            value="{{ old("answers.{$index}.caption", $answer->caption ?? '') }}">
                                                        <button type="button"
                                                            class="open-media-modal flex-shrink-0 p-2 bg-zinc-200 hover:bg-zinc-300 dark:bg-zinc-700 dark:hover:bg-zinc-600 text-zinc-700 dark:text-zinc-100 rounded-lg answer-image-button"
                                                            data-target="answer_image_{{ $answer->answer_number }}">
                                                            <i class="fas fa-image"></i>
                                                        </button>

                                                        @if ($answer->answer_number > 1)
                                                            <button type="button"
                                                                class="remove-answer flex-shrink-0 text-red-500 hover:text-red-700">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        @endif
                                                    </div>

                                                    <!-- Container para imagem e explicação -->
                                                    <div class="answer-content-container">
                                                        <!-- Campo de explicação (sempre hidden inicialmente, será mostrado via JavaScript) -->
                                                        <div class="explanation-field mt-2 ml-2 pl-4 border-l-2 border-trends-primary hidden">
                                                            <label class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1 block">
                                                                Explicação da alternativa
                                                            </label>
                                                                
                                                                                                                                
                                                                <div 
                                                                id="explanation-editor-{{ $answer->answer_number }}" 
                                                                class="editor explanation-textarea w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 mt-1" 
                                                                data-input="explanation-{{ $answer->answer_number }}" 
                                                                data-placeholder="Digite a explicação para esta alternativa...">
                                                                </div>

                                                                <textarea
                                                                id="explanation-{{ $answer->answer_number }}"
                                                                name="{{ isset($question) ? 'explanations[' . $answer->answer_number . ']' : '' }}"
                                                                data-answer="{{ $answer->answer_number }}" rows="6"
                                                                class="hidden explanation-textarea w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                                                placeholder="Digite uma explicação para esta alternativa...">{{ isset($question) && $question->question_type === 'multiple' ? old('explanations.' . $answer->answer_number, $answer->explanation) : old('explanation', $answer->explanation) }}</textarea>
                                                                

                                                        </div>

                                                        <!-- Container de imagem sempre depois da explicação -->
                                                        <div class="media-preview mt-2" id="answer_image_{{ $answer->answer_number }}-preview">


                                                        
                                                            @if ($answer->hasMedia('answer_image'))
                                                                @php
                                                                    $media = $answer->firstMedia('answer_image');
                                                                    $mediaType = $media->aggregate_type;
                                                                    $imageUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=200&h=150&fit=crop&fm=webp';
                                                                @endphp
                                                                
                                                                
                                                                @if ($mediaType === 'image')
                                                                    {{-- Mostrar thumbnail da imagem --}}
                                                                    <div class="relative">
                                                                        <img src="{{ route('media.serve', ['path' => $media->getDiskPath()]) }}?w=200&h=150&fit=crop&fm=webp"
                                                                            alt="Imagem da alternativa"
                                                                            class="w-full h-32 object-cover rounded-lg"
                                                                            onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'flex items-center justify-center w-full h-32 bg-zinc-100 rounded-lg\'><i class=\'fas fa-image text-red-400 text-2xl\'></i></div>';">

                                                                        <button type="button" class="preview-remove-btn absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600">
                                                                            <i class="fas fa-times text-xs"></i>
                                                                        </button>
                                                                    </div>

                                                                <input type="text" class="border rounded px-1 py-0.5 mt-1 media-caption-input" placeholder="Legenda da Mídia..." value="{{ old("answers.{$index}.caption", $media->pivot->caption ?? '') }}" data-media-id="{{ $media->id }}">

                                                                @elseif ($media->mime_type === 'application/pdf')
                                                                    {{-- PDF --}}
                                                                    <div class="bg-red-100 w-full h-32 flex items-center justify-center rounded-lg">
                                                                        <i class="fas fa-file-pdf text-red-500 text-3xl"></i>
                                                                    </div>

                                                                @elseif (Str::startsWith($media->mime_type, 'audio/'))
                                                                    {{-- MP3 ou outros áudios --}}
                                                                    <div class="bg-blue-100 w-full h-32 flex items-center justify-center rounded-lg">
                                                                        <i class="fas fa-music text-blue-500 text-3xl"></i>
                                                                    </div>

                                                                @elseif (Str::startsWith($media->mime_type, 'video/'))
                                                                    {{-- MP4 ou outros vídeos --}}
                                                                    <div class="bg-purple-100  w-full h-32 flex items-center justify-center rounded-lg">
                                                                        <i class="fas fa-film text-purple-500 text-3xl"></i>
                                                                    </div>

                                                                @else
                                                                    {{-- Outro formato não identificado --}}
                                                                    <div class="w-full h-32 flex items-center justify-center bg-zinc-100 rounded-lg">
                                                                        <i class="fas fa-file text-gray-400 text-3xl"></i>
                                                                    </div>
                                                                @endif

                                                                <!--<p class="text-sm text-zinc-500 mt-2 truncate">{{ $media->filename }}</p> -->
                                                            @endif



                                                        </div>



                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        {{-- Primeira alternativa --}}
                                        <div class="flex items-start gap-4 answer-item rounded-lg border bg-dark-200 p-3"
                                            data-answer-index="0">
                                            <div class="flex-shrink-0 pt-2">
                                                <input type="radio" name="correct_answer" value="1" checked
                                                    class="h-4 w-4 text-trends-primary border-zinc-300 focus:ring-trends-primary/30 answer-radio">
                                                <input type="checkbox" name="correct_answers[]" value="1"
                                                    {{ in_array(1, old('correct_answers', [])) ? 'checked' : '' }}
                                                    class="h-4 w-4 text-trends-primary border-zinc-300 rounded focus:ring-trends-primary/30 answer-checkbox hidden">
                                            </div>
                                            <div class="flex-grow">
                                                <div class="mb-2">
                                                    <label
                                                        class="text-sm font-medium text-zinc-700 dark:text-zinc-300 answer-label">
                                                        Alternativa 1
                                                    </label>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <input type="text" name="answers[0][answer]"
                                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 answer-text"
                                                        value="{{ old('answers.0.answer') }}">

                                                    <input type="hidden" name="answers[0][image]" id="answer_image_1"
                                                        class="answer-image-input" value="{{ old('answers.0.image') }}">
                                                    <input type="hidden" name="answers[0][caption]" id="answer_caption_1"
                                                        class="answer-image-caption-input" value="{{ old('answers.0.caption') }}">
                                                    <button type="button"
                                                        class="open-media-modal flex-shrink-0 p-2 bg-zinc-200 hover:bg-zinc-300 dark:bg-zinc-700 dark:hover:bg-zinc-600 text-zinc-700 dark:text-zinc-100 rounded-lg answer-image-button"
                                                        data-target="answer_image_1">
                                                        <i class="fas fa-image"></i>
                                                    </button>
                                                </div>

                                                <!-- Container para imagem e explicação -->
                                                <div class="answer-content-container">
                                                    <div
                                                        class="explanation-field mt-2 ml-2 pl-4 border-l-2 border-trends-primary hidden">
                                                        <label
                                                            class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                                            Explicação da alternativa correta
                                                        </label>
                                                        
                                                        

                                                        <div id="explanation-editor-1" class="editor explanation-textarea w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 mt-1"
                                                              data-input="explanation-1" data-answer="1" data-placeholder="Digit a explicação da resposta correta ..."></div>

                                                        <textarea name="explanations[1]" id="explanation-1" data-answer="1" rows="6" class="hidden explanation-textarea w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 mt-1">{{ old('explanation') }}</textarea>
                                                        
                                                                  


                                                    </div>

                                                    <!-- Container de imagem sempre depois da explicação -->
                                                    <div class="media-preview mt-2" id="answer_image_1-preview">
                                                        @if (old('answers.0.image'))
                                                            <div class="relative">
                                                                <img src="{{ old('answers.0.image') }}"
                                                                    alt="Imagem da alternativa"
                                                                    class="w-full h-32 object-cover rounded-lg"
                                                                    onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'flex items-center justify-center w-full h-32 bg-zinc-100 rounded-lg\'><i class=\'fas fa-image text-red-400 text-2xl\'></i></div>';">
                                                                <button type="button" class="preview-remove-btn absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600">
                                                                    <i class="fas fa-times text-xs"></i>
                                                                </button>
                                                            </div>
                                                            <p class="text-sm text-zinc-500 mt-2 truncate">
                                                                {{ basename(old('answers.0.image')) }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <button type="button" id="add-answer"
                                    class="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                                    <i class="fas fa-plus"></i>
                                    <span>Adicionar Alternativa</span>
                                </button>
                            </div>

                            <!-- Resposta Dissertativa -->
                            <div id="essay-answer-section" class="space-y-2" style="display: none;">
                                <label for="essay_answer" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                    Resposta Esperada <span class="text-red-500">*</span>
                                </label>
                                <textarea name="essay_answer" id="essay_answer" rows="6"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200">{{ old('essay_answer', $question->essay_answer ?? '') }}</textarea>
                            </div>

                            <!-- Configurações -->
                            <div class="flex items-center gap-4">
                                <label class="inline-flex items-center cursor-pointer relative">
                                    <input type="checkbox" name="free" value="1" class="sr-only peer"
                                        {{ old('free', isset($question) ? $question->free : false) ? 'checked' : '' }}>
                                    <div
                                        class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary">
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Questão Gratuita</span>
                                </label>
                            </div>
                        </div>

                        <div
                            class="px-6 py-4 bg-zinc-50 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800">
                            <div class="flex justify-end gap-4">
                                <a href="{{ route('admin.questions.index') }}"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                                    <i class="fas fa-times"></i>
                                    <span>Cancelar</span>
                                </a>
                                <button type="submit" id="submit-button"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                                    <i class="fas fa-save"></i>
                                    <span>{{ isset($question) ? 'Atualizar' : 'Salvar' }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Coluna da direita - Preview da questão -->
                @include('panel.admin.questions.partials.preview')
            </div>
        </form>
    </div>
@endsection


<!-- Template para nova alternativa -->
<template id="answer-template">
    <div class="flex items-start gap-4 answer-item rounded-lg border bg-dark-200 p-3" data-answer-index="">
        <div class="flex-shrink-0 pt-2">
            <input type="radio" name="correct_answer" value=""
                class="h-4 w-4 text-trends-primary border-zinc-300 focus:ring-trends-primary/30 answer-radio">
            <input type="checkbox" name="correct_answers[]" value=""
                class="h-4 w-4 text-trends-primary border-zinc-300 rounded focus:ring-trends-primary/30 answer-checkbox hidden">
        </div>
        <div class="flex-grow">
            <div class="mb-2">
                <label class="text-sm font-medium text-zinc-700 dark:text-zinc-300 answer-label">
                    Alternativa
                </label>
            </div>
            <div class="flex items-center gap-2">
                <input type="text" name=""
                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 answer-text">

                <input type="hidden" name="" class="answer-image-input">
                <input type="hidden" name="" class="answer-image-caption-input">
                <button type="button"
                    class="open-media-modal flex-shrink-0 p-2 bg-zinc-200 hover:bg-zinc-300 dark:bg-zinc-700 dark:hover:bg-zinc-600 text-zinc-700 dark:text-zinc-100 rounded-lg answer-image-button"
                    data-target="">
                    <i class="fas fa-image"></i>
                </button>

                <button type="button" class="remove-answer flex-shrink-0 text-red-500 hover:text-red-700">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="answer-content-container">
                <!-- Campo de Explicação (aparece automaticamente quando checkbox/radio é marcado) -->
                <!-- Campo de explicação será criado dinamicamente via JavaScript quando necessário -->
                <div class="explanation-field-placeholder"></div>
                
                <!-- Preview da imagem -->
                <div class="media-preview mt-2">
                    <!-- O preview da imagem será inserido aqui via JavaScript -->
                </div>
            </div>


            
        </div>
    </div>
</template>

@push('scripts')




    <!-- Script para Choices.js -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Aguardar o Choices estar disponível
            function initCategoryChoices() {
                if (typeof window.Choices === 'undefined') {
                    setTimeout(initCategoryChoices, 100);
                    return;
                }

                // Inicializar Choices.js
                const categorySelects = document.querySelectorAll('.categories-select');
                categorySelects.forEach(function(select) {
                    if (!select.choicesInstance) {
                        const placeholder = select.getAttribute('data-placeholder') || 'Selecione uma categoria';
                        const choices = new window.Choices(select, {
                            searchEnabled: true,
                            searchChoices: true,
                            searchPlaceholderValue: 'Digite para buscar...',
                            noResultsText: 'Nenhum resultado encontrado',
                            noChoicesText: 'Nenhuma opção disponível',
                            itemSelectText: 'Clique para selecionar',
                            placeholder: true,
                            placeholderValue: placeholder,
                            removeItemButton: false,
                            shouldSort: false,
                            allowHTML: true
                        });
                        select.choicesInstance = choices;
                    }
                });
            }

            initCategoryChoices();

            // GARANTIR que explicações de respostas corretas sejam mostradas na edição
            setTimeout(function() {
                @php
                    $correctAnswers = [];
                    foreach (($question->answers ?? []) as $answer) {
                        if ($answer->correct && $answer->explanation) {
                            $correctAnswers[] = $answer->answer_number;
                        }
                    }
                @endphp
                let correctAnswers = @json($correctAnswers);
                correctAnswers.forEach(function(answerNumber) {
                    let correctInput = document.querySelector('input[name="correct_answer"][value="' + answerNumber + '"], input[name="correct_answers[]"][value="' + answerNumber + '"]');
                            if (correctInput) {
                                correctInput.checked = true;
                                // Encontrar e mostrar o campo de explicação
                                const answerItem = correctInput.closest('.answer-item');
                                if (answerItem) {
                                    const explanationField = answerItem.querySelector('.explanation-field');
                                    if (explanationField) {
                                        explanationField.classList.remove('hidden');
                                console.log('Explicação mostrada para resposta', answerNumber);
                                    }
                                }
                            }
                });
            }, 200);
        });
    </script>

    @vite(['resources/js/quill-editor.js', 'resources/js/questions.js'])
@endpush

