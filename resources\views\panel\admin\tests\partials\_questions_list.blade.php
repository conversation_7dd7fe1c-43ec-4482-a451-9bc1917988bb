@if($questions->isEmpty())
    <div class="p-4 text-center text-zinc-500 dark:text-zinc-400">
        <p><PERSON><PERSON><PERSON><PERSON> questão disponível.</p>
    </div>
@else
    @foreach($questions as $question)
        <div class="question-item p-3 border border-zinc-200 dark:border-zinc-700 rounded-lg hover:bg-gray-50 dark:hover:bg-zinc-800/50 transition flex items-center justify-between gap-4 mb-2"
            data-id="{{ $question->id }}"
            data-question="{{ strip_tags($question->question) }}"
            data-categories="{{ $question->categories->pluck('id')->implode(',') }}"
            data-type="{{ $question->question_type }}"
            data-created="{{ $question->created_at }}">
            
            <div class="flex items-center gap-3 flex-grow">
                <div class="flex-shrink-0 select-check">
                    <i class="fas fa-check-circle text-trends-primary text-lg"></i>
                </div>
                <div class="flex flex-col flex-grow">
                    <div class="question-text text-sm font-medium text-zinc-900 dark:text-zinc-100">
                        {!! strip_tags(Str::limit($question->question, 80)) !!}
                    </div>
                    <div class="flex gap-2 mt-1 flex-wrap">
                        <span class="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-0.5 rounded">
                            {{ ucfirst($question->question_type) }}
                        </span>
                        
                        @if($question->free)
                            <span class="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-0.5 rounded">
                                Gratuita
                            </span>
                        @else
                            <span class="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 px-2 py-0.5 rounded">
                                Paga
                            </span>
                        @endif
                        
                        @if($question->categories->count() > 0)
                            <span class="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-0.5 rounded">
                                {{ $question->categories->first()->title }}
                            </span>
                        @endif
                    </div>
                </div>
            </div>
            
            <button type="button" class="add-question-btn h-8 w-8 bg-trends-primary text-white rounded-full hover:bg-trends-primary/90 flex items-center justify-center flex-shrink-0">
                <i class="fas fa-plus"></i>
            </button>
        </div>
    @endforeach
@endif 