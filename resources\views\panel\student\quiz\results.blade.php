<x-layouts.quiz :title="'Resultados - ' . $attempt->test->name">
    <!-- Breadcrumb -->
    <x-student.breadcrumb
        :course="$attempt->test->module->course"
        :module="$attempt->test->module"
        :test="$attempt->test" />

    <div class="max-w-6xl mx-auto px-4">
        <!-- Cabeçalho dos Resultados -->
        <div class="quiz-container p-6 mb-6 fade-in">
            <div class="text-center mb-6">
                <div class="mb-4">
                    @if($attempt->passed)
                        <i class="fas fa-trophy text-6xl text-yellow-500 mb-4"></i>
                        <h1 class="text-3xl font-bold text-green-800 mb-2">Parabéns! Você foi aprovado!</h1>
                    @else
                        <i class="fas fa-times-circle text-6xl text-red-500 mb-4"></i>
                        <h1 class="text-3xl font-bold text-red-800 mb-2">Não foi desta vez...</h1>
                    @endif
                </div>
                
                <h2 class="text-xl font-semibold text-gray-800 mb-2">{{ $attempt->test->name }}</h2>
                <p class="text-gray-600">{{ $attempt->test->module->course->title }} - {{ $attempt->test->module->title }}</p>
            </div>

            <!-- Estatísticas Principais -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg p-6 text-center shadow-md">
                    <div class="text-3xl font-bold {{ $attempt->passed ? 'text-green-600' : 'text-red-600' }} mb-2">
                        {{ round($attempt->score) }}%
                    </div>
                    <div class="text-gray-600">Nota Final</div>
                </div>
                
                <div class="bg-white rounded-lg p-6 text-center shadow-md">
                    <div class="text-3xl font-bold text-green-600 mb-2">{{ $attempt->correct_answers }}</div>
                    <div class="text-gray-600">Respostas Corretas</div>
                </div>
                
                <div class="bg-white rounded-lg p-6 text-center shadow-md">
                    <div class="text-3xl font-bold text-red-600 mb-2">{{ $attempt->total_questions - $attempt->correct_answers }}</div>
                    <div class="text-gray-600">Respostas Incorretas</div>
                </div>
                
                <div class="bg-white rounded-lg p-6 text-center shadow-md">
                    <div class="text-3xl font-bold text-blue-600 mb-2">{{ $attempt->test->passing_score }}%</div>
                    <div class="text-gray-600">Nota Mínima</div>
                </div>
            </div>

            <!-- Status da Aprovação -->
            <div class="mb-6">
                @if($attempt->passed)
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex items-center justify-center space-x-3 text-green-700">
                            <i class="fas fa-check-circle text-2xl"></i>
                            <div>
                                <h3 class="text-lg font-semibold">Aprovado!</h3>
                                <p class="text-sm">Você atingiu a nota mínima necessária para aprovação.</p>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center justify-center space-x-3 text-red-700">
                            <i class="fas fa-times-circle text-2xl"></i>
                            <div>
                                <h3 class="text-lg font-semibold">Não Aprovado</h3>
                                <p class="text-sm">
                                    Você precisava de {{ $attempt->test->passing_score }}% para ser aprovado.
                                    @if($attempt->test->isFinalTest())
                                        Você poderá tentar novamente após {{ $attempt->test->cooldown_hours }} horas.
                                    @else
                                        Você pode tentar novamente quando quiser.
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Informações da Tentativa -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center text-sm">
                    <div>
                        <div class="font-semibold text-gray-800">Iniciado em:</div>
                        <div class="text-gray-600">{{ $attempt->started_at ? $attempt->started_at->format('d/m/Y H:i') : 'N/A' }}</div>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-800">Finalizado em:</div>
                        <div class="text-gray-600">{{ $attempt->completed_at ? $attempt->completed_at->format('d/m/Y H:i') : 'Em andamento' }}</div>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-800">Tempo Total:</div>
                        <div class="text-gray-600">
                            @if($attempt->completed_at && $attempt->started_at)
                                {{ $attempt->completed_at->diffForHumans($attempt->started_at, true) }}
                            @else
                                Em andamento
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revisão das Questões -->
        <div class="quiz-container p-6 mb-6">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">Revisão das Questões</h3>
            
            <div class="space-y-6">
                @foreach($answers as $index => $answer)
                    <div class="bg-white rounded-lg p-6 shadow-md">
                        <!-- Cabeçalho da Questão -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <h4 class="text-lg font-semibold text-gray-800">
                                    Questão {{ $index + 1 }}
                                </h4>
                                @if($answer->question_type === 'multiple')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-check-square mr-1"></i>
                                        Múltipla escolha
                                    </span>
                                @endif
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($answer->is_correct)
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                        <i class="fas fa-check mr-1"></i>Correta
                                    </span>
                                @else
                                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                                        <i class="fas fa-times mr-1"></i>Incorreta
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Texto da Questão -->
                        <div class="mb-4">
                            <p class="text-gray-800 mb-2">{{ $answer->question->question }}</p>
                            @if($answer->question->image)
                                <img src="{{ $answer->question->image }}" alt="Imagem da questão" class="max-w-md h-auto rounded-lg shadow-sm">
                            @endif
                        </div>

                        <!-- Alternativas -->
                        <div class="space-y-3">
                            @foreach($answer->question->answers as $option)
                                @php
                                    // Verificar se está selecionada (single ou multiple)
                                    if ($answer->question_type === 'multiple') {
                                        $isSelected = $answer->selectedAnswers->contains('id', $option->id);
                                    } else {
                                        $isSelected = $answer->selectedAnswer && $answer->selectedAnswer->id === $option->id;
                                    }
                                    $isCorrect = $option->correct;
                                @endphp
                                
                                <div class="p-3 rounded-lg border-2 
                                    @if($isSelected && $isCorrect) border-green-500 bg-green-50
                                    @elseif($isSelected && !$isCorrect) border-red-500 bg-red-50
                                    @elseif(!$isSelected && $isCorrect) border-green-500 bg-green-50
                                    @else border-gray-200 bg-gray-50
                                    @endif
                                ">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            @if($isSelected && $isCorrect)
                                                <i class="fas fa-check-circle text-green-600"></i>
                                            @elseif($isSelected && !$isCorrect)
                                                <i class="fas fa-times-circle text-red-600"></i>
                                            @elseif(!$isSelected && $isCorrect)
                                                <i class="fas fa-check-circle text-green-600"></i>
                                            @else
                                                <i class="far fa-circle text-gray-400"></i>
                                            @endif
                                        </div>
                                        
                                        <div class="flex-1">
                                            <p class="
                                                @if($isSelected && $isCorrect) text-green-800
                                                @elseif($isSelected && !$isCorrect) text-red-800
                                                @elseif(!$isSelected && $isCorrect) text-green-800
                                                @else text-gray-700
                                                @endif
                                            ">
                                                {{ $option->answer }}
                                                @if($isSelected)
                                                    <span class="ml-2 text-sm font-medium">(Sua resposta)</span>
                                                @endif
                                                @if($isCorrect && !$isSelected)
                                                    <span class="ml-2 text-sm font-medium">(Resposta correta)</span>
                                                @endif
                                            </p>
                                            
                                            @if($option->image)
                                                <img src="{{ $option->image }}" alt="Imagem da alternativa" class="mt-2 max-w-xs h-auto rounded">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Explicações -->
                        @php
                            $correctAnswers = $answer->question->answers->where('correct', true);
                            $explanations = $correctAnswers->filter(function($answer) {
                                return !empty($answer->explanation);
                            });
                        @endphp

                        @if($explanations->count() > 0)
                            <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <h5 class="font-semibold text-blue-800 mb-2">
                                    {{ $explanations->count() > 1 ? 'Explicações:' : 'Explicação:' }}
                                </h5>
                                @foreach($explanations as $explAnswer)
                                    <div class="text-blue-700 text-sm {{ !$loop->last ? 'mb-3' : '' }}">
                                        @if($explanations->count() > 1)
                                            <strong>{{ $explAnswer->answer }}:</strong><br>
                                        @endif
                                        {{ $explAnswer->explanation }}
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Botões de Ação -->
        <div class="quiz-container p-6 text-center">
            <div class="space-y-4">
                @if(!$attempt->passed && $attempt->test->isQuiz())
                    <a href="{{ route('student.quiz.start', [$attempt->test->module->course->slug, $attempt->test->module->slug, $attempt->test->slug]) }}"
                       class="btn-primary inline-block">
                        <i class="fas fa-redo mr-2"></i>
                        Tentar Novamente
                    </a>
                @elseif(!$attempt->passed && $attempt->test->isFinalTest())
                    @if($student->canTakeTest($attempt->test))
                        <a href="{{ route('student.test.start', [$attempt->test->module->course->slug, $attempt->test->module->slug, $attempt->test->slug]) }}" 
                           class="btn-primary inline-block">
                            <i class="fas fa-redo mr-2"></i>
                            Tentar Novamente
                        </a>
                    @else
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                            <p class="text-yellow-800">
                                <i class="fas fa-clock mr-2"></i>
                                Você poderá tentar novamente em: {{ $student->getTestCooldownRemaining($attempt->test) }}
                            </p>
                        </div>
                    @endif
                @endif
                
                <div>
                    <a href="{{ route('student.show', $attempt->test->module->course->slug) }}" 
                       class="btn-secondary inline-block">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar ao Curso
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-layouts.quiz>
