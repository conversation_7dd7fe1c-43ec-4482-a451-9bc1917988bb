@props(['title', 'courses'])

<div class="mb-8">
    <h2 class="text-xl font-semibold mb-4 text-zinc-900 dark:text-zinc-100">{{ $title }}</h2>

    <div class="swiper-carousel swiper group">
        <div class="swiper-wrapper">
            @foreach($courses as $course)
                <div class="swiper-slide">
                    <x-course-card :course="$course" />
                </div>
            @endforeach
        </div>

        <!-- Botões de navegação -->
        <div class="swiper-button-next absolute top-1/2 -translate-y-1/2 right-2 z-10 w-10 h-10 bg-black/70 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg cursor-pointer">
            <i class="fas fa-chevron-right text-sm"></i>
        </div>
        <div class="swiper-button-prev absolute top-1/2 -translate-y-1/2 left-2 z-10 w-10 h-10 bg-black/70 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg cursor-pointer">
            <i class="fas fa-chevron-left text-sm"></i>
        </div>
    </div>
</div>