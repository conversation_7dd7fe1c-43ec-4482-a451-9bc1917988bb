@props(['mobile' => false])

@php
$links = [
    [
        'route' => 'student.dashboard',
        'icon' => 'fas fa-home',
        'label' => 'Início',
        'active' => request()->routeIs('student.dashboard')
    ],
    [
        'route' => 'student.courses',
        'icon' => 'fas fa-book',
        'label' => 'Especialidades',
        'active' => request()->routeIs('student.courses*')
    ],
    [
        'route' => 'student.quizzes',
        'icon' => 'fas fa-question-circle',
        'label' => 'Testes',
        'active' => request()->routeIs('student.quizzes*')
    ]
];
@endphp

@if($mobile)
    <!-- Mobile Navigation Links -->
    @foreach($links as $link)
        <a href="{{ route($link['route']) }}" 
           class="block pl-3 pr-4 py-2 border-l-4 {{ $link['active'] ? 'border-red-500 text-red-400 bg-gray-800' : 'border-transparent text-gray-300 hover:bg-gray-800' }} text-base font-medium">
            <i class="{{ $link['icon'] }} mr-2"></i>{{ $link['label'] }}
        </a>
    @endforeach
@else
    <!-- Desktop Navigation Links -->
    @foreach($links as $link)
        <a href="{{ route($link['route']) }}"
           class="inline-flex items-center px-1 pt-1 border-b-2 {{ $link['active'] ? 'border-red-500 text-white' : 'border-transparent text-gray-200 hover:text-white hover:border-gray-400' }} text-sm font-medium leading-5 transition duration-150 ease-in-out">
            <i class="{{ $link['icon'] }} mr-1"></i>
            {{ $link['label'] }}
        </a>
    @endforeach
@endif
