import { MediaManagerStandalone } from './MediaManagerStandalone.js';
import { MediaManagerModal } from './MediaManagerModal.js';
import { MediaRenderer } from './MediaRenderer.js';
import { SidebarManager } from './SidebarManager.js';
import { notify } from '../notifications.js';

export class MediaManager {
    // Instâncias globais
    static standaloneInstance = null;
    static activeModalInstance = null;
    static isModalOpening = false;
    static initialized = false;

    // ==================== MÉTODOS ESTÁTICOS DE INICIALIZAÇÃO ====================

    /**
     * Inicializa automaticamente todos os MediaManagers na página
     */
    static init() {
        if (MediaManager.initialized) return;
        
        // Aguardar DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => MediaManager.init());
            return;
        }
        
        MediaManager.initialized = true;
        MediaManager.initSidebarSystem();
        MediaManager.initStandalone();
        MediaManager.initModalButtons();
    }

    /**
     * Inicializa o sistema global de sidebar
     */
    static initSidebarSystem() {
        // Configurar event delegation global para sidebars
        SidebarManager.setupGlobalDelegation();
        
        // Configurar observer para detectar modais AJAX
        SidebarManager.setupDOMObserver();
    }

    /**
     * Inicializa o gerenciador standalone
     */
    static initStandalone() {
        // Proteção contra inicialização duplicada
        if (MediaManager.standaloneInstance) {
            return;
        }
        
        const standaloneGrid = document.getElementById('mediaGrid');
        if (!standaloneGrid || standaloneGrid.dataset.standalone !== 'true') {
            return;
        }
        
        try {
            MediaManager.standaloneInstance = new MediaManagerStandalone(standaloneGrid, {
                multiple: true,
                onSelectionChange: (selectedItems) => {
                    MediaManager.updateGlobalIndicators(selectedItems);
                }
            });
        } catch (error) {
            console.error('Erro ao inicializar MediaManager:', error);
            notify.error('Erro ao carregar gerenciador de mídia');
        }
    }

    /**
     * Inicializa botões de modal
     */
    static initModalButtons() {
        // Verificar se já foi inicializado
        if (document.mediaButtonsInitialized) return;
        document.mediaButtonsInitialized = true;

        // Event delegation para todos os botões de mídia
        document.addEventListener('click', (event) => {
            const button = event.target.closest('.open-media-modal, [data-media-selector]');
            if (!button) return;
            
            // Prevenir múltiplos cliques
            if (MediaManager.isModalOpening || MediaManager.activeModalInstance) {
                event.preventDefault();
                return;
            }
            
            event.preventDefault();
            
            const config = {
                target: button.dataset.target,
                multiple: button.dataset.multiple === 'true',
                preview: button.dataset.preview,
                accept: button.dataset.accept || 'all'
            };
            
            // Detectar target automaticamente se não especificado
            if (!config.target) {
                config.target = MediaManager.detectTargetFromButton(button);
            }
            
            // Armazenar referência do botão para facilitar busca do campo
            config.sourceButton = button;
            
            MediaManager.openSelector(config);
        });
    }

    /**
     * Detecta target automaticamente baseado no botão
     */
    static detectTargetFromButton(button) {
        // Verificar data-target primeiro
        if (button.dataset.target) {
            return button.dataset.target;
        }
        
        // CORREÇÃO: Verificar data-input-id (usado no formulário de questões)
        if (button.dataset.inputId) {
            return button.dataset.inputId;
        }
        
        // Tentar encontrar campo relacionado para usar como target
        const form = button.closest('form');
        const container = button.closest('.space-y-2, .form-group, .field, .input-group') || form;
        
        if (container) {
            // CORREÇÃO: Excluir campos de sistema (_token, _method) da busca
            const hiddenInput = container.querySelector('input[type="hidden"][name]:not([name="_token"]):not([name="_method"]), input[type="hidden"][id]:not([id="_token"]):not([id="_method"])');
            if (hiddenInput) {
                return hiddenInput.name || hiddenInput.id;
            }
        }
        
        // Verificar se é múltiplo ou pelo nome do campo
        const multiple = button.dataset.multiple === 'true';
        const buttonText = button.textContent.toLowerCase();
        const buttonId = button.id?.toLowerCase() || '';
        
        // Palavras-chave que indicam galeria
        const galleryKeywords = ['galeria', 'gallery', 'multiple', 'multiplos', 'varios'];
        const thumbKeywords = ['thumb', 'thumbnail', 'capa', 'cover', 'avatar', 'logo'];
        
        if (multiple || galleryKeywords.some(keyword => 
            buttonText.includes(keyword) || buttonId.includes(keyword))) {
            return 'gallery';
        }
        
        if (thumbKeywords.some(keyword => 
            buttonText.includes(keyword) || buttonId.includes(keyword))) {
            return 'thumbnail'; // Usar 'thumbnail' como padrão em vez de 'thumb'
        }
        
        // Default baseado em multiple
        return multiple ? 'gallery' : 'thumbnail';
    }

    /**
     * Abre seletor de mídia em modal
     */
    static async openSelector(config) {
        // Verificar se já há um modal aberto
        if (MediaManager.isModalOpening || MediaManager.activeModalInstance) {
            return;
        }

        try {
            MediaManager.isModalOpening = true;
            
            // Detectar target se não especificado
            const targetType = config.target || (config.multiple ? 'gallery' : 'thumb');

            // NOVO: Buscar IDs já existentes no input hidden se for galeria
            let preSelectedIds = [];
            if (targetType === 'gallery') {
                let input = document.getElementById(config.target);
                if (!input) {
                    // Tentar encontrar pelo botão
                    const button = config.sourceButton || document.querySelector(`[data-target="${config.target}"]`);
                    if (button) {
                        const container = button.closest('.space-y-2, .form-group, .field, .input-group') || button.closest('form');
                        if (container) {
                            input = container.querySelector(`input[name="${config.target}"]`) || container.querySelector(`#${config.target}`);
                        }
                    }
                }
                if (input && input.value) {
                    try {
                        preSelectedIds = JSON.parse(input.value);
                    } catch (e) { preSelectedIds = []; }
                }
            }

            MediaManager.activeModalInstance = new MediaManagerModal({
                target: targetType,
                multiple: config.multiple,
                preSelectedIds: preSelectedIds,
                onSelect: (media) => {
                    MediaManager.handleSelection(media, config);
                    MediaManager.closeModal();
                },
                onClose: () => {
                    MediaManager.closeModal();
                }
            });
            
            await MediaManager.activeModalInstance.open();
            
        } catch (error) {
            console.error('Erro ao abrir seletor:', error);
            notify.error('Erro ao abrir seletor de mídia');
            MediaManager.closeModal();
        } finally {
            MediaManager.isModalOpening = false;
        }
    }

    /**
     * Fecha modal ativo
     */
    static closeModal() {
        if (MediaManager.activeModalInstance) {
            MediaManager.activeModalInstance.destroy();
            MediaManager.activeModalInstance = null;
        }
        MediaManager.isModalOpening = false;
    }

    /**
     * Manipula seleção de mídia
     */
    static handleSelection(media, config) {
        if (!config.target) return;

        // Primeiro tentar encontrar o campo pelo data-target
        let targetInput = document.getElementById(config.target);
        
        // Se não encontrar, tentar encontrar pelo botão que foi clicado
        if (!targetInput) {
            const button = config.sourceButton || document.querySelector(`[data-target="${config.target}"]`);
            if (button) {
                // Procurar input relacionado próximo ao botão
                const form = button.closest('form');
                const container = button.closest('.space-y-2, .form-group, .field, .input-group') || form;
                
                if (container) {
                    // Buscar por padrões comuns de nomes
                    const targetMappings = {
                        'thumb': ['thumbnail', 'thumb', 'image'],
                        'thumbnail': ['thumbnail', 'thumb', 'image'],
                        'gallery': ['gallery', 'images', 'media'],
                        'cover': ['cover', 'capa'],
                        'avatar': ['avatar', 'profile_image'],
                        'logo': ['logo']
                    };
                    
                    const possibleNames = targetMappings[config.target] || [config.target];
                    
                    for (const name of possibleNames) {
                        // CORREÇÃO: Excluir campos de sistema (_token, _method) da busca
                        targetInput = container.querySelector(`input[name="${name}"]:not([name="_token"]):not([name="_method"]), #${name}:not([id="_token"]):not([id="_method"])`);
                        if (targetInput) break;
                    }
                }
            }
        }

        if (!targetInput) {
            console.error('MediaManager - Campo não encontrado:', {
                target: config.target,
                button: config.sourceButton,
                searchedSelectors: [
                    `#${config.target}`,
                    `input[name="${config.target}"]`,
                    '[data-target] input relacionado'
                ]
            });
            notify.error(`Campo para "${config.target}" não encontrado`);
            return;
        }

        console.log('Campo encontrado:', {
            target: config.target,
            input: targetInput,
            inputId: targetInput.id,
            inputName: targetInput.name
        });

        try {
            // Atualizar input baseado no target
            const isGalleryTarget = MediaRenderer.isMultipleTarget(config.target);
            
            if (isGalleryTarget) {
                // Para galeria, sempre usar array JSON
                const mediaArray = Array.isArray(media) ? media : [media];
                const mediaIds = mediaArray.map(m => m.id);
                
                // Se o campo já tem valores, adicionar aos existentes
                let existingIds = [];
                try {
                    existingIds = JSON.parse(targetInput.value || '[]');
                } catch (e) {
                    existingIds = [];
                }
                
                // Adicionar novos IDs sem duplicatas
                mediaIds.forEach(id => {
                    if (!existingIds.includes(id)) {
                        existingIds.push(id);
                    }
                });
                
                console.log('MediaManager - Atualizando galeria:', {
                    target: config.target,
                    newMediaIds: mediaIds,
                    existingIds: JSON.parse(targetInput.value || '[]'),
                    finalIds: existingIds,
                    mediaArray: mediaArray.length,
                    allMediaSelected: mediaArray,
                    inputValueBefore: targetInput.value,
                    inputValueAfter: JSON.stringify(existingIds)
                });
                
                targetInput.value = JSON.stringify(existingIds);
                
                // Disparar evento personalizado com TODAS as mídias selecionadas
                targetInput.dispatchEvent(new CustomEvent('mediaSelected', {
                    detail: { media: mediaArray, config }
                }));
            } else {
                // Para thumbnail/single, usar apenas um ID
                const mediaId = Array.isArray(media) ? media[0].id : media.id;
                targetInput.value = mediaId;

                console.log('ID da mídia atribuído ao campo:', mediaId, 'Campo:', targetInput);
                
                // Disparar evento personalizado
                const eventDetail = { media: Array.isArray(media) ? media[0] : media, config, target: config.target };
                
                targetInput.dispatchEvent(new CustomEvent('mediaSelected', {
                    detail: eventDetail
                }));
                
                // Também disparar no document para garantir que seja capturado
                document.dispatchEvent(new CustomEvent('mediaSelected', {
                    detail: eventDetail
                }));
                
                // E no window como fallback
                window.dispatchEvent(new CustomEvent('mediaSelected', {
                    detail: eventDetail
                }));
                
                console.log('MediaManager - Evento mediaSelected disparado:', {
                    target: config.target,
                    media: Array.isArray(media) ? media[0] : media,
                    inputId: targetInput.id,
                    inputName: targetInput.name
                });
            }

            // Atualizar preview apenas para targets que NÃO são galeria
            if (!isGalleryTarget && config.preview) {
                MediaManager.updatePreview(media, config);
            }
            
            // Atualizar preview automático apenas para thumbnails/singles
            if (!isGalleryTarget) {
                const previewElement = document.getElementById(config.target + '-preview');
                if (previewElement) {
                    MediaManager.updatePreview(media, { ...config, preview: config.target + '-preview' });
                }
            }

            const count = Array.isArray(media) ? media.length : 1;
            notify.success(`${count} mídia(s) selecionada(s)`);

        } catch (error) {
            console.error('Erro ao processar seleção:', error);
            notify.error('Erro ao processar seleção');
        }
    }





     /**
     * Atualiza preview de mídia
     */
    static updatePreview(media, config) {
        const previewElement = document.getElementById(config.preview);
        if (!previewElement) return;

        const mediaArray = Array.isArray(media) ? media : [media];

        const previewHtml = mediaArray.map(item => {



            if (item.is_image) {
                // Imagem: thumbnail real
                // Buscar legenda existente do campo hidden
                const existingCaption = MediaManager.getExistingCaption(config.target);

                return `
                    <div class="relative inline-block mr-2 mb-2" data-media-id="${item.id}">
                        <img src="${item.thumbnail_url}" alt="${item.filename}"
                            class="w-full h-32 object-cover rounded border">
                            <button type="button" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 preview-remove-btn" aria-label="Remover midia">
                            <i class="fas fa-times text-xs"></i>
                            </button>
                        <input type="text"
                               class="text-xs border rounded px-1 py-0.5 mt-1 media-caption-input"
                               placeholder="Legenda..."
                               value="${existingCaption || ''}"
                               data-media-id="${item.id}">
                    </div>
                `;
            }

            let icon = '<i class="fas fa-file text-gray-400 text-2xl"></i>'; // padrão
            let iconBgColor = 'bg-gray-100'; //padrao

            if (item.mime_type === 'application/pdf') {
                icon = '<i class="fas fa-file-pdf text-red-500 text-2xl"></i>';
                iconBgColor = 'bg-red-100';
            } else if (item.mime_type === 'audio/mpeg' || item.mime_type.startsWith('audio/')) {
                icon = '<i class="fas fa-music text-blue-500 text-2xl"></i>';
                iconBgColor = 'bg-blue-100';
            } else if (item.mime_type === 'video/mp4' || item.mime_type.startsWith('video/')) {
                icon = '<i class="fas fa-film text-purple-500 text-2xl"></i>';
                iconBgColor = 'bg-purple-100';
            }

            return `
                <div class="inline-block mr-2 mb-2">
                    <div class="w-32 h-32 ${iconBgColor} rounded border flex items-center justify-center">
                        ${icon}
                    </div>
                    <div class="text-xs text-gray-600 truncate max-w-32 mt-1">${item.filename}</div>
                </div>
            `;
        }).join('');

        previewElement.innerHTML = previewHtml;
        previewElement.classList.remove('hidden');

        // Adicionar eventos para os inputs de legenda
        previewElement.querySelectorAll('.media-caption-input').forEach(input => {
            input.addEventListener('input', function() {
                const mediaId = this.dataset.mediaId;
                const caption = this.value;
                MediaManager.updateMediaCaption(mediaId, caption, config);
            });
        });
    }

    /**
     * Atualiza a legenda da mídia no campo apropriado
     */
    static updateMediaCaption(mediaId, caption, config) {
        // Determinar o contexto baseado no target
        const target = config.target;


        // ✅ QUESTÃO PRINCIPAL
        if (target === 'question_image') {
            const captionInput = document.querySelector(`#question_image-preview input.media-caption-input[data-media-id="${mediaId}"]`);
            
            if (captionInput) {
                captionInput.name = 'question_image_caption';
                captionInput.value = caption;
            }
        }


        // Para alternativas de questões (answers)
        if (target && target.includes('answer_image')) {
            // Extrair o número da alternativa do target (ex: "answer_image_1" -> 1)
            const match = target.match(/answer_image_(\d+)/);
            if (match) {
                // Encontrar o índice baseado no target
                const answerItems = document.querySelectorAll('.answer-item');
                answerItems.forEach((item, index) => {
                    const imageInput = item.querySelector('.answer-image-input');
                    if (imageInput && imageInput.id === target) {
                        const captionInput = document.querySelector(`input[name="answers[${index}][caption]"]`);
                        if (captionInput) {
                            captionInput.value = caption;
                        }
                    }
                });
            }
        }

        // Para outras situações, você pode adicionar mais condições aqui
        // Por exemplo, para imagens na questão principal, etc.
    }

    /**
     * Busca a legenda existente para um target específico
     */
    static getExistingCaption(target) {
        if (target && target.includes('answer_image')) {
            // Extrair o número da alternativa do target (ex: "answer_image_1" -> 1)
            const match = target.match(/answer_image_(\d+)/);
            if (match) {
                // Encontrar o campo de legenda correspondente
                const answerItems = document.querySelectorAll('.answer-item');
                for (let item of answerItems) {
                    const imageInput = item.querySelector('.answer-image-input');
                    if (imageInput && imageInput.id === target) {
                        const captionInput = item.querySelector('.answer-image-caption-input');
                        return captionInput ? captionInput.value : '';
                    }
                }
            }
        }
        return '';
    }














    /**
     * Atualiza preview de mídia
    
    BKP - 10/07/25
    *//*
    static updatePreview(media, config) {
        const previewElement = document.getElementById(config.preview);
        if (!previewElement) return;

        const mediaArray = Array.isArray(media) ? media : [media];
        
        const previewHtml = mediaArray.map(item => {
            if (item.is_image) {
                return `
                    <div class="inline-block mr-2 mb-2">
                        <img src="${item.thumbnail_url}" alt="${item.filename}" 
                             class="w-16 h-16 object-cover rounded border">
                        <div class="text-xs text-gray-600 truncate max-w-16 mt-1">${item.filename}</div>
                    </div>
                `;
            } else {
                return `
                    <div class="inline-block mr-2 mb-2">
                        <div class="w-16 h-16 bg-gray-100 rounded border flex items-center justify-center">
                            <i class="fas fa-file text-gray-400"></i>
                        </div>
                        <div class="text-xs text-gray-600 truncate max-w-16 mt-1">${item.filename}</div>
                    </div>
                `;
            }
        }).join('');
        
        previewElement.innerHTML = previewHtml;
        previewElement.classList.remove('hidden');
    }*/







    /**
     * Atualiza indicadores globais
     */
    static updateGlobalIndicators(selectedItems) {
        // Atualizar contadores se existirem
        document.querySelectorAll('[data-media-count]').forEach(counter => {
            counter.textContent = selectedItems.length;
        });

        // Atualizar botões dependentes de seleção
        document.querySelectorAll('[data-requires-selection]').forEach(button => {
            button.disabled = selectedItems.length === 0;
        });
    }

    /**
     * API pública estática
     */
    static getAPI() {
        return {
            openSelector: MediaManager.openSelector,
            closeModal: MediaManager.closeModal,
            refresh: () => {
                if (MediaManager.standaloneInstance && !MediaManager.standaloneInstance.isDestroyed) {
                    MediaManager.standaloneInstance.refresh();
                }
            },
            getSelected: () => {
                if (MediaManager.standaloneInstance && !MediaManager.standaloneInstance.isDestroyed) {
                    return MediaManager.standaloneInstance.getSelected();
                }
                return [];
            },
            clearSelection: () => {
                if (MediaManager.standaloneInstance && !MediaManager.standaloneInstance.isDestroyed) {
                    MediaManager.standaloneInstance.clearSelection();
                }
            },
            isModalOpen: () => {
                return !!(MediaManager.activeModalInstance && !MediaManager.activeModalInstance.isDestroyed);
            }
        };
    }
}

// ==================== INICIALIZAÇÃO AUTOMÁTICA ====================

// Função para garantir inicialização
function ensureMediaManagerInit() {
    if (typeof window !== 'undefined') {
        // Tentar inicializar imediatamente
        MediaManager.init();
        
        // Também tentar após um pequeno delay para garantir que o DOM esteja pronto
        setTimeout(() => {
            MediaManager.init();
        }, 100);
        
        // E também no evento DOMContentLoaded se ainda não carregou
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                MediaManager.init();
            });
        }
    }
}

// Inicializar
ensureMediaManagerInit();

// ==================== API GLOBAL ====================

// Exportar API para uso global
window.MediaUtils = MediaManager.getAPI();

// Exportar MediaManager para acesso global
window.MediaManager = MediaManager;

// Manter compatibilidade com código existente
window.openMediaSelector = MediaManager.openSelector;

export default MediaManager; 