// Modal de Contato para Módulos Pagos
console.log('Script contact-modal.js carregado!');

function openContactModal(moduleTitle, modulePrice) {
    console.log('Abrindo modal para:', moduleTitle, modulePrice);
    
    const modalTitleElement = document.getElementById('modalModuleTitle');
    const modalPriceElement = document.getElementById('modalModulePrice');
    
    if (!modalTitleElement || !modalPriceElement) {
        console.error('Elementos do modal não encontrados!');
        alert('Erro: Modal não encontrado na página!');
        return;
    }
    
    modalTitleElement.textContent = moduleTitle;
    modalPriceElement.textContent = 'R$ ' + modulePrice;
    
    // Atualizar links com o nome do módulo
    const whatsappLink = document.getElementById('whatsappLink');
    const emailLink = document.getElementById('emailLink');
    
    if (whatsappLink) {
        whatsappLink.href = `https://wa.me/5511999999999?text=Olá! Gostaria de adquirir o módulo: ${encodeURIComponent(moduleTitle)} (R$ ${modulePrice})`;
    }
    
    if (emailLink) {
        emailLink.href = `mailto:<EMAIL>?subject=Interesse no módulo: ${encodeURIComponent(moduleTitle)}&body=Olá! Gostaria de mais informações sobre o módulo "${moduleTitle}" no valor de R$ ${modulePrice}.`;
    }
    
    const modal = document.getElementById('contactModal');
    if (!modal) {
        console.error('Modal contactModal não encontrado!');
        alert('Erro: Modal não encontrado!');
        return;
    }
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    document.body.style.overflow = 'hidden';
}

function closeContactModal() {
    const modal = document.getElementById('contactModal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.style.overflow = 'auto';
    }
}

// Aguardar o DOM carregar antes de adicionar event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('Contact modal script carregado!');
    
    // Fechar modal ao clicar no overlay
    const modal = document.getElementById('contactModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeContactModal();
            }
        });
    }

    // Fechar modal com ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeContactModal();
        }
    });
});
