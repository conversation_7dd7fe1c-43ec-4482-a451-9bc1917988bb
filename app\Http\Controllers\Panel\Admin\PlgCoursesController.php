<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Models\SysUser;
use App\Models\PlgCourse;
use Plank\Mediable\Media;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\PlgCategories;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class PlgCoursesController extends Controller
{
    public function index()
    {
        return view('panel.admin.courses.index', [
            'columns' => $this->getColumns(),
            'id' => 'courses-table',
            'ajaxUrl' => route('admin.courses.data'),
            'filters' => $this->getFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    public function getData()
    {
        $user = Auth::user();

        $query = PlgCourse::with(['category', 'teacher', 'media'])
            ->select('plg_courses.*')
            ->orderBy('id', 'desc');

        // Filtro simples: professores veem apenas seus cursos
        if ($user->role === 'teacher') {
            $query->where('user_id', $user->id);
        }
        // super_admin vê tudo (sem filtro)

        return DataTables::of($query)
            ->editColumn('status', fn($course) => view('panel.admin.courses.partials.status', compact('course'))->render())
            ->editColumn('featured', fn($course) => view('panel.admin.courses.partials.featured', compact('course'))->render())
            ->addColumn('actions', function($course) {
                return view('panel.admin.includes.table-actions', [
                    'actions' => [
                        [
                            'type' => 'edit',
                            'route' => route('admin.courses.edit', $course->id),
                            'title' => 'Editar curso'
                        ],
                        [
                            'type' => 'show',
                            'route' => route('admin.courses.show', $course->id),
                            'title' => 'Visualizar curso'
                        ],
                        [
                            'type' => 'duplicate',
                            'route' => route('admin.courses.duplicate', $course->id),
                            'title' => 'Duplicar curso'
                        ],
                        [
                            'type' => 'delete',
                            'route' => route('admin.courses.destroy', $course->id),
                            'title' => 'Excluir curso',
                            'confirm_message' => 'Tem certeza que deseja excluir este curso?'
                        ]
                    ]
                ])->render();
            })
            /*->addColumn('image_url', function ($course) {
            $media = $course->getMedia('thumbnail')->first();

            if (!$media) return null;

            $path = $media->getDiskPath();

            return route('media.serve', ['path' => $path]) . '?w=100&h=75&fit=crop&fm=webp';
            })*/

           ->editColumn('title', function ($course) {
            $media = $course->getMedia('thumbnail')->first();
            return [
                'text' => $course->title,
                'thumbnail' => $media
                    ? route('media.serve', ['path' => $media->getDiskPath()]).'?w=250&fit=crop&fm=webp'
                    : null,
            ];
            })


            ->filterColumn('category.title', fn($query, $keyword) => $query->whereHas('category', fn($q) => $q->where('title', 'like', "%{$keyword}%")))
            ->filterColumn('teacher.name', fn($query, $keyword) => $query->whereHas('teacher', fn($q) => $q->where('name', 'like', "%{$keyword}%")))
            ->filterColumn('status', fn($query, $keyword) => $keyword ? $query->where('status', $keyword) : null)
            ->rawColumns(['actions', 'status', 'featured'])
            ->make(true);
    }

    public function create()
    {
        return view('panel.admin.courses.create', $this->formData());
    }

    public function store(Request $request)
    {
        $validated = $this->validateRequest($request);

        $user = Auth::user();

        // Se for professor, usar automaticamente o ID dele
        // Se for super_admin, usar o teacher_id selecionado
        $teacherId = $user->role === 'teacher' ? $user->id : $validated['teacher_id'];

        $validated += [
            'user_id' => $teacherId,
            'slug' => Str::slug($validated['title']),
            'company_id' => $user->company_id,
            'featured' => $request->has('featured'),
        ];

        $course = PlgCourse::create($validated);

        // Associar a mídia ao curso se houver thumbnail
        if ($request->filled('thumbnail')) {
            $media = Media::find($request->thumbnail);
            if ($media) {
                $course->attachMedia($media, 'thumbnail');
            }
        }

        return redirect()->route('admin.courses.index')->with('success', 'Curso criado com sucesso!');
    }

    public function edit($id)
    {
        $user = Auth::user();
        $course = PlgCourse::with('media')->findOrFail($id);

        // Validação simples: professor só pode editar seus próprios cursos
        if ($user->role === 'teacher' && $course->user_id !== $user->id) {
            abort(403, 'Você não tem permissão para editar este curso.');
        }

        $thumbnailUrl = '';
        if ($course->hasMedia('thumbnail')) {
            $media = $course->firstMedia('thumbnail');
            $thumbnailUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=250&h=150&fit=crop&fm=webp';
        }

        return view('panel.admin.courses.create', array_merge(
            ['course' => $course, 'thumbnailUrl' => $thumbnailUrl],
            $this->formData()
        ));
    }

    public function show($id)
    {
        $user = Auth::user();
        $course = PlgCourse::with(['category', 'teacher', 'modules'])->findOrFail($id);

        // Validação simples: professor só pode ver seus próprios cursos
        if ($user->role === 'teacher' && $course->user_id !== $user->id) {
            abort(403, 'Você não tem permissão para ver este curso.');
        }

        return view('panel.admin.courses.show', compact('course'));
    }

    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $course = PlgCourse::findOrFail($id);

        // Validação simples: professor só pode editar seus próprios cursos
        if ($user->role === 'teacher' && $course->user_id !== $user->id) {
            abort(403, 'Você não tem permissão para editar este curso.');
        }
        $validated = $this->validateRequest($request);

        $validated += [
            'user_id' => $validated['teacher_id'],
            'slug' => Str::slug($validated['title']),
            'featured' => $request->has('featured'),
        ];

        $course->update($validated);

        // Atualizar a mídia do curso
        if ($request->filled('thumbnail')) {
            // Remover todas as mídias da coleção 'thumbnail'
            $course->detachMediaTags('thumbnail');
            
            // Associar nova mídia
            $media = \Plank\Mediable\Media::find($request->thumbnail);
            if ($media) {
                $course->attachMedia($media, 'thumbnail');
            }
        } else {
            // Se o thumbnail foi removido, remover todas as mídias da coleção
            $course->detachMediaTags('thumbnail');
        }

        return redirect()->route('admin.courses.index')->with('success', 'Curso atualizado com sucesso!');
    }

    public function destroy($id)
    {
        $user = Auth::user();
        $course = PlgCourse::findOrFail($id);

        // Validação simples: professor só pode deletar seus próprios cursos
        if ($user->role === 'teacher' && $course->user_id !== $user->id) {
            abort(403, 'Você não tem permissão para deletar este curso.');
        }

        $this->deleteThumbnail($course->thumbnail);
        $course->delete();

        return response()->json(['success' => true]);
    }

    public function duplicate($id)
    {
        $course = PlgCourse::findOrFail($id);
        
        // Criar uma cópia do curso
        $newCourse = $course->replicate();
        
        // Atualizar campos únicos
        $newCourse->title = $course->title . ' (Cópia)';
        $newCourse->slug = Str::slug($newCourse->title);
        $newCourse->status = 'draft'; // Sempre começa como rascunho
        $newCourse->featured = false; // Reset featured status
        
        // Se tiver thumbnail, duplicar o arquivo
        if ($course->thumbnail) {
            $oldPath = str_replace('storage/', '', $course->thumbnail);
            $extension = pathinfo($oldPath, PATHINFO_EXTENSION);
            $newPath = 'courses/' . Str::random(40) . '.' . $extension;
            
            if (Storage::disk('public')->exists($oldPath)) {
                Storage::disk('public')->copy($oldPath, $newPath);
                $newCourse->thumbnail = 'storage/' . $newPath;
            }
        }
        
        $newCourse->save();

        return redirect()->route('admin.courses.edit', $newCourse->id)
            ->with('success', 'Curso duplicado com sucesso! Você pode editar os detalhes agora.');
    }



    private function getColumns()
    {
        return [
            ['data' => 'id', 'name' => 'plg_courses.id', 'label' => 'ID'],
            ['data' => 'title', 'name' => 'plg_courses.title', 'label' => 'Título'],
            ['data' => 'category.title', 'name' => 'category.title', 'label' => 'Categoria'],
            ['data' => 'teacher.name', 'name' => 'teacher.name', 'label' => 'Professor'],
            ['data' => 'status', 'name' => 'plg_courses.status', 'label' => 'Status'],
            ['data' => 'featured', 'name' => 'plg_courses.featured', 'label' => 'Destaque'],
            ['data' => 'actions', 'name' => 'actions', 'label' => 'Ações', 'orderable' => false, 'searchable' => false],
        ];
    }

    private function getFilters()
    {
        $categories = PlgCategories::orderBy('title')->get()->map(fn($c) => ['value' => $c->title, 'label' => $c->title]);
        $teachers = SysUser::where('role', 'teacher')->orderBy('name')->get()->map(fn($t) => ['value' => $t->name, 'label' => $t->name]);

        return [
            ['label' => 'Categoria', 'column' => 'category.title', 'options' => $categories],
            ['label' => 'Status', 'column' => 'status', 'options' => [
                ['value' => 'published', 'label' => 'Publicado'],
                ['value' => 'draft', 'label' => 'Rascunho'],
                ['value' => 'archived', 'label' => 'Arquivado'],
            ]],
            ['label' => 'Professor', 'column' => 'teacher.name', 'options' => $teachers],
        ];
    }

    private function formData()
    {
        return [
            'categories' => PlgCategories::orderBy('title')->get(),
            'teachers' => SysUser::where('role', 'teacher')->orderBy('name')->get(),
            'thumbnailUrl' => ''
        ];
    }

    private function validateRequest(Request $request)
    {
        return $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'required|string|max:255',
            'category_id' => 'required|exists:plg_categories,id',
            'teacher_id' => 'required|exists:sys_users,id',
            'thumbnail' => 'nullable|exists:media,id',
            'status' => 'required|in:draft,published,archived',
            'featured' => 'boolean',
            'duration_minutes' => 'nullable|integer',
        ]);
    }

    private function storeThumbnail(Request $request)
    {
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('courses', 'public');
            return 'storage/' . $path;
        }
        return $request->thumbnail;
    }

    private function deleteThumbnail($path)
    {
        if ($path) {
            $oldPath = str_replace('storage/', '', $path);
            Storage::disk('public')->delete($oldPath);
        }
    }
}
