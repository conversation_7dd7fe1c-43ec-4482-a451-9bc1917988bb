@import 'theme.css';
@import "choices.js/public/assets/styles/choices.min.css";
@import 'choices-custom.css';
@import 'datatable-custom.css';
@import "quill/dist/quill.core.css";
@import "quill/dist/quill.snow.css";
@import "notyf/notyf.min.css";


@tailwind base;
@tailwind components;
@tailwind utilities;

/* Cores do Tema */
:root {
    --color-trends-primary: #ef4444;
    --color-trends-secondary: #1e1e1e;
    --color-trends-accent: #f8f9fa;
}

/* Aplicando as cores como classes utilitárias */
.text-trends-primary {
    color: var(--color-trends-primary);
}

.bg-trends-primary {
    background-color: var(--color-trends-primary);
}

.text-trends-secondary {
    color: var(--color-trends-secondary);
}

.bg-trends-secondary {
    background-color: var(--color-trends-secondary);
}

/* Classes de opacidade personalizada */
.bg-trends-primary\/80 {
    background-color: rgba(239, 68, 68, 0.8);
}

.bg-trends-secondary\/80 {
    background-color: rgba(30, 30, 30, 0.8);
}

/* Removendo a transição antiga */
/* * {
    transition: background-color 0.3s ease, color 0.3s ease;
} */

@layer base {
    body {
        font-family: 'Inter', sans-serif;
    }
}

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Sidebar Transitions */
.sidebar {
    transition: all 300ms ease-in-out;
    overflow: hidden;
}

.sidebar-toggle i {
    transition: transform 300ms ease-in-out;
}

.menu-text {
    transition: opacity 200ms ease-in-out;
}

/* Logo styles */
.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-full, .logo-mini {
    transition: opacity 200ms ease-in-out;
}

.sidebar-collapsed .logo-full {
    display: none;
}

.sidebar-collapsed .logo-mini {
    display: block;
}

/* Submenu Styles */
.submenu {
    transition: all 200ms ease-in-out;
    overflow: hidden;
    max-height: 0;
    background-color: #151515;
}

.submenu:not(.hidden) {
    max-height: 400px;
}

.menu-arrow {
    transition: transform 200ms ease-in-out;
    transform: rotate(0deg);
}

.menu-arrow.active {
    transform: rotate(180deg);
}

.has-submenu {
    cursor: pointer;
    user-select: none;
}

/* Hover Card Animations */
.hover-card {
    transition: opacity 150ms ease-in-out;
    width: 200px;
    background-color: #1e1e1e;
}

/* Sidebar Items */
.sidebar-item:hover .sidebar-item-icon,
.sidebar-item.active .sidebar-item-icon {
    color: var(--color-trends-primary);
}

/* Quando sidebar está aberto */
.sidebar:not(.sidebar-collapsed) .sidebar-item.active {
    background-color: rgba(239, 68, 68, 0.1); /* Vermelho com opacidade baixa */
}

.sidebar:not(.sidebar-collapsed) .sidebar-item.active .menu-text,
.sidebar:not(.sidebar-collapsed) .sidebar-item.active .sidebar-item-icon {
    color: var(--color-trends-primary);
}

/* Quando sidebar está fechado */
.sidebar.sidebar-collapsed .sidebar-item-icon {
    margin-left: auto;
    margin-right: auto;
}

/* Responsividade */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        z-index: 30;
    }
    
    /* Ajuste para submenus em mobile */
    #mobile-sidebar .submenu {
        background-color: #151515;
        max-height: none;
        display: none;
    }
    
    #mobile-sidebar .submenu:not(.hidden) {
        display: block;
    }
}

/* Remover barra de rolagem em todos os elementos */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
}

* {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* ==========================================
   MEDIA MANAGER STYLES - CENTRALIZADOS
   ========================================== */

/* Estilos básicos para itens de mídia */
/* .media-item {
    position: relative;
    border: 2px solid transparent;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
} */

.media-item:hover {
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Estado selecionado - SEM pseudoelementos */
.media-item.selected {
    border-color: #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

/* Estilos para modo de seleção múltipla */
.multiple-select-mode .media-item {
    cursor: pointer;
    position: relative;
}

.multiple-select-mode .media-item::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    background-color: white;
    z-index: 10;
    transition: all 0.2s ease;
}

.multiple-select-mode .media-item.selected::before {
    background-color: #ef4444;
    border-color: #ef4444;
}

.multiple-select-mode .media-item.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    color: white;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 11;
}

.multiple-select-mode .media-item.selected {
    border-color: #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1);
}

/* Indicador de seleção */
.selection-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ef4444;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

/* Dropzone para upload */
.media-dropzone {
    border: 2px dashed #d1d5db;
    transition: all 0.2s ease;
}

.media-dropzone.drag-over {
    border-color: #ef4444;
    background-color: rgba(239, 68, 68, 0.05);
}

/* Modal de preview */
.media-preview-modal {
    z-index: 9999;
}

.media-preview-content img {
    max-height: 80vh;
    max-width: 90vw;
}

/* ==========================================
   PAGINATION STYLES - CENTRALIZADOS
   ========================================== */

/* Botões de paginação básicos */
.pagination-btn {
    min-width: 2.5rem;
    text-align: center;
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    color: #71717a;
    background-color: white;
    border: 1px solid #d4d4d8;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.dark .pagination-btn {
    color: #a1a1aa;
    background-color: #27272a;
    border-color: #3f3f46;
}

/* Estados dos botões */
.pagination-btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.pagination-btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #dc2626;
    color: white;
    border-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(220, 38, 38, 0.1);
}

.pagination-btn.active {
    background-color: #dc2626;
    border-color: #dc2626;
    color: white;
    cursor: default;
    box-shadow: 0 2px 4px -1px rgba(220, 38, 38, 0.2);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Container de paginação */
.media-pagination nav {
    background: white;
    border-top: 1px solid #e4e4e7;
    padding: 1rem;
}

.dark .media-pagination nav {
    background: #18181b;
    border-color: #3f3f46;
}

/* Botões de navegação específicos */
.media-pagination .prev-page-btn,
.media-pagination .next-page-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.dark .media-pagination .prev-page-btn,
.dark .media-pagination .next-page-btn {
    color: #d1d5db;
    background-color: #374151;
    border-color: #4b5563;
}

.media-pagination .prev-page-btn:hover,
.media-pagination .next-page-btn:hover {
    background-color: #dc2626;
    color: white;
    border-color: #dc2626;
}

/* Ellipsis */
.media-pagination .navigation-controls span {
    padding: 0.5rem 1rem;
    color: #71717a;
    font-weight: 500;
}

.dark .media-pagination .navigation-controls span {
    color: #a1a1aa;
}

/* Estado de carregamento */
.media-pagination.loading {
    opacity: 0.6;
    pointer-events: none;
}

.media-pagination.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid #dc2626;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

/* Responsividade */
@media (max-width: 640px) {
    .media-pagination nav {
        padding: 0.75rem;
    }
    
    .media-pagination .navigation-controls button {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Responsividade para o grid de mídias */
.media-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}
@media (min-width: 400px) {
  .media-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 640px) {
  .media-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.25rem;
  }
}
@media (min-width: 1024px) {
  .media-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
@media (min-width: 1280px) {
  .media-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Estilização dos itens de mídia */
.media-item {
  min-height: 0;
  will-change: transform, box-shadow;
}

.media-item:hover {
  transform: translateY(-2px);
}

.media-item.selected {
  transform: translateY(-2px) scale(1.02);
}

/* Aspect ratio para imagens quadradas */
.aspect-square {
  aspect-ratio: 1 / 1;
}

/* Melhorar transições */
.media-item img {
  transition: transform 0.2s ease;
}

.media-item:hover img {
  transform: scale(1.05);
}

.media-item.selected img {
  transform: scale(1.02);
}

/* Responsividade do checkbox */
@media (max-width: 640px) {
  .media-item .absolute.top-2.left-2 {
    top: 0.25rem;
    left: 0.25rem;
  }
  
  .media-item .absolute.top-2.right-2 {
    top: 0.25rem;
    right: 0.25rem;
  }
}

/* Nomes de arquivos quebram linha */
.media-grid .media-item .media-filename {
  white-space: normal;
  word-break: break-all;
  font-size: 1rem;
  text-align: center;
}

/* Botões maiores no mobile */
@media (max-width: 640px) {
  .media-upload-btn, #cleanOrphanedBtn, .cleanOrphanedBtn {
    font-size: 1.1rem;
    padding: 0.9rem 1.2rem;
  }
}

/* Botão toggle sidebar */
#toggleSidebarBtn {
  min-width: 40px;
  min-height: 40px;
}

/* Sidebar mobile sobreposta */
#mobileSidebar {
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.15);
}

/* Botão X da sidebar */
#closeSidebarBtn {
  transition: all 0.3s ease-in-out;
}

/* Media content como container relativo */
#media-content {
  position: relative;
}

/* ==========================================
   SWIPER CAROUSEL STYLES
   ========================================== */

/* Estilos básicos para Swiper */
.swiper-carousel {
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

.swiper-carousel .swiper-wrapper {
    padding: 15px 0;
    margin: -15px 0;
}

.swiper-slide {
    width: auto;
    height: auto;
    flex-shrink: 0;
    padding: 12px 8px;
}

/* Desabilitar ícones padrão do Swiper */
.swiper-button-next:after,
.swiper-button-prev:after {
    display: none !important;
    content: '' !important;
}

/* Forçar invisibilidade total dos botões por padrão */
.swiper-button-next,
.swiper-button-prev {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    transition: all 0.3s ease !important;
}

/* Mostrar botões apenas no hover do grupo */
.group:hover .swiper-button-next,
.group:hover .swiper-button-prev {
    opacity: 0.9 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Hover individual dos botões */
.group:hover .swiper-button-next:hover,
.group:hover .swiper-button-prev:hover {
    opacity: 1 !important;
    transform: translateY(-50%) scale(1.1) !important;
}

/* Desabilitar estados disabled do Swiper */
.swiper-button-disabled {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Garantir que slides não sejam cortados */
.swiper-wrapper {
    align-items: stretch;
}

/* Evitar corte dos efeitos de hover nos cards */
.swiper-slide a,
.swiper-slide > div {
    transform-origin: center;
    will-change: transform;
}

/* Espaço interno para animações de hover */
.swiper-slide > a > div,
.swiper-slide > div {
    margin: 0 4px;
}