@extends('layouts.panel')

@section('title', isset($test) ? 'Editar Teste' : 'Novo Teste')
@section('page_title', isset($test) ? 'Editar Teste' : 'Novo Teste')

@push('styles')
    <style>
        .selected-questions-list .question-item,
        .questions-list .question-item {
            transition: all 0.3s ease;
        }

        .selected-questions-list .question-item:hover,
        .questions-list .question-item:hover {
            background-color: rgba(var(--color-primary-rgb), 0.05);
        }

        .question-item .tag {
            font-size: 0.7rem;
            padding: 0.1rem 0.4rem;
        }

        .search-questions {
            position: relative;
        }

        .search-questions i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
        }

        .search-questions input {
            padding-left: 30px;
        }

        .questions-count-badge {
            min-width: 20px;
        }

        .type-info {
            background-color: #f1f5f9;
            border-left: 3px solid #3b82f6;
        }

        .type-info.challenge {
            border-left-color: #f97316;
        }

        .type-info.exam {
            border-left-color: #22c55e;
        }

        /* Estilização da barra de rolagem */
        .questions-list::-webkit-scrollbar,
        .selected-questions-list::-webkit-scrollbar {
            width: 8px;
        }

        .questions-list::-webkit-scrollbar-track,
        .selected-questions-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .questions-list::-webkit-scrollbar-thumb,
        .selected-questions-list::-webkit-scrollbar-thumb {
            background: var(--color-primary, #ef4444);
            border-radius: 4px;
        }

        .questions-list::-webkit-scrollbar-thumb:hover,
        .selected-questions-list::-webkit-scrollbar-thumb:hover {
            background: var(--color-primary-hover, #dc2626);
        }

        .dark .questions-list::-webkit-scrollbar-track,
        .dark .selected-questions-list::-webkit-scrollbar-track {
            background: #2d2d2d;
        }

        /* Estilo para questões selecionadas */
        .question-item.selected {
            background-color: rgba(var(--color-primary-rgb), 0.05);
            border-color: var(--color-primary, #ef4444) !important;
        }

        .dark .question-item.selected {
            background-color: rgba(var(--color-primary-rgb), 0.15);
            border-color: var(--color-primary, #ef4444) !important;
        }

        .question-item .select-check {
            display: none;
        }

        .question-item.selected .select-check {
            display: block;
        }

        /* Estilo para questões selecionadas na lista de questões selecionadas */
        .selected-question-item.selected {
            background-color: rgba(var(--color-primary-rgb), 0.05);
            border-color: var(--color-primary, #ef4444) !important;
        }

        .dark .selected-question-item.selected {
            background-color: rgba(var(--color-primary-rgb), 0.15);
            border-color: var(--color-primary, #ef4444) !important;
        }

        .selected-question-item .remove-check {
            display: none;
            position: absolute;
            top: 8px;
            left: 8px;
            color: var(--color-primary, #ef4444);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            padding: 2px;
            font-size: 0.75rem;
        }

        .dark .selected-question-item .remove-check {
            background-color: rgba(30, 30, 30, 0.9);
        }

        .selected-question-item.selected .remove-check {
            display: flex;
        }
    </style>
@endpush

@section('content')
    <div class="animate-fade-in">
        @includeIf('panel.admin.includes.alerts')

        <form action="{{ isset($test) ? route('admin.tests.update', $test->id) : route('admin.tests.store') }}" method="POST"
            id="testForm">
            @csrf
            @if (isset($test))
                @method('PUT')
            @endif

            <!-- Container para os campos de questões -->
            <div id="questions_container"></div>

            <div class="grid grid-cols-1 lg:grid-cols-1 gap-6">
                <!-- Coluna da esquerda - Configuração do Teste -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 rounded-lg mb-6">
                        <div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-700">
                            <div class="flex items-center gap-2">
                                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                                <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Novo Teste</h1>
                            </div>
                            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                                Preencha os dados para configurar o teste.
                            </p>
                        </div>

                        <div class="p-6">
                            <div class="flex flex-col md:flex-row gap-6">
                                <!-- Nome do Teste -->
                                <div class="flex-1">
                                    <label for="name"
                                        class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Nome do Teste <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="name" name="name"
                                        value="{{ old('name', $test->title ?? '') }}" required
                                        placeholder="Digite o nome do teste"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                                </div>

                                <!-- Curso -->
                                <div class="flex-1">
                                    <label for="course_id"
                                        class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Curso <span class="text-red-500">*</span>
                                    </label>
                                    <select id="course_id" name="course_id" required
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                                        <option value="">Selecione um curso</option>
                                        @foreach ($courses as $course)
                                            <option value="{{ $course->id }}"
                                                {{ old('course_id', isset($test) ? $test->module->course_id : '') == $course->id ? 'selected' : '' }}>
                                                {{ $course->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Módulo -->
                                <div class="flex-1">
                                    <label for="module_id"
                                        class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Módulo <span class="text-red-500">*</span>
                                    </label>
                                    <select id="module_id" name="module_id" required
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                                        @if(isset($test))
                                            <option value="{{ $test->module_id }}" selected>{{ $test->module->title }}</option>
                                        @else
                                            <option value="">Selecione um curso primeiro</option>
                                        @endif
                                    </select>
                                </div>

                                <!-- Tipo de Teste -->
                                <div class="flex-1">
                                    <label for="test_type" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Tipo de Teste <span class="text-red-500">*</span>
                                    </label>
                                    <select id="test_type" name="test_type" class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200" required>
                                        <option value="quiz"
                                            {{ old('test_type', $test->test_type ?? '') == 'quiz' ? 'selected' : '' }}>
                                            Quiz
                                        </option>
                                        <option value="challenge"
                                            {{ old('test_type', $test->test_type ?? '') == 'challenge' ? 'selected' : '' }}>
                                            Desafio
                                        </option>
                                        <option value="exam"
                                            {{ old('test_type', $test->test_type ?? '') == 'exam' ? 'selected' : '' }}>
                                            Exame
                                        </option>
                                    </select>
                                </div>

                                <!-- Status do teste -->
                                <div class="flex-auto">
                                    <label for="test_type" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                        Teste Ativo <span class="text-red-500">*</span>
                                    </label>
                                    
                                    <label class="top-2 relative inline-flex items-center cursor-pointer relative">
                                        <input type="checkbox" name="active" value="1" class="sr-only peer" {{ old('active', $test->active ?? true) ? 'checked' : '' }}>
                                        <div class="mr-3 w-11 h-6 bg-zinc-300 dark:bg-zinc-700 rounded-full peer peer-focus:ring-2 peer-focus:ring-green-500/30 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-zinc-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="p-6 grid grid-cols-1 lg:grid-cols-1">
                            <!-- Informações do tipo selecionado -->
                            <div id="type-info-quiz" class="type-info rounded p-4 text-zinc-600 dark:text-zinc-400 text-sm {{ old('test_type', $test->test_type ?? 'quiz') == 'quiz' ? '' : 'hidden' }}">
                                <p><strong>Quiz:</strong> Um conjunto de questões simples para revisão de conteúdo.</p>
                                <p class="mt-1">Recomendado para revisão rápida e verificação de conhecimento.</p>
                                <p class="mt-2 text-blue-600 dark:text-blue-400"><i class="fas fa-info-circle mr-1"></i> Permite questões gratuitas e pagas.</p>
                            </div>

                            <div id="type-info-challenge" class="type-info challenge rounded p-4 text-zinc-600 dark:text-zinc-400 text-sm {{ old('test_type', $test->test_type ?? '') == 'challenge' ? '' : 'hidden' }}">
                                <p><strong>Desafio:</strong> Permite apenas questões gratuitas.</p>
                                <p class="mt-1">Ideal para oferecer testes gratuitos como amostra do conteúdo.</p>
                                <p class="mt-2 text-orange-600 dark:text-orange-400"><i class="fas fa-exclamation-circle mr-1"></i> Somente questões gratuitas são permitidas.</p>
                            </div>

                            <div id="type-info-exam" class="type-info exam rounded p-4 text-zinc-600 dark:text-zinc-400 text-sm {{ old('test_type', $test->test_type ?? '') == 'exam' ? '' : 'hidden' }}">
                                <p><strong>Exame:</strong> Avaliação completa com tempo limitado.</p>
                                <p class="mt-1">Apropriado para avaliações finais de módulo ou simulados de prova.</p>
                                <p class="mt-2 text-green-600 dark:text-green-400"><i class="fas fa-lock mr-1"></i> Somente questões pagas são permitidas.</p>
                            </div>
                        </div>

                        <!-- Configurações de Tentativas -->
                        <div class="px-6 py-4 border-t border-zinc-200 dark:border-zinc-700">
                            <div class="flex items-center gap-2 mb-4">
                                <div class="h-5 w-1 bg-orange-500 rounded-full"></div>
                                <h2 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Configurações de Tentativas</h2>
                            </div>
                            <p class="text-zinc-500 dark:text-zinc-400 text-sm mb-6">
                                Configure quantas tentativas são permitidas, tempo limite e controle de inatividade.
                            </p>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Máximo de Tentativas -->
                                <div>
                                    <label for="max_attempts" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                        Máximo de Tentativas
                                    </label>
                                    <select id="max_attempts" name="max_attempts"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                                        <option value="1" {{ old('max_attempts', $test->max_attempts ?? 1) == 1 ? 'selected' : '' }}>1 tentativa</option>
                                        <option value="2" {{ old('max_attempts', $test->max_attempts ?? 1) == 2 ? 'selected' : '' }}>2 tentativas</option>
                                        <option value="3" {{ old('max_attempts', $test->max_attempts ?? 1) == 3 ? 'selected' : '' }}>3 tentativas</option>
                                        <option value="5" {{ old('max_attempts', $test->max_attempts ?? 1) == 5 ? 'selected' : '' }}>5 tentativas</option>
                                        <option value="10" {{ old('max_attempts', $test->max_attempts ?? 1) == 10 ? 'selected' : '' }}>10 tentativas</option>
                                    </select>
                                    <div class="mt-1">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" name="allow_unlimited_attempts" value="1"
                                                {{ old('allow_unlimited_attempts', $test->allow_unlimited_attempts ?? false) ? 'checked' : '' }}
                                                class="rounded border-zinc-300 text-trends-primary focus:ring-trends-primary/30">
                                            <span class="ml-2 text-sm text-zinc-600 dark:text-zinc-400">Tentativas ilimitadas</span>
                                        </label>
                                    </div>
                                </div>
                            </div>




                        </div>

                        <!-- Seleção de Questões -->
                        <div>
                            <div class="px-6 py-4 border-t border-b border-zinc-200 dark:border-zinc-700">
                                <div class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <div class="h-5 w-1 bg-purple-500 rounded-full"></div>
                                        <div>
                                            <h2 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Seleção de Questões</h2>
                                <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                                    Selecione as questões para incluir no teste.
                                </p>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="{{ route('admin.questions.import') }}" 
                                           class="inline-flex items-center gap-2 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
                                            <i class="fas fa-file-import"></i>
                                            <span>Importar CSV</span>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
                                <!-- Banco de Questões -->
                                <div class="bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 rounded-lg">
                                    <div class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700 flex items-center justify-between">
                                        <h3 class="text-lg font-medium text-zinc-800 dark:text-zinc-100">Banco de Questões</h3>
                                        <div class="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300 rounded-full px-3 py-1 text-xs font-medium">
                                            <span id="available-questions-count">{{ isset($questions) ? count($questions) : 0 }}</span>
                                            questões
                                        </div>
                                    </div>

                                    <!-- Filtros do banco de questões -->
                                    <div class="px-6 py-4">
                                        <div class="flex flex-wrap gap-4">
                                            <div class="w-full">
                                                <div class="flex items-center">
                                                    <div class="relative w-full">
                                                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                                                            <i class="fas fa-search text-gray-400"></i>
                                                        </div>
                                                        <input type="text" id="questions-search" class="bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 text-zinc-900 dark:text-zinc-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" placeholder="Buscar questões...">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
                                                <div>
                                                    <label for="question-category-filter" class="block mb-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">Categoria</label>
                                                    <select id="question-category-filter" class="bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 text-zinc-900 dark:text-zinc-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                                        <option value="">Todas as categorias</option>
                                                        @if (isset($categories))
                                                            @foreach ($categories as $category)
                                                                <option value="{{ $category->id }}">{{ $category->title }}
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>

                                                <div>
                                                    <label for="question-type-filter"
                                                        class="block mb-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">Tipo</label>
                                                    <select id="question-type-filter"
                                                        class="bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 text-zinc-900 dark:text-zinc-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                                        <option value="">Todos os tipos</option>
                                                        <option value="single">Múltipla escolha (única)</option>
                                                        <option value="multiple">Múltipla escolha (várias)</option>
                                                        <option value="essay">Dissertativa</option>
                                                    </select>
                                                </div>

                                                <div>
                                                    <label for="question-sort-filter"
                                                        class="block mb-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">Ordenar
                                                        por</label>
                                                    <select id="question-sort-filter"
                                                        class="bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 text-zinc-900 dark:text-zinc-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                                        <option value="newest">Mais recentes</option>
                                                        <option value="oldest">Mais antigas</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <div class="flex items-center justify-between mb-2">
                                                <div class="flex items-center gap-2">
                                                    <!-- Removendo os botões Todos e Desmarcar todos -->
                                                    <button type="button" id="select-all-questions"
                                                        class="inline-flex items-center gap-1 px-2 py-1 border border-trends-primary/30 rounded bg-trends-primary/10 text-trends-primary hover:bg-trends-primary/20">
                                                        <i class="fas fa-check-square text-sm"></i>
                                                        <span>Todos</span>
                                                    </button>
                                                    <button type="button" id="unselect-all-questions"
                                                        style="display: none;"
                                                        class="inline-flex items-center gap-1 px-2 py-1 border border-trends-primary/30 rounded bg-trends-primary/10 text-trends-primary hover:bg-trends-primary/20">
                                                        <i class="fas fa-square text-sm"></i>
                                                        <span>Desmarcar todos</span>
                                                    </button>
                                                </div>
                                                <button type="button" id="add-all-questions"
                                                    class="inline-flex items-center gap-1 px-2 py-1 border border-trends-primary/30 rounded bg-trends-primary/10 text-trends-primary hover:bg-trends-primary/20">
                                                    <i class="fas fa-plus-circle text-sm"></i>
                                                    <span id="add-questions-text">Adicionar</span>
                                                </button>
                                            </div>

                                            <div class="questions-list space-y-2 min-h-[300px] max-h-[500px] overflow-y-auto border border-zinc-200 dark:border-zinc-700 rounded-lg bg-white dark:bg-zinc-900 p-2">
                                                @if (empty($questions) || count($questions) == 0)
                                                    <div class="p-4 text-center text-zinc-500 dark:text-zinc-400">
                                                        <p>Nenhuma questão disponível.</p>
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Paginação -->
                                            <div id="questions-pagination" class="mt-4 flex justify-center gap-2"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Questões Selecionadas -->
                                <div
                                    class="bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 rounded-lg">
                                    <div
                                        class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700 flex items-center justify-between">
                                        <h3 class="text-lg font-medium text-zinc-800 dark:text-zinc-100">Questões
                                            Selecionadas</h3>
                                        <div
                                            class="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-300 rounded-full px-3 py-1 text-xs font-medium">
                                            <span
                                                id="selected-questions-count">{{ isset($selectedQuestions) ? count($selectedQuestions) : 0 }}</span>
                                            questões
                                        </div>
                                    </div>

                                    <div class="p-6">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300">Questões
                                                adicionadas</h4>
                                            <div class="flex items-center gap-2">
                                                <button type="button" id="select-all-selected"
                                                    class="inline-flex items-center gap-1 px-2 py-1 border border-trends-primary/30 rounded bg-trends-primary/10 text-trends-primary hover:bg-trends-primary/20">
                                                    <i class="fas fa-check-square text-sm"></i>
                                                    <span>Todos</span>
                                                </button>
                                                <button type="button" id="unselect-all-selected" style="display: none;"
                                                    class="inline-flex items-center gap-1 px-2 py-1 border border-trends-primary/30 rounded bg-trends-primary/10 text-trends-primary hover:bg-trends-primary/20">
                                                    <i class="fas fa-square text-sm"></i>
                                                    <span>Desmarcar todos</span>
                                                </button>
                                                <button type="button" id="remove-selected-questions"
                                                    class="inline-flex items-center gap-1 px-2 py-1 border border-red-300 rounded bg-red-50 dark:bg-red-900/10 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20"
                                                    style="display: none;">
                                                    <i class="fas fa-trash-alt text-sm"></i>
                                                    <span id="remove-questions-text">Remover</span>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="selected-questions-list space-y-2 min-h-[300px] max-h-[500px] overflow-y-auto border border-zinc-200 dark:border-zinc-700 rounded-lg bg-white dark:bg-zinc-900 p-2"
                                            id="selected-questions-list">
                                            @if (isset($selectedQuestions) && count($selectedQuestions) > 0)
                                                @foreach ($selectedQuestions as $question)
                                                    <div class="selected-question-item p-3 border border-zinc-200 dark:border-zinc-700 rounded-lg bg-gray-50 dark:bg-zinc-800/50 flex items-center justify-between gap-4 mb-2 relative"
                                                        data-id="{{ $question->id }}">
                                                        <div class="remove-check w-5 h-5 flex items-center justify-center">
                                                            <i class="fas fa-times-circle"></i>
                                                        </div>
                                                        <input type="hidden" class="question-ref"
                                                            value="{{ $question->id }}">
                                                        <div class="flex flex-col flex-grow">
                                                            <div
                                                                class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                                                {!! strip_tags(Str::limit($question->question, 80)) !!}
                                                            </div>
                                                            <div class="flex gap-2 mt-1 flex-wrap">
                                                                <span
                                                                    class="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-0.5 rounded">
                                                                    {{ ucfirst($question->question_type) }}
                                                                </span>

                                                                @if ($question->free)
                                                                    <span
                                                                        class="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-0.5 rounded">
                                                                        Gratuita
                                                                    </span>
                                                                @else
                                                                    <span
                                                                        class="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 px-2 py-0.5 rounded">
                                                                        Paga
                                                                    </span>
                                                                @endif

                                                                @if ($question->categories->count() > 0)
                                                                    <span
                                                                        class="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-0.5 rounded">
                                                                        {{ $question->categories->first()->title }}
                                                                    </span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <button type="button"
                                                            class="remove-question-btn h-8 w-8 bg-red-500 text-white rounded-full hover:bg-red-600 flex items-center justify-center flex-shrink-0">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div
                                                    class="p-4 text-center text-zinc-500 dark:text-zinc-400 selected-questions-empty">
                                                    <p>Nenhuma questão selecionada.</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="px-6 py-4 bg-zinc-50 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800">
                            <div class="flex justify-end gap-4">
                                <!-- Status do Teste -->
                                <div>
                                    <a href="{{ route('admin.tests.index') }}"
                                        class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                                        <i class="fas fa-times"></i>
                                        <span>Cancelar</span>
                                    </a>
                                    <button type="submit" id="submit-button"
                                        class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                                        <i class="fas fa-save"></i>
                                        <span>{{ isset($test) ? 'Atualizar' : 'Criar Teste' }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        const searchInput = document.getElementById('questions-search');
        const categoryFilter = document.getElementById('question-category-filter');
        const typeFilter = document.getElementById('question-type-filter');
        const sortFilter = document.getElementById('question-sort-filter');
        const testTypeSelect = document.getElementById('test_type');
        const courseSelect = document.getElementById('course_id');
        const moduleSelect = document.getElementById('module_id');
        const questionsList = document.querySelector('.questions-list');
        const selectedList = document.getElementById('selected-questions-list');
        const questionsContainer = document.getElementById('questions_container');
        const availableCount = document.getElementById('available-questions-count');
        const selectedCount = document.getElementById('selected-questions-count');
        const questionsPagination = document.getElementById('questions-pagination');
        
        let selectedQuestions = [];
        let isEditMode = {{ isset($test) ? 'true' : 'false' }};
        let initialModuleId = '{{ isset($test) ? $test->module_id : '' }}';

        // Inicializar questões selecionadas se estiver em modo de edição
        if (selectedList) {
            selectedList.querySelectorAll('.selected-question-item').forEach(item => {
                const questionId = item.dataset.id;
                if (questionId) {
                    selectedQuestions.push(questionId);
                }
            });
            updateSelectedQuestionsInputs();
        }

        // Função para carregar os módulos baseado no curso selecionado
        function loadModules(courseId, callback = null) {
            moduleSelect.innerHTML = '<option value="">Carregando módulos...</option>';
            moduleSelect.disabled = true;

            if (!courseId) {
                moduleSelect.innerHTML = '<option value="">Selecione um curso primeiro</option>';
                moduleSelect.disabled = true;
                return;
            }

            fetch(`{{ url('panel/admin/modules/course') }}/${courseId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                moduleSelect.innerHTML = '<option value="">Selecione um módulo</option>';
                
                if (data && data.modules && data.modules.length > 0) {
                    data.modules.forEach(module => {
                        const option = document.createElement('option');
                        option.value = module.id;
                        option.textContent = module.title;
                        
                        // Se houver um módulo previamente selecionado
                        if (module.id == initialModuleId) {
                            option.selected = true;
                        }
                        
                        moduleSelect.appendChild(option);
                    });
                    moduleSelect.disabled = false;
                } else {
                    moduleSelect.innerHTML = '<option value="">Nenhum módulo encontrado</option>';
                }

                if (callback) callback();
            })
            .catch(error => {
                console.error('Erro ao carregar módulos:', error);
                moduleSelect.innerHTML = '<option value="">Erro ao carregar módulos</option>';
                moduleSelect.disabled = true;
            })
            .finally(() => {
                moduleSelect.disabled = false;
            });
        }

        // Evento para quando o curso é alterado
        courseSelect.addEventListener('change', function() {
            initialModuleId = ''; // Limpa o módulo inicial ao trocar de curso
            loadModules(this.value);
        });

        // Carrega os módulos inicialmente se um curso estiver selecionado
        if (courseSelect.value) {
            loadModules(courseSelect.value);
        }

        // Função para renderizar a paginação
        function renderPagination(currentPage, hasMorePages) {
            let html = '';
            
            if (currentPage > 1) {
                html += `
                    <button type="button" class="pagination-btn prev-page h-8 w-8 bg-gray-100 dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 rounded hover:bg-gray-200 dark:hover:bg-zinc-700 flex items-center justify-center" data-page="${currentPage - 1}">
                        <i class="fas fa-chevron-left text-xs"></i>
                    </button>
                `;
            }
            
            if (hasMorePages) {
                html += `
                    <button type="button" class="pagination-btn next-page h-8 w-8 bg-gray-100 dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 rounded hover:bg-gray-200 dark:hover:bg-zinc-700 flex items-center justify-center" data-page="${currentPage + 1}">
                        <i class="fas fa-chevron-right text-xs"></i>
                    </button>
                `;
            }
            
            questionsPagination.innerHTML = html;
        }

        // Função para atualizar a mensagem de informação do tipo de teste
        function updateTestTypeInfo(testType) {
            document.querySelectorAll('[id^="type-info-"]').forEach(el => el.classList.add('hidden'));
            const infoElement = document.getElementById(`type-info-${testType}`);
            if (infoElement) {
                infoElement.classList.remove('hidden');
            }
        }

        // Evento para quando o tipo de teste é alterado
        testTypeSelect.addEventListener('change', function() {
            updateTestTypeInfo(this.value);
            loadQuestions();
        });

        searchInput.addEventListener('keyup', debounce(() => loadQuestions(), 300));
        
        [categoryFilter, typeFilter, sortFilter].forEach(el => {
            el.addEventListener('change', () => loadQuestions());
        });

        function loadQuestions(page = 1) {
            questionsList.innerHTML = `
                <div class="p-4 text-center text-zinc-500 dark:text-zinc-400">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    Carregando questões...
                </div>
            `;
            questionsPagination.innerHTML = ''; // Limpar paginação enquanto carrega

            const params = new URLSearchParams({
                page,
                search: searchInput.value,
                category_id: categoryFilter.value,
                question_type: typeFilter.value,
                sort_by: sortFilter.value,
                selected_questions: selectedQuestions.join(','),
                test_type: testTypeSelect.value
            });

            fetch(`{{ route('admin.tests.questions') }}?${params}`, {
                method: 'GET',
                headers: { 'Accept': 'application/json', 'X-Requested-With': 'XMLHttpRequest' }
            })
            .then(r => r.json())
            .then(data => {
                if (data && data.html) {
                    questionsList.innerHTML = data.html;
                    availableCount.textContent = data.total;
                    renderPagination(data.current_page, data.has_more_pages);
                } else {
                    questionsList.innerHTML = `<div class="p-4 text-center text-red-500"><p>Erro ao carregar questões.</p></div>`;
                    questionsPagination.innerHTML = '';
                }
            })
            .catch(() => {
                questionsList.innerHTML = `<div class="p-4 text-center text-red-500"><p>Erro ao carregar questões.</p></div>`;
                questionsPagination.innerHTML = '';
            });
        }

        // Adicionar evento de clique na paginação
        questionsPagination.addEventListener('click', function(e) {
            const paginationBtn = e.target.closest('.pagination-btn');
            if (paginationBtn) {
                const page = paginationBtn.dataset.page;
                loadQuestions(parseInt(page));
            }
        });

        // Remover o antigo evento de paginação do questionsList e deixar apenas para adicionar questões
        questionsList.addEventListener('click', function(e) {
            const btn = e.target.closest('.add-question-btn');
            if (!btn) return;

            const questionItem = btn.closest('.question-item');
            if (questionItem) {
                addQuestionToSelected(questionItem);
                loadQuestions(); 
            }
        });

        selectedList.addEventListener('click', function(e) {
            const btn = e.target.closest('.remove-question-btn');

            if (!btn) return;

            const item = btn.closest('.selected-question-item');

            if (item) removeQuestion(item.dataset.id);
        });

        function addQuestionToSelected(questionItem) {
            const questionId = questionItem.dataset.id;

            if (selectedQuestions.includes(questionId)) return;
            selectedQuestions.push(questionId);
            updateSelectedQuestionsInputs();
            
            const content = questionItem.querySelector('.flex-col').cloneNode(true);

            const selectedItem = document.createElement('div');

            selectedItem.className = 'selected-question-item p-3 border border-zinc-200 dark:border-zinc-700 rounded-lg bg-gray-50 dark:bg-zinc-800/50 flex items-center justify-between gap-4 mb-2 relative';
            selectedItem.dataset.id = questionId;

            const removeBtn = document.createElement('button');

            removeBtn.type = 'button';
            removeBtn.className = 'remove-question-btn h-8 w-8 bg-red-500 text-white rounded-full hover:bg-red-600 flex items-center justify-center flex-shrink-0';
            removeBtn.innerHTML = '<i class="fas fa-minus"></i>';
            selectedItem.appendChild(content);
            selectedItem.appendChild(removeBtn);
            
            selectedList.querySelector('.selected-questions-empty')?.remove();
            selectedList.appendChild(selectedItem);
            selectedCount.textContent = selectedQuestions.length;
        }
        
        function removeQuestion(questionId) {
            selectedQuestions = selectedQuestions.filter(id => id !== questionId);
            updateSelectedQuestionsInputs();
            selectedList.querySelector(`.selected-question-item[data-id="${questionId}"]`)?.remove();
            if (selectedQuestions.length === 0 && !selectedList.querySelector('.selected-questions-empty')) {
                const emptyDiv = document.createElement('div');
                emptyDiv.className = 'p-4 text-center text-zinc-500 dark:text-zinc-400 selected-questions-empty';
                emptyDiv.innerHTML = '<p>Nenhuma questão selecionada.</p>';
                selectedList.appendChild(emptyDiv);
            }
            selectedCount.textContent = selectedQuestions.length;
            setTimeout(() => loadQuestions(), 100);
        }
        
        function updateSelectedQuestionsInputs() {
            questionsContainer.innerHTML = '';

            if (selectedQuestions.length > 0) {
                selectedQuestions.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'questions[]';
                    input.value = id;
                    questionsContainer.appendChild(input);
                });
            }
        }

        function debounce(fn, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => fn.apply(this, args), wait);
            };
        }

        loadQuestions();

        // Configurações de Tentativas
        const maxAttemptsSelect = document.getElementById('max_attempts');
        const unlimitedAttemptsCheckbox = document.querySelector('input[name="allow_unlimited_attempts"]');

        // Controlar tentativas ilimitadas
        function toggleUnlimitedAttempts() {
            if (unlimitedAttemptsCheckbox.checked) {
                maxAttemptsSelect.disabled = true;
                maxAttemptsSelect.classList.add('opacity-50');
            } else {
                maxAttemptsSelect.disabled = false;
                maxAttemptsSelect.classList.remove('opacity-50');
            }
        }

        // Event listeners
        unlimitedAttemptsCheckbox.addEventListener('change', toggleUnlimitedAttempts);

        // Garantir que max_attempts seja enviado no formulário
        document.querySelector('form').addEventListener('submit', function(e) {
            if (unlimitedAttemptsCheckbox.checked) {
                // Temporariamente habilitar o campo para que seja enviado
                maxAttemptsSelect.disabled = false;
                // Definir um valor padrão para tentativas ilimitadas
                maxAttemptsSelect.value = '1'; // Valor será ignorado no backend quando unlimited está marcado
            }
        });

        // Inicializar estado
        toggleUnlimitedAttempts();
    });
    </script>
@endpush