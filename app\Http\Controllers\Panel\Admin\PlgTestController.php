<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

use Exception;
use App\Models\PlgModule;
use App\Models\PlgTest;
use App\Models\PlgQuestion;
use App\Models\PlgCategories;
use App\Models\PlgCourse;

class PlgTestController extends Controller
{
    /**
     * Exibe a página de listagem de testes
     */
    public function index()
    {
        return view('panel.admin.tests.index', [
            'columns' => $this->getColumns(),
            'id' => 'tests-table',
            'ajaxUrl' => route('admin.tests.data'),
            'filters' => $this->getFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    /**
     * Retorna os dados dos testes para o DataTables
     */
    public function getData()
    {
        $user = Auth::user();

        $query = PlgTest::with(['module'])->select('plg_tests.*')->orderBy('plg_tests.id', 'desc');

        // Filtro simples: professores veem apenas seus testes
        if ($user->role === 'teacher') {
            $query->where('user_id', $user->id);
        }
        // super_admin vê tudo
        
        return DataTables::of($query)
            ->addColumn('module_name', function($test) {
                return $test->module ? $test->module->title : 'Sem módulo';
            })
            ->addColumn('questions_count', function($test) {
                return is_array($test->questions) ? count($test->questions) : 0;
            })
            ->addColumn('test_type', function($test) {
                $colors = [
                    'quiz' => 'bg-red-500',
                    'challenge' => 'bg-orange-500',
                    'exam' => 'bg-purple-500'
                ];
                $labels = [
                    'quiz' => 'Quiz',
                    'challenge' => 'Desafio',
                    'exam' => 'Exame'
                ];
                $color = $colors[$test->test_type] ?? 'bg-gray-500';
                $label = $labels[$test->test_type] ?? 'Indefinido';
                return '<span class="badge ' . $color . ' text-white px-2 py-1 rounded">' . $label . '</span>';
            })
            ->editColumn('active', function($test) {
                return $test->active
                    ? '<span class="badge bg-success text-white px-2 py-1 rounded">Ativo</span>'
                    : '<span class="badge bg-danger text-white px-2 py-1 rounded">Inativo</span>';
            })
            ->addColumn('actions', function($test) {
                return view('panel.admin.includes.table-actions', [
                    'actions' => $this->getTestActions($test)
                ])->render();
            })
            ->filterColumn('module_name', function($query, $keyword) {
                $query->whereHas('module', function($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%");
                });
            })
            ->rawColumns(['actions', 'test_type', 'active'])
            ->make(true);
    }
    
    /**
     * Retorna as ações disponíveis para cada teste
     */
    private function getTestActions($test)
    {
        return [
            [
                'type' => 'edit',
                'route' => route('admin.tests.edit', $test->id),
                'title' => 'Editar teste'
            ],
            [
                'type' => 'show',
                'route' => route('admin.tests.show', $test->id),
                'title' => 'Visualizar teste'
            ],
            [
                'type' => 'duplicate',
                'route' => route('admin.tests.duplicate', $test->id),
                'title' => 'Duplicar teste'
            ],
            [
                'type' => 'delete',
                'route' => route('admin.tests.destroy', $test->id),
                'title' => 'Excluir teste',
                'confirm_message' => 'Tem certeza que deseja excluir este teste?'
            ]
        ];
    }
    
    /**
     * Exibe o formulário para criar um novo teste
     */
    public function create()
    {
        $courses = PlgCourse::orderBy('title')->get();
        $modules = PlgModule::orderBy('title')->get();
        $categories = PlgCategories::orderBy('title')->get();
        
        return view('panel.admin.tests.create', compact('courses', 'modules', 'categories'));
    }
    
    /**
     * Armazena um novo teste no banco de dados
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'course_id' => 'required|exists:plg_courses,id',
            'module_id' => 'required|exists:plg_modules,id',
            'test_type' => 'required|in:quiz,challenge,exam',
            'questions' => 'array|required',
            'questions.*' => 'exists:plg_questions,id',
            // Campos de configuração simplificados
            'max_attempts' => 'required_unless:allow_unlimited_attempts,1|integer|min:1|max:100',
            'allow_unlimited_attempts' => 'boolean',
        ]);

        DB::beginTransaction();

        try {
            // Verificar se o módulo pertence ao curso selecionado
            $module = PlgModule::where('id', $request->module_id)
                ->where('course_id', $request->course_id)
                ->first();

            if (!$module) {
                throw new \Exception('O módulo selecionado não pertence ao curso escolhido.');
            }

            $user = Auth::user();

            // Preparar questões no formato JSON
            $questionsData = [];
            if ($request->has('questions')) {
                foreach ($request->questions as $index => $questionId) {
                    $questionsData[] = [
                        'id' => $questionId,
                        'order' => $index
                    ];
                }
            }

            $test = PlgTest::create([
                'company_id' => $user->company_id,
                'title' => $request->name, // Usar 'title' em vez de 'name'
                'description' => $request->description ?? '',
                'module_id' => $request->module_id,
                'test_type' => $request->test_type, // Adicionar test_type
                'questions' => $questionsData, // Armazenar questões como JSON
                'max_attempts' => $request->allow_unlimited_attempts ? -1 : $request->max_attempts,
                'allow_unlimited_attempts' => $request->has('allow_unlimited_attempts'),
                'passing_score' => $request->passing_score ?? 70.00,
                'active' => $request->has('active'),
                // Valores padrão para campos removidos da interface
                'cooldown_hours' => 0, // Sem tempo de espera entre tentativas
                'time_limit_minutes' => null, // Sem limite de tempo
                'auto_abandon_inactive_minutes' => 30, // Valor padrão
                'show_activity_warning_minutes' => 25, // Valor padrão
                'allow_resume' => true, // Permitir retomar por padrão
                'randomize_questions' => true, // Randomizar questões por padrão
                'randomize_alternatives' => true, // Randomizar alternativas por padrão
            ]);



            DB::commit();
            
            return redirect()
                ->route('admin.tests.index')
                ->with('success', 'Teste criado com sucesso!');
                
        } catch (Exception $e) {
            DB::rollBack();
            return back()
                ->with('error', 'Erro ao criar teste: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Exibe o formulário para editar um teste
     */
    public function edit($id)
    {
        $test = PlgTest::with(['module.course'])->findOrFail($id);
        $courses = PlgCourse::orderBy('title')->get();
        $modules = PlgModule::where('course_id', $test->module->course_id)->orderBy('title')->get();

        // As questões agora estão armazenadas como JSON no campo 'questions'
        $selectedQuestions = [];
        if ($test->questions && is_array($test->questions)) {
            // Buscar as questões pelos IDs armazenados no JSON
            $questionIds = array_column($test->questions, 'id');
            $selectedQuestions = PlgQuestion::whereIn('id', $questionIds)->get();
        }

        $categories = PlgCategories::orderBy('title')->get();

        return view('panel.admin.tests.create', compact('test', 'courses', 'modules', 'selectedQuestions', 'categories'));
    }
    
    /**
     * Atualiza um teste no banco de dados
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'course_id' => 'required|exists:plg_courses,id',
            'module_id' => 'required|exists:plg_modules,id',
            'test_type' => 'required|in:quiz,challenge,exam',
            'questions' => 'array|required',
            'questions.*' => 'exists:plg_questions,id',
            // Campos de configuração simplificados
            'max_attempts' => 'required_unless:allow_unlimited_attempts,1|integer|min:1|max:100',
            'allow_unlimited_attempts' => 'boolean',
        ]);

        DB::beginTransaction();

        try {
            // Verificar se o módulo pertence ao curso selecionado
            $module = PlgModule::where('id', $request->module_id)
                ->where('course_id', $request->course_id)
                ->first();

            if (!$module) {
                throw new \Exception('O módulo selecionado não pertence ao curso escolhido.');
            }

            // Preparar questões no formato JSON
            $questionsData = [];
            if ($request->has('questions')) {
                foreach ($request->questions as $index => $questionId) {
                    $questionsData[] = [
                        'id' => $questionId,
                        'order' => $index
                    ];
                }
            }

            $test = PlgTest::findOrFail($id);
            $test->update([
                'title' => $request->name, // Usar 'title' em vez de 'name'
                'description' => $request->description ?? '',
                'module_id' => $request->module_id,
                'test_type' => $request->test_type, // Adicionar test_type
                'questions' => $questionsData, // Armazenar questões como JSON
                'max_attempts' => $request->allow_unlimited_attempts ? -1 : $request->max_attempts,
                'allow_unlimited_attempts' => $request->has('allow_unlimited_attempts'),
                'passing_score' => $request->passing_score ?? 70.00,
                'active' => $request->has('active'),
                // Valores padrão para campos removidos da interface
                'cooldown_hours' => 0, // Sem tempo de espera entre tentativas
                'time_limit_minutes' => null, // Sem limite de tempo
                'auto_abandon_inactive_minutes' => 30, // Valor padrão
                'show_activity_warning_minutes' => 25, // Valor padrão
                'allow_resume' => true, // Permitir retomar por padrão
                'randomize_questions' => true, // Randomizar questões por padrão
                'randomize_alternatives' => true, // Randomizar alternativas por padrão
            ]);

            DB::commit();
            
            return redirect()
                ->route('admin.tests.index')
                ->with('success', 'Teste atualizado com sucesso!');
                
        } catch (Exception $e) {
            DB::rollBack();
            return back()
                ->with('error', 'Erro ao atualizar teste: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Remove um teste
     */
    public function destroy($id)
    {
        try {
            $test = PlgTest::findOrFail($id);
            $test->delete();
            
            return redirect()
            ->back()
            ->with('success', 'Teste excluído com sucesso!');
           
        } catch (Exception $e) {
            return redirect()
            ->back()
            ->with('error', 'Erro ao excluir teste: ' . $e->getMessage());
        }
    }

    /**
     * Exibe os detalhes de um teste específico
     */
    public function show($id)
    {
        $test = PlgTest::with(['module'])->findOrFail($id);

        // Buscar as questões pelos IDs armazenados no JSON
        $questions = [];
        if ($test->questions && is_array($test->questions)) {
            $questionIds = array_column($test->questions, 'id');
            $questions = PlgQuestion::whereIn('id', $questionIds)->get();
        }

        return view('panel.admin.tests.show', compact('test', 'questions'));
    }
    
    /**
     * Retorna as colunas para o DataTables
     */
    private function getColumns()
    {
        return [
            [
                'data' => 'id',
                'name' => 'plg_tests.id',
                'label' => 'ID',
                'width' => '60px'
            ],
            [
                'data' => 'title',
                'name' => 'plg_tests.title',
                'label' => 'Título',
                'width' => '250px'
            ],
            [
                'data' => 'module_name',
                'name' => 'module_name',
                'label' => 'Módulo',
                'width' => '200px',
                'searchable' => true,
                'orderable' => false
            ],
            [
                'data' => 'test_type',
                'name' => 'test_type',
                'label' => 'Tipo',
                'width' => '120px',
                'searchable' => false,
                'orderable' => false
            ],
            [
                'data' => 'questions_count',
                'name' => 'questions_count',
                'label' => 'Questões',
                'width' => '90px',
                'searchable' => false,
                'orderable' => false
            ],
            [
                'data' => 'active',
                'name' => 'plg_tests.active',
                'label' => 'Status',
                'width' => '100px'
            ],
            [
                'data' => 'created_at',
                'name' => 'plg_tests.created_at',
                'label' => 'Criado em',
                'width' => '150px'
            ],
            [
                'data' => 'actions',
                'name' => 'actions',
                'label' => 'Ações',
                'width' => '80px',
                'searchable' => false,
                'orderable' => false
            ]
        ];
    }

    /**
     * Retorna os filtros para o DataTables
     */
    private function getFilters()
    {
        return [
            [
                'label' => 'Status',
                'column' => 'active',
                'options' => [
                    ['value' => '', 'label' => 'Todos'],
                    ['value' => '1', 'label' => 'Ativo'],
                    ['value' => '0', 'label' => 'Inativo']
                ]
            ]
        ];
    }

    /**
     * Retorna questões filtradas e paginadas para o formulário de criação/edição de testes
     */
    public function getFilteredQuestions(Request $request)
    {
        try {
            $query = PlgQuestion::query()
                ->with('categories')
                ->when($request->search, function($q) use ($request) {
                    $q->where('question', 'like', "%{$request->search}%");
                })
                ->when($request->category_id, function($q) use ($request) {
                    $q->whereHas('categories', function($q) use ($request) {
                        $q->where('plg_categories.id', $request->category_id);
                    });
                })
                ->when($request->question_type, function($q) use ($request) {
                    $q->where('question_type', $request->question_type);
                })
                ->when($request->test_type, function($q) use ($request) {
                    switch ($request->test_type) {
                        case 'challenge':
                            $q->where('free', true);
                            break;
                        case 'exam':
                            $q->where('free', false);
                            break;
                        // case 'quiz': não precisa filtrar, aceita ambos
                    }
                })
                ->when($request->selected_questions, function($q) use ($request) {
                    $selectedIds = explode(',', $request->selected_questions);
                    if (!empty($selectedIds[0])) {
                        $q->whereNotIn('id', $selectedIds);
                    }
                });

            if ($request->sort_by === 'oldest') {
                $query->orderBy('created_at', 'asc');
            } else {
                $query->orderBy('created_at', 'desc');
            }

            // Primeiro, obter o total de questões que correspondem aos filtros
            $total = $query->count();

            // Depois, obter a página atual com a paginação
            $questions = $query->paginate(10);

            return response()->json([
                'html' => view('panel.admin.tests.partials._questions_list', compact('questions'))->render(),
                'total' => $total,
                'current_page' => $questions->currentPage(),
                'has_more_pages' => $questions->hasMorePages()
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao carregar questões: ' . $e->getMessage());
            return response()->json([
                'error' => 'Erro ao carregar questões: ' . $e->getMessage()
            ], 500);
        }
    }

    public function duplicate(PlgTest $test)
    {
        try {
            DB::beginTransaction();

            // Criar uma cópia do teste
            $newTest = $test->replicate();
            $newTest->title = $test->title . ' (Cópia)';
            $newTest->active = false;
            $newTest->save();

            // As questões já são copiadas automaticamente pelo replicate() pois estão em JSON

            DB::commit();

            return redirect()
                ->route('admin.tests.edit', ['test' => $newTest])
                ->with('success', 'Teste duplicado com sucesso!');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()
                ->route('admin.tests.index')
                ->with('error', 'Erro ao duplicar o teste: ' . $e->getMessage());
        }
    }
} 