@extends('layouts.panel')

@section('title', 'Editando módulo ' . $module->title)
@section('page_title', 'Editando módulo ' . $module->title)

@push('styles')
    @vite(['resources/css/quill-editor.css'])
@endpush

@section('content')
<div class="animate-fade-in space-y-6">
    <!-- Seção Editar Módulo -->
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Editar Módulo',
            'description' => 'Edite as informações deste módulo.',
            'actions' => [
                [
                    'route' => route('admin.modules.index'),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ]
            ]
        ])

        <div class="p-6">
            @include('panel.admin.includes.alerts')

            <form action="{{ route('admin.modules.update', $module->id) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')
                
                <div class="space-y-6">
                    <!-- Curso -->
                    <div>
                        <label for="course_id" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Curso <span class="text-red-500">*</span>
                        </label>
                        <select name="course_id" id="course_id" 
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('course_id') border-red-500 @enderror"
                                required>
                            <option value="">Selecione um curso</option>
                            @foreach ($courses as $course)
                                <option value="{{ $course->id }}"
                                    {{ old('course_id', $module->course->id ?? null) == $course->id ? 'selected' : '' }}>
                                    {{ $course->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('course_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                
                    <!-- Título -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Título do Módulo <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" id="title" value="{{ old('title', $module->title) }}"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('title') border-red-500 @enderror"
                            placeholder="Digite o título do módulo" required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>




                            <!-- Preço -->
                        <div class="md:col-span-1">
                            <label for="price" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                Preço do Módulo <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="price" id="price" placeholder="R$ 0,00" value="{{ old('price', number_format($module->price, 2, ',', '.')) }}"
                                class="money w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('price') border-red-500 @enderror"
                                placeholder="Digite o título do módulo">
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Duração do Acesso -->
                        <div class="md:col-span-1">
                            <label for="duration_months" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                Duração do Acesso
                            </label>
                            <select name="duration_months" id="duration_months"
                                class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('duration_months') border-red-500 @enderror">
                                <option value="" {{ old('duration_months', $module->duration_months) === null ? 'selected' : '' }}>Vitalício</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ old('duration_months', $module->duration_months) == $i ? 'selected' : '' }}>
                                        {{ $i }} {{ $i == 1 ? 'mês' : 'meses' }}
                                    </option>
                                @endfor
                            </select>
                            @error('duration_months')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                                Deixe vazio para acesso vitalício
                            </p>
                        </div>





        

                  <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">

                    <!-- Ordem -->
                    <div class="md:col-span-1 mb-0">
                        <label for="order" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Ordem
                        </label>
                        <input type="number" name="order" id="order" value="{{ old('order', $module->order) }}"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('order') border-red-500 @enderror"
                            min="0">
                        @error('order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

   
                    <!-- Grástis ou Não -->
                     <div class="md:col-span-2 mt-6">
                        <label class="flex items-center gap-2">
                            <input type="checkbox" name="is_free" value="1" class="sr-only peer" {{ old('is_free', isset($module) ? $module->is_free : false) ? 'checked' : '' }}>
                            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary"></div>
                            <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Módulo Grátis</span>
                        </label>
                    </div>

                    <!-- Status -->
                     <div class="md:col-span-2 mt-6">
                        <label class="flex items-center gap-2">
                            <input type="checkbox" name="active" value="1" class="sr-only peer" {{ old('is_free', isset($module) ? $module->active : false) ? 'checked' : '' }}>
                            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary"></div>
                            <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Módulo ativo</span>
                        </label>
                    </div>

                </div> 



                        @error('active')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    
                



                    <!-- Descrição -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Descrição <span class="text-red-500">*</span>
                        </label>
                        <div id="description-editor" class="editor" data-input="description"
                            data-placeholder="Digite uma descrição para o módulo..."></div>
                        <input type="hidden" name="description" id="description"
                            value="{{ old('description', $module->description) }}" required>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Imagem de Capa -->
                    <div class="media-selector">

                    <div class="flex justify-between items-center mb-2">
                   
                               <button type="button" 
                                class="{{ isset($module) && $module->hasMedia('thumbnail') ? 'hidden' : '' }} open-media-modal w-full px-4 py-8 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg hover:border-trends-primary dark:hover:border-trends-primary transition-colors group"
                               data-mediable-type="App\Models\PlgModule"
                                        data-tag="thumbnail"
                                        data-input-id="module_thumbnail"
                                        data-preview-id="thumbnail-preview" 
                                        data-multiple="false">
                                <div class="text-center">
                                    <i class="fas fa-image text-2xl text-zinc-400 dark:text-zinc-600 group-hover:text-trends-primary transition-colors"></i>
                                    <p class="mt-2 text-sm text-zinc-500 dark:text-zinc-400 group-hover:text-trends-primary transition-colors">
                                        Clique para selecionar uma imagem para a capa do Módulo
                                    </p>
                                </div>
                            </button>

                         </div>
                            <input type="hidden" name="thumbnail" class="media-input" data-preview-id="thumbnail-preview" id="module_thumbnail" value="{{ old('thumbnail', isset($module) && $module->hasMedia('thumbnail') ? $module->firstMedia('thumbnail')->id : '') }}">

                                <div class="media-preview {{ isset($module) && $module->hasMedia('thumbnail') ? '' : 'hidden' }}" id="thumbnail-preview">
                                    @if (isset($module) && $module->hasMedia('thumbnail'))
                                        @php
                                            $thumbnailMedia = $module->firstMedia('thumbnail');
                                            $thumbnailUrl = route('media.serve', ['path' => $thumbnailMedia->getDiskPath()]) . '?w=250&fm=webp';
                                        @endphp
                                            <div class="relative w-64">
                                            <img src="{{ $thumbnailUrl }}" alt="{{ $module->title ?? 'Thumbnail do Módulo' }}" class="w-full h-32 object-cover rounded-lg">
                                                <button type="button" class="preview-remove-btn absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600">
                                                    <i class="fas fa-times text-xs"></i>
                                                </button>
                                            </div>
                                            <p class="text-sm text-zinc-500 mt-2 truncate">{{ $thumbnailMedia->filename }}</p>
                                    @endif
                                </div>

                            
                            <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendado: 1280x720px</p>
                        </div>
                    
                    </div>
                
                <!-- Botões -->
                <div class="flex justify-end gap-4">
                        <a href="{{ route('admin.modules.index') }}" 
                        class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                        <i class="fas fa-times"></i>
                        <span>Cancelar</span>
                        </a>
                        <button type="submit" 
                        class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                        <i class="fas fa-save"></i>
                        <span>Atualizar Módulo</span>
                        </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Seção Conteúdos do Módulo -->
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Conteúdos do Módulo',
            'description' => 'Gerencie todos os conteúdos disponíveis neste módulo.',
            'actions' => [
                [
                    'route' => route('admin.modules.contents.create', $module->id),
                    'text' => 'Adicionar Conteúdo ao módulo ' . $module->title,
                    'icon' => 'fas fa-plus',
                    'class' => 'bg-trends-primary text-white',
                    'hover_class' => 'bg-trends-primary/90'
                ]
            ]
        ])

        @include('panel.admin.includes.datatable', [
            'id' => $id,
            'columns' => $columns,
            'ajaxUrl' => $ajaxUrl,
            'filters' => $filters ?? [],
            'clearFiltersBtn' => $clearFiltersBtn ?? false
        ])
    </div>

    <!-- Seção Testes do Módulo -->
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Testes do Módulo',
            'description' => 'Visualize os testes disponíveis neste módulo.',
            'actions' => [
                [
                    'route' => route('admin.tests.create', ['module_id' => $module->id]),
                    'text' => 'Adicionar Teste',
                    'icon' => 'fas fa-plus',
                    'class' => 'bg-trends-primary text-white',
                    'hover_class' => 'bg-trends-primary/90'
                ]
            ]
        ])

        <div class="p-6">
            @if($module->tests->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($module->tests as $test)
                        <div class="bg-zinc-50 dark:bg-zinc-800/50 rounded-lg p-4 border border-zinc-200 dark:border-zinc-700">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="text-base font-medium text-zinc-900 dark:text-zinc-100">
                                        {{ $test->name }}
                                    </h3>
                                    <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
                                        {{ is_array($test->questions) ? count($test->questions) : 0 }} questões
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.tests.edit', $test->id) }}" 
                                       class="text-zinc-500 dark:text-zinc-400 hover:text-trends-primary dark:hover:text-trends-primary"
                                       title="Editar teste">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.tests.show', $test->id) }}"
                                       class="text-zinc-500 dark:text-zinc-400 hover:text-trends-primary dark:hover:text-trends-primary"
                                       title="Visualizar teste">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="mt-3 flex items-center space-x-3 text-sm">
                                <span class="inline-flex items-center px-2 py-1 rounded-md {{ $test->active ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400' }}">
                                    <i class="fas {{ $test->active ? 'fa-check' : 'fa-times' }} mr-1"></i>
                                    {{ $test->active ? 'Ativo' : 'Inativo' }}
                                </span>
                                <span class="text-zinc-500 dark:text-zinc-400">
                                    Tipo: {{ $test->test_type }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6">
                    <div class="text-zinc-500 dark:text-zinc-400">
                        <i class="fas fa-clipboard-question text-4xl mb-2"></i>
                        <p>Nenhum teste cadastrado para este módulo.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection 

@push('scripts')
    @vite(['resources/js/quill-editor.js'])
@endpush