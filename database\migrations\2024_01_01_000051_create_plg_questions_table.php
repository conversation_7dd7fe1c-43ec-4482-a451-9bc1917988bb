<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id')->nullable(); // Quem criou a questão

            $table->boolean('free')->default(false);
            $table->boolean('imported')->default(false);
            
            $table->enum('question_type', ['single', 'multiple', 'essay'])->default('single');
            $table->string('answer_format')->default('numeric');
            
            $table->text('question');
            $table->text('essay_answer')->nullable();
            $table->text('image')->nullable(); // Campo para imagem da questão

            $table->timestamps();
            $table->softDeletes();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');
            
            // Índices para performance
            $table->index(['company_id', 'question_type']); // Multi-tenancy + tipo
            $table->index(['free']);
            $table->index(['company_id', 'free']); // Multi-tenancy + gratuito
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_questions');
    }
};
