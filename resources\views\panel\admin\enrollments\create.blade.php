@extends('layouts.panel')

@section('title', isset($enrollment) ? 'Editar Matrícula' : 'Nova Matrícula')
@section('page_title', isset($enrollment) ? 'Editar Matrícula' : 'Nova Matrícula')

@section('content')
    <div class="animate-fade-in">
        <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-t-lg shadow-lg">
            @include('panel.admin.includes.page-header', [
                'title' => isset($enrollment) ? 'Editar Matrícula' : 'Nova Matrícula',
                'description' => isset($enrollment) ? 'Edite os dados da matrícula.' : 'Matricule um estudante em um módulo.',
                'actions' => [
                    [
                        'route' => route('admin.enrollments.index'),
                        'text' => 'Voltar',
                        'icon' => 'fas fa-arrow-left',
                        'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                        'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                    ]
                ]
            ])

            <form action="{{ isset($enrollment) ? route('admin.enrollments.update', $enrollment->id) : route('admin.enrollments.store') }}" 
                    method="POST" class="space-y-6">
                @csrf
                @if(isset($enrollment))
                    @method('PUT')
                @endif

                <div class="px-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Estudante -->
                        <div>
                            <label for="student_id" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Estudante *
                            </label>
                            <select name="student_id" id="student_id"
                                    class="choices-select"
                                    data-placeholder="Selecione um estudante"
                                    {{ isset($enrollment) ? 'disabled' : 'required' }}>
                                <option value="">Selecione um estudante</option>
                                @foreach($students as $student)
                                    <option value="{{ $student->id }}" 
                                            {{ (isset($enrollment) && $enrollment->student_id == $student->id) || old('student_id') == $student->id ? 'selected' : '' }}>
                                        {{ $student->name }} ({{ $student->email }})
                                    </option>
                                @endforeach
                            </select>
                            <a href="{{route('admin.students.create') }}" class="text-sm text-blue-600 hover:underline">
                                + Cadastrar Novo Estudante
                            </a>
                            @if(isset($enrollment))
                                <input type="hidden" name="student_id" value="{{ $enrollment->student_id }}">
                            @endif
                            @error('student_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Curso -->
                        <div>
                            <label for="course_id" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Curso *
                            </label>
                            <select name="course_id" id="course_id"
                                    class="choices-select"
                                    data-placeholder="Selecione um curso"
                                    {{ isset($enrollment) ? 'disabled' : '' }}>
                                <option value="">Selecione um curso</option>
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" 
                                            {{ (isset($enrollment) && $enrollment->module && $enrollment->module->course_id == $course->id) ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Módulo -->
                        <div>
                            <label for="module_id" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Módulo *
                            </label>
                            <select name="module_id" id="module_id"
                                    class="choices-select"
                                    data-placeholder="Selecione um módulo"
                                    {{ isset($enrollment) ? 'disabled' : 'required' }}>
                                <option value="">Selecione um módulo</option>
                                @if(isset($enrollment) && $enrollment->module)
                                    <option value="{{ $enrollment->module->id }}" selected>
                                        {{ $enrollment->module->title }}
                                    </option>
                                @endif
                            </select>
                            @if(isset($enrollment))
                                <input type="hidden" name="module_id" value="{{ $enrollment->module_id }}">
                            @endif
                            @error('module_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Status *
                            </label>
                            <select name="status" id="status" 
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-800 dark:text-white"
                                    required>
                                <option value="active" {{ (isset($enrollment) && $enrollment->status == 'active') || old('status') == 'active' ? 'selected' : '' }}>
                                    Ativo
                                </option>
                                <option value="expired" {{ (isset($enrollment) && $enrollment->status == 'expired') || old('status') == 'expired' ? 'selected' : '' }}>
                                    Expirado
                                </option>
                                <option value="canceled" {{ (isset($enrollment) && $enrollment->status == 'canceled') || old('status') == 'canceled' ? 'selected' : '' }}>
                                    Cancelado
                                </option>
                            </select>
                            @error('status')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Professor Responsável (apenas para super_admin) -->
                        @if(!isset($enrollment) && Auth::user()->role === 'super_admin')
                        <div>
                            <label for="assigned_teacher_id" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Professor Responsável *
                            </label>
                            <select name="assigned_teacher_id" id="assigned_teacher_id"
                                    class="choices-select"
                                    data-placeholder="Selecione um professor"
                                    required>
                                <option value="">Selecione um professor</option>
                                @foreach($teachers as $teacher)
                                    <option value="{{ $teacher->id }}" {{ old('assigned_teacher_id') == $teacher->id ? 'selected' : '' }}>
                                        {{ $teacher->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('assigned_teacher_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        @endif

                        @if(!isset($enrollment) && Auth::user()->role === 'teacher')
                        <!-- Campo oculto para teacher - sempre será teacher_created -->
                        <input type="hidden" name="enrollment_method" value="teacher_created">
                        <input type="hidden" name="assigned_teacher_id" value="{{ Auth::user()->id }}">
                        @elseif(!isset($enrollment) && Auth::user()->role === 'super_admin')
                        <!-- Campo oculto para super_admin - sempre será admin_created -->
                        <input type="hidden" name="enrollment_method" value="admin_created">
                        @endif

                        <!-- Status de Aprovação -->
                        <div>
                            <label for="approval_status" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Status de Aprovação *
                            </label>
                            <select name="approval_status" id="approval_status"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-800 dark:text-white"
                                    required>
                                <option value="pending" {{ (isset($enrollment) && $enrollment->approval_status == 'pending') || old('approval_status') == 'pending' ? 'selected' : '' }}>
                                    Pendente
                                </option>
                                <option value="approved" {{ (isset($enrollment) && $enrollment->approval_status == 'approved') || old('approval_status') == 'approved' ? 'selected' : '' }}>
                                    Aprovado
                                </option>
                                <option value="rejected" {{ (isset($enrollment) && $enrollment->approval_status == 'rejected') || old('approval_status') == 'rejected' ? 'selected' : '' }}>
                                    Rejeitado
                                </option>
                            </select>
                            @error('approval_status')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Preço do Módulo -->
                        <div>
                            <label for="module_price_at_time" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Preço do Módulo (R$)
                            </label>
                            <input type="number" name="module_price_at_time" id="module_price_at_time" 
                                    step="0.01" min="0"
                                    value="{{ isset($enrollment) ? $enrollment->module_price_at_time : old('module_price_at_time') }}"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-800 dark:text-white"
                                    placeholder="0.00">
                            <p class="text-xs text-zinc-500 mt-1">Deixe vazio para usar o preço atual do módulo</p>
                            @error('module_price_at_time')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Data de Expiração -->
                        <div>
                            <label for="expires_at" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                                Data de Expiração
                            </label>
                            <input type="date" name="expires_at" id="expires_at" 
                                    value="{{ isset($enrollment) && $enrollment->expires_at ? $enrollment->expires_at->format('Y-m-d') : old('expires_at') }}"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-800 dark:text-white">
                            <p class="text-xs text-zinc-500 mt-1">Deixe vazio para acesso permanente</p>
                            @error('expires_at')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Motivo da Rejeição (apenas se rejeitado) -->
                    @if(isset($enrollment) && $enrollment->approval_status === 'rejected')
                    <div id="rejection_reason_field">
                        <label for="rejection_reason" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                            Motivo da Rejeição
                        </label>
                        <textarea name="rejection_reason" id="rejection_reason" rows="3"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-800 dark:text-white"
                                    placeholder="Descreva o motivo da rejeição...">{{ isset($enrollment) ? $enrollment->rejection_reason : old('rejection_reason') }}</textarea>
                        @error('rejection_reason')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    @endif
                </div>
                
                <!-- Botões -->
                @include('panel.admin.includes.page-footer', [
                    'cancelRoute' => route('admin.enrollments.index'),
                    'cancelText' => 'Cancelar',
                    'submitText' => isset($enrollment) ? 'Atualizar Matrícula' : 'Criar Matrícula'
                ])
            </form>

        </div>
    </div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos DOM
    const courseSelect = document.getElementById('course_id');
    const moduleSelect = document.getElementById('module_id');
    const approvalStatusSelect = document.getElementById('approval_status');
    const assignedTeacherSelect = document.getElementById('assigned_teacher_id');

    @if(Auth::user()->role === 'super_admin')
    // Dados dos cursos por professor
    const teacherCourses = @json($teachers->mapWithKeys(function($teacher) use ($courses) {
        return [$teacher->id => $courses->where('user_id', $teacher->id)->map(function($course) {
            return ['id' => $course->id, 'title' => $course->title];
        })];
    }));

    const allCourses = @json($courses->map(function($course) {
        return ['id' => $course->id, 'title' => $course->title];
    }));
    @endif

    // Configuração padrão do Choice.js
    const choicesConfig = {
        searchEnabled: true,
        searchPlaceholderValue: 'Digite para buscar...',
        noResultsText: 'Nenhum resultado encontrado',
        noChoicesText: 'Nenhuma opção disponível',
        itemSelectText: 'Clique para selecionar',
        removeItemButton: false,
        shouldSort: false
    };

    // Função para atualizar módulos via AJAX
    function updateModules(courseId) {
        if (!moduleSelect) return;

        const moduleChoices = moduleSelect.choicesInstance;
        const loadingOption = [{value: '', label: courseId ? 'Carregando módulos...' : 'Selecione um módulo', disabled: !courseId}];

        // Atualizar interface
        if (moduleChoices) {
            moduleChoices.clearStore();
            moduleChoices.setChoices(loadingOption, 'value', 'label', true);
        } else {
            moduleSelect.innerHTML = `<option value="">${loadingOption[0].label}</option>`;
        }

        if (!courseId) return;

        // Buscar módulos via AJAX
        fetch(`{{ url('panel/admin/enrollments/modules-by-course') }}/${courseId}`)
            .then(response => response.json())
            .then(modules => {
                const moduleOptions = [{value: '', label: 'Selecione um módulo', disabled: false}]
                    .concat(modules.map(m => ({value: m.id.toString(), label: m.title, disabled: false})));

                if (moduleChoices) {
                    moduleChoices.clearStore();
                    moduleChoices.setChoices(moduleOptions, 'value', 'label', true);
                } else {
                    moduleSelect.innerHTML = moduleOptions.map(opt =>
                        `<option value="${opt.value}">${opt.label}</option>`
                    ).join('');
                }
            })
            .catch(() => {
                const errorOption = [{value: '', label: 'Erro ao carregar módulos', disabled: true}];
                if (moduleChoices) {
                    moduleChoices.clearStore();
                    moduleChoices.setChoices(errorOption, 'value', 'label', true);
                } else {
                    moduleSelect.innerHTML = '<option value="">Erro ao carregar módulos</option>';
                }
            });
    }

    @if(Auth::user()->role === 'super_admin')
    // Função para filtrar cursos por professor
    function filterCoursesByTeacher(teacherId) {
        const courseChoices = courseSelect?.choicesInstance;
        if (!courseChoices) return;

        const courses = teacherId ? (teacherCourses[teacherId] || []) : allCourses;
        const courseOptions = [{value: '', label: 'Selecione um curso', disabled: false}]
            .concat(courses.map(c => ({value: c.id, label: c.title, disabled: false})));

        courseChoices.clearStore();
        courseChoices.setChoices(courseOptions, 'value', 'label', true);
        updateModules(''); // Limpar módulos
    }
    @endif

    // Inicializar Choice.js
    function initChoices() {
        if (typeof window.Choices === 'undefined') {
            setTimeout(initChoices, 100);
            return;
        }

        document.querySelectorAll('.choices-select').forEach(element => {
            if (!element.choicesInstance) {
                const placeholder = element.getAttribute('data-placeholder') || 'Selecione uma opção';
                const choices = new window.Choices(element, {...choicesConfig, placeholderValue: placeholder});
                element.choicesInstance = choices;

                // Event listeners específicos
                if (element.id === 'course_id') {
                    element.addEventListener('addItem', e => updateModules(e.detail.value));
                    element.addEventListener('removeItem', () => updateModules(''));
                }
                @if(Auth::user()->role === 'super_admin')
                else if (element.id === 'assigned_teacher_id') {
                    element.addEventListener('addItem', e => filterCoursesByTeacher(e.detail.value));
                    element.addEventListener('removeItem', () => filterCoursesByTeacher(''));
                }
                @endif
            }
        });

        // Fallback para selects sem Choice.js
        if (courseSelect && !courseSelect.choicesInstance) {
            courseSelect.addEventListener('change', e => updateModules(e.target.value));
        }
    }

    // Campo de motivo da rejeição
    if (approvalStatusSelect) {
        approvalStatusSelect.addEventListener('change', function() {
            const rejectionField = document.getElementById('rejection_reason_field');
            if (this.value === 'rejected' && !rejectionField) {
                this.closest('.grid').insertAdjacentHTML('afterend', `
                    <div id="rejection_reason_field">
                        <label for="rejection_reason" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                            Motivo da Rejeição *
                        </label>
                        <textarea name="rejection_reason" id="rejection_reason" rows="3" required
                                  class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-zinc-800 dark:text-white"
                                  placeholder="Descreva o motivo da rejeição..."></textarea>
                    </div>
                `);
            } else if (this.value !== 'rejected' && rejectionField) {
                rejectionField.remove();
            }
        });
    }

    // Inicializar
    initChoices();
});
</script>
@endsection
