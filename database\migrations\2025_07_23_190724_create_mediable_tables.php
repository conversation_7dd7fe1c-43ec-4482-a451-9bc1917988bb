<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Plank\Mediable\Media;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Esta migration cria as tabelas do Laravel Mediable (media e mediables)
     * com todas as colunas necessárias já incluídas.
     */
    public function up(): void
    {
        // Criar tabela media se não existir
        if (!Schema::hasTable('media')) {
            Schema::create('media', function (Blueprint $table) {
                $table->id();
                $table->string('disk', 32);
                $table->string('directory');
                $table->string('filename');
                $table->string('extension', 32);
                $table->string('mime_type', 128);
                $table->string('aggregate_type', 32)->index();
                $table->unsignedInteger('size');
                $table->string('variant_name', 255)->nullable();
                $table->foreignIdFor(Media::class, 'original_media_id')
                    ->nullable()
                    ->constrained('media')
                    ->nullOnDelete();
                $table->text('alt')->nullable();
                $table->timestamps();
                $table->unique(['disk', 'directory', 'filename', 'extension']);
            });
        }

        // Criar tabela mediables se não existir
        if (!Schema::hasTable('mediables')) {
            Schema::create('mediables', function (Blueprint $table) {
                $table->foreignIdFor(Media::class)->constrained('media')->cascadeOnDelete();
                $table->morphs('mediable');
                $table->string('tag')->index();
                $table->string('caption')->nullable();
                $table->json('custom_properties')->nullable();
                $table->unsignedInteger('order')->index();
                $table->primary(['media_id', 'mediable_type', 'mediable_id', 'tag']);
                $table->index(['mediable_id', 'mediable_type']);
            });
        } else {
            // Se a tabela já existe, adicionar colunas que podem estar faltando
            Schema::table('mediables', function (Blueprint $table) {
                if (!Schema::hasColumn('mediables', 'caption')) {
                    $table->string('caption')->nullable()->after('tag');
                }
                if (!Schema::hasColumn('mediables', 'custom_properties')) {
                    $table->json('custom_properties')->nullable()->after('caption');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mediables');
        Schema::dropIfExists('media');
    }

    /**
     * Get the migration connection name.
     */
    public function getConnection()
    {
        return config('mediable.connection_name', parent::getConnection());
    }
};

