<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Plank\Mediable\Mediable;

class PlgModule extends Model
{
    use HasFactory, SoftDeletes, Mediable;

    protected $table = 'plg_modules';

    protected $casts = [
        'active' => 'boolean',
        'is_free' => 'boolean',
        'is_on_sale' => 'boolean',
        'is_new_release' => 'boolean',
        'is_featured' => 'boolean',
        'is_bestseller' => 'boolean',
        'sale_starts_at' => 'datetime',
        'sale_ends_at' => 'datetime',
        'price' => 'decimal:2',
        'price_old' => 'decimal:2',
        'marketing_tags' => 'array',
        'duration_months' => 'integer',
    ];

    /**
     * Accessor para garantir que a descrição sempre retorne HTML limpo
     */
    public function getDescriptionAttribute($value)
    {
        if (empty($value)) {
            return $value;
        }

        // Remove tags vazias comuns do Quill
        $cleaned = preg_replace('/<p><br><\/p>/', '', $value);
        $cleaned = preg_replace('/<p>\s*<\/p>/', '', $cleaned);
        $cleaned = preg_replace('/<p><br\s*\/?><\/p>/', '', $cleaned);
        $cleaned = trim($cleaned);

        if (empty($cleaned)) {
            return '';
        }

        // Se já contém tags HTML válidas, retorna como está
        if (strip_tags($cleaned) !== $cleaned) {
            return $cleaned;
        }

        // Se é texto simples, envolve em <p>
        return '<p>' . htmlspecialchars($cleaned, ENT_QUOTES, 'UTF-8') . '</p>';
    }

    protected $fillable = [
        'company_id',
        'user_id', // Campo adicionado para rastreabilidade
        'duration_months', // Campo adicionado para duração do acesso
        'title',
        'slug',
        'description',
        'order',
        'active',
        'course_id',
        'price',
        'price_old',
        'is_free',
        'is_on_sale',
        'is_new_release',
        'is_featured',
        'is_bestseller',
        'sale_starts_at',
        'sale_ends_at',
        'currency',
        'marketing_description',
        'marketing_tags',
    ];

    /**
     * Relacionamento com o curso.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(PlgCourse::class, 'course_id');
    }

    /**
     * Relacionamento com conteúdos.
     */
    public function contents(): HasMany
    {
        return $this->hasMany(PlgModuleContent::class, 'module_id')
            ->orderBy('order');
    }

    /**
     * Relacionamento com os testes.
     */
    public function tests(): HasMany
    {
        return $this->hasMany(PlgTest::class, 'module_id')
            ->orderBy('id', 'desc');
    }

    /**
     * Relacionamento com estudantes matriculados
     */
    public function enrolledStudents()
    {
        return $this->belongsToMany(PlgStudent::class, 'plg_enrollments', 'module_id', 'student_id')
            ->withTimestamps()
            ->withPivot(['company_id', 'enrolled_at', 'expires_at', 'module_price_at_time', 'status']);
    }

    /**
     * Matrículas neste módulo
     */
    public function enrollments()
    {
        return $this->hasMany(PlgEnrollment::class, 'module_id');
    }

    /**
     * Métodos de Marketing e Preços
     */

    /**
     * Verificar se está em promoção ativa
     */
    public function isOnActivePromotion(): bool
    {
        if (!$this->is_on_sale) {
            return false;
        }

        // Se tem data de início e ainda não começou
        if ($this->sale_starts_at && $this->sale_starts_at->isFuture()) {
            return false;
        }

        // Se tem data de fim e já terminou
        if ($this->sale_ends_at && $this->sale_ends_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Obter preço atual (considerando promoção)
     */
    public function getCurrentPrice(): float
    {
        if ($this->is_free) {
            return 0.00;
        }

        return $this->isOnActivePromotion() ? $this->price : ($this->price_old ?? $this->price);
    }

    /**
     * Obter desconto percentual
     */
    public function getDiscountPercentage(): ?int
    {
        if (!$this->price_old || !$this->isOnActivePromotion()) {
            return null;
        }

        return round((($this->price_old - $this->price) / $this->price_old) * 100);
    }

    /**
     * Verificar se tem desconto
     */
    public function hasDiscount(): bool
    {
        return $this->getDiscountPercentage() > 0;
    }

    /**
     * Obter badges de marketing
     */
    public function getMarketingBadges(): array
    {
        $badges = [];

        if ($this->is_new_release) $badges[] = 'Novo';
        if ($this->is_featured) $badges[] = 'Destaque';
        if ($this->is_bestseller) $badges[] = 'Mais Vendido';
        if ($this->isOnActivePromotion()) $badges[] = 'Promoção';
        if ($this->is_free) $badges[] = 'Gratuito';

        return $badges;
    }

    /**
     * Scopes para Marketing
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOnSale($query)
    {
        return $query->where('is_on_sale', true)
                    ->where(function($q) {
                        $q->whereNull('sale_starts_at')->orWhere('sale_starts_at', '<=', now());
                    })
                    ->where(function($q) {
                        $q->whereNull('sale_ends_at')->orWhere('sale_ends_at', '>=', now());
                    });
    }

    public function scopeNewReleases($query)
    {
        return $query->where('is_new_release', true);
    }

    public function scopeBestsellers($query)
    {
        return $query->where('is_bestseller', true);
    }

    public function scopeFree($query)
    {
        return $query->where('is_free', true);
    }

    public function scopePaid($query)
    {
        return $query->where('is_free', false);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($module) {
            if (empty($module->slug)) {
                $module->slug = Str::slug($module->title);
            }
        });

        static::updating(function ($module) {
            if ($module->isDirty('title') && empty($module->slug)) {
                $module->slug = Str::slug($module->title);
            }
        });
    }

    /**
     * Retorna a duração em meses do módulo
     */
    public function getDurationInMonths(): ?int
    {
        return $this->duration_months;
    }

    /**
     * Verifica se o módulo tem acesso vitalício
     */
    public function isLifetime(): bool
    {
        return is_null($this->duration_months);
    }

    /**
     * Retorna texto formatado da duração
     */
    public function getDurationText(): string
    {
        if ($this->isLifetime()) {
            return 'Acesso vitalício';
        }

        $months = $this->duration_months;
        if ($months === 1) {
            return '1 mês';
        }

        return "{$months} meses";
    }

    /**
     * Calcula data de expiração baseada na data de matrícula
     */
    public function calculateExpiryDate(\Carbon\Carbon $enrolledAt): ?\Carbon\Carbon
    {
        if ($this->isLifetime()) {
            return null; // Sem expiração
        }

        return $enrolledAt->copy()->addMonths($this->duration_months);
    }
}