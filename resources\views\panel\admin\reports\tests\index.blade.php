@extends('layouts.panel')

@section('title', 'Relatórios de Testes')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">Relatórios de Testes</h1>
            <p class="text-zinc-600 dark:text-zinc-400">Análise detalhada do desempenho dos estudantes</p>
        </div>
        
        <div class="flex gap-3">
            <button onclick="refreshDashboard()" 
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>Atualizar
            </button>
        </div>
    </div>

    <!-- Dashboard Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <i class="fas fa-chart-line text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Total de Tentativas</p>
                    <p id="total-attempts" class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Taxa de Aprovação</p>
                    <p id="pass-rate" class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Tempo Médio</p>
                    <p id="avg-time" class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <i class="fas fa-users text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">Estudantes Únicos</p>
                    <p id="unique-students" class="text-2xl font-bold text-zinc-800 dark:text-zinc-100">-</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                    Teste
                </label>
                <select id="test-filter" onchange="updateDashboard()" 
                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                    <option value="">Todos os testes</option>
                    @foreach($tests as $test)
                        <option value="{{ $test->id }}">{{ $test->name }}</option>
                    @endforeach
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">
                    Período
                </label>
                <select id="period-filter" onchange="updateDashboard()" 
                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500/30 dark:bg-zinc-800/50 dark:text-zinc-200">
                    <option value="7">Últimos 7 dias</option>
                    <option value="30" selected>Últimos 30 dias</option>
                    <option value="90">Últimos 90 dias</option>
                    <option value="365">Último ano</option>
                </select>
            </div>

            <div class="flex items-end">
                <button onclick="exportData()" 
                    class="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-download mr-2"></i>Exportar CSV
                </button>
            </div>
        </div>
    </div>

    <!-- Gráfico de Tentativas -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm mb-6">
        <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100 mb-4">
            Tentativas por Dia
        </h3>
        <canvas id="attempts-chart" height="100"></canvas>
    </div>

    <!-- Lista de Testes -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">
                Testes Disponíveis
            </h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-zinc-50 dark:bg-zinc-900/50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Teste
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Tipo
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Tentativas
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Taxa de Aprovação
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                    @foreach($tests as $test)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                        {{ $test->name }}
                                    </div>
                                    <div class="text-sm text-zinc-500 dark:text-zinc-400">
                                        {{ $test->module->course->name }} - {{ $test->module->name }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $test->test_type === 'quiz' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : 
                                       ($test->test_type === 'exam' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' : 
                                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400') }}">
                                    {{ ucfirst($test->test_type) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $test->attempts()->count() }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                @php
                                    $completed = $test->attempts()->where('status', 'completed')->count();
                                    $passed = $test->attempts()->where('status', 'completed')->where('passed', true)->count();
                                    $passRate = $completed > 0 ? round(($passed / $completed) * 100) : 0;
                                @endphp
                                {{ $passRate }}%
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="flex gap-2">
                                    <a href="{{ route('admin.reports.tests.show', $test) }}" 
                                        class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-chart-bar mr-1"></i>Relatório
                                    </a>
                                    <a href="{{ route('admin.reports.tests.export', $test) }}" 
                                        class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300">
                                        <i class="fas fa-download mr-1"></i>Exportar
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    let attemptsChart;

    document.addEventListener('DOMContentLoaded', function() {
        initializeChart();
        updateDashboard();
    });

    function initializeChart() {
        const ctx = document.getElementById('attempts-chart').getContext('2d');
        attemptsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Tentativas',
                    data: [],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    async function updateDashboard() {
        const testId = document.getElementById('test-filter').value;
        const period = document.getElementById('period-filter').value;

        try {
            const response = await fetch(`/panel/admin/reports/tests/dashboard-data?test_id=${testId}&period=${period}`);
            const data = await response.json();

            // Atualizar cards
            document.getElementById('total-attempts').textContent = data.total_attempts;
            document.getElementById('pass-rate').textContent = Math.round(data.pass_rate) + '%';
            document.getElementById('avg-time').textContent = Math.round(data.avg_time_minutes) + ' min';

            // Atualizar gráfico
            const labels = data.attempts_by_day.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const chartData = data.attempts_by_day.map(item => item.count);

            attemptsChart.data.labels = labels;
            attemptsChart.data.datasets[0].data = chartData;
            attemptsChart.update();

        } catch (error) {
            console.error('Erro ao carregar dados:', error);
        }
    }

    function refreshDashboard() {
        updateDashboard();
    }

    function exportData() {
        const testId = document.getElementById('test-filter').value;
        if (testId) {
            window.location.href = `/panel/admin/reports/tests/${testId}/export`;
        } else {
            alert('Selecione um teste específico para exportar.');
        }
    }
</script>
@endpush
