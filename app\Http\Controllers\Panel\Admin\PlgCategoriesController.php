<?php

namespace App\Http\Controllers\Panel\Admin;

use Plank\Mediable\Media;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\PlgCategories;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class PlgCategoriesController extends Controller
{
    public function index()
    {
        return view('panel.admin.categories.index', [
            'columns' => $this->getColumns(),
            'id' => 'categories-table',
            'ajaxUrl' => route('admin.categories.data')
        ]);
    }

    public function getData()
    {
        $user = Auth::user();

        $query = PlgCategories::select('*')->orderBy('id', 'desc');

        // Filtro simples: professores veem apenas suas categorias
        if ($user->role === 'teacher') {
            $query->where('user_id', $user->id);
        }
        // super_admin vê tudo

        return DataTables::of($query)
            ->addColumn('actions', function($category) {
                return view('panel.admin.includes.table-actions', [
                    'actions' => [
                        [
                            'type' => 'edit',
                            'route' => route('admin.categories.edit', $category->id),
                            'title' => 'Editar categoria'
                        ],
                        [
                            'type' => 'delete',
                            'route' => route('admin.categories.destroy', $category->id),
                            'title' => 'Excluir categoria',
                            'confirm_message' => 'Tem certeza que deseja excluir esta categoria?'
                        ]
                    ]
                ])->render();
            })
            ->editColumn('title', function ($category) {
            $media = $category->getMedia('thumbnail')->first();
            return [
                'text' => $category->title,
                'thumbnail' => $media
                    ? route('media.serve', ['path' => $media->getDiskPath()]) . '?w=250&h=150&fit=crop&fm=webp'
                    : null,
            ];
            })
            // ->editColumn('description', function($category) {
            //     $shortDescription = Str::limit(strip_tags($category->description), 100);
            //     return '<div class="text-zinc-500 dark:text-zinc-400 truncate max-w-xs" style="max-width: 260px;">' . $shortDescription . '</div>';
            // })
            ->editColumn('description', function($category) {
            return Str::limit(strip_tags($category->description), 100);
            })
            ->editColumn('active', function($category) {
                return view('panel.admin.categories.partials.status', compact('category'))->render();
            })
            ->rawColumns(['actions', 'title', 'description', 'active'])
            ->make(true);
    }

    public function create()
    {
        return view('panel.admin.categories.create',['thumbnailUrl'=>'']);
    }

    public function store(Request $request)
    {
        $validated = $this->validateRequest($request);
        
        $user = Auth::user();

        $validated += [
            'slug' => Str::slug($validated['title']),
            'company_id' => $user->company_id,
            'user_id' => $user->id, // Auto-preenchimento do user_id
            'order' => $validated['order'] ?? 0,
            'active' => $request->has('active'),
        ];

        $category = PlgCategories::create($validated);

        // Associar a mídia à categoria se houver thumbnail
        if ($request->filled('thumbnail')) {
            $media = Media::find($request->thumbnail);
            if ($media) {
                $category->attachMedia($media, 'thumbnail');
            }
        }

        // Associar mídias da galeria
        if ($request->filled('gallery')) {
            $galleryIds = json_decode($request->gallery, true);
            if (is_array($galleryIds) && !empty($galleryIds)) {
                $galleryMedia = Media::whereIn('id', $galleryIds)->get();
                foreach ($galleryMedia as $media) {
                    $category->attachMedia($media, 'gallery');
                }
            }
        }

        return redirect()->route('admin.categories.index')
            ->with('success', 'Categoria criada com sucesso!');
    }

    public function edit($id)
    {
        $category = PlgCategories::findOrFail($id);
        return view('panel.admin.categories.create', compact('category'));
    }

    public function update(Request $request, $id)
    {
        $category = PlgCategories::findOrFail($id);
        $validated = $this->validateRequest($request);

        $validated += [
            'slug' => Str::slug($validated['title']),
            'order' => $validated['order'] ?? 0,
            'active' => $request->has('active'),
        ];

        $category->update($validated);

        // Atualizar a mídia da categoria
        if ($request->filled('thumbnail')) {
            // Remover todas as mídias da coleção 'thumbnail'
            $category->detachMediaTags('thumbnail');
            
            // Associar nova mídia
            $media = Media::find($request->thumbnail);
            if ($media) {
                $category->attachMedia($media, 'thumbnail');
            }
        } else {
            // Se o thumbnail foi removido, remover todas as mídias da coleção
            $category->detachMediaTags('thumbnail');
        }

        // Atualizar galeria da categoria
        $category->detachMediaTags('gallery'); // Limpar galeria existente
        
        if ($request->filled('gallery')) {
            $galleryIds = json_decode($request->gallery, true);
            if (is_array($galleryIds) && !empty($galleryIds)) {
                $galleryMedia = Media::whereIn('id', $galleryIds)->get();
                foreach ($galleryMedia as $media) {
                    $category->attachMedia($media, 'gallery');
                }
            }
        }

        return redirect()->route('admin.categories.index')
            ->with('success', 'Categoria atualizada com sucesso!');
    }

    public function destroy($id)
    {
        $category = PlgCategories::findOrFail($id);
        $category->delete();

        return response()->json(['success' => true]);
    }

    private function getColumns()
    {
        return [
            [
                'data' => 'id', 
                'name' => 'id', 
                'label' => 'ID'
            ],
            [
                'data' => 'title', 
                'name' => 'title', 
                'label' => 'Título'
            ],
            [
                'data' => 'description', 
                'name' => 'description', 
                'label' => 'Descrição'
            ],
            [
                'data' => 'active', 
                'name' => 'active', 
                'label' => 'Status'
            ],
            [
                'data' => 'actions', 
                'name' => 'actions', 
                'label' => 'Ações', 
                'orderable' => false, 
                'searchable' => false
            ],
        ];
    }

    private function validateRequest(Request $request)
    {
        return $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:50',
            'order' => 'nullable|integer',
            'active' => 'boolean',
            'thumbnail' => 'nullable|exists:media,id',
            'gallery' => 'nullable|json',
        ]);
    }
} 