@php
    // Configurações padrão do seletor de ícones
    $inputName = $inputName ?? 'icon';
    $inputId = $inputId ?? 'icon';
    $currentValue = $currentValue ?? '';
    $label = $label ?? 'Ícone';
    $placeholder = $placeholder ?? 'Selecione um ícone';
@endphp

<div class="space-y-2">
    <label for="{{ $inputId }}" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
        {{ $label }}
    </label>

    <!-- Seletor de Ícone -->
    <div class="icon-selector" data-input-id="{{ $inputId }}">
        <input type="hidden" name="{{ $inputName }}" id="{{ $inputId }}" value="{{ $currentValue }}">

        <!-- Botão para abrir seletor -->
        <button type="button" id="icon-selector-btn-{{ $inputId }}"
            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 text-left flex items-center justify-between">
            <div class="flex items-center gap-2">
                <i id="selected-icon-{{ $inputId }}"
                    class="{{ $currentValue ?: 'fas fa-book' }} text-trends-primary"></i>
                <span id="selected-icon-name-{{ $inputId }}">{{ $currentValue ?: 'fas fa-book' }}</span>
            </div>
            <i class="fas fa-chevron-down text-zinc-400"></i>
        </button>

        <!-- Modal do seletor de ícones -->
        <div id="icon-modal-{{ $inputId }}"
            class="fixed inset-0 z-50 bg-black bg-opacity-50 hidden flex items-center justify-center">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-4xl max-h-[80vh] w-full mx-4 flex flex-col">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">
                        Selecionar Ícone
                    </h3>
                    <button type="button" id="close-icon-modal-{{ $inputId }}"
                        class="text-zinc-500 hover:text-zinc-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Busca de ícones -->
                <div class="mb-4">
                    <input type="text" id="icon-search-{{ $inputId }}"
                        placeholder="Buscar ícones... (ex: livro, casa, usuário)"
                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-700 dark:text-zinc-200">
                </div>

                <!-- Grade de ícones -->
                <div class="flex-1 overflow-y-auto">
                    <div id="icons-grid-{{ $inputId }}"
                        class="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-3">
                        <!-- Ícones serão carregados via JavaScript -->
                    </div>
                </div>

                <div class="mt-4 flex justify-end gap-2">
                    <button type="button" id="cancel-icon-selection-{{ $inputId }}"
                        class="px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600">
                        Cancelar
                    </button>
                    <button type="button" id="confirm-icon-selection-{{ $inputId }}"
                        class="px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90">
                        Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <p class="text-xs text-zinc-500 dark:text-zinc-400">
        Clique no botão acima para escolher um ícone
    </p>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar seletor de ícones para este input específico
    initIconSelector('{{ $inputId }}');
});

function initIconSelector(inputId) {
    // Lista de ícones populares com suas categorias e palavras-chave
    const iconsList = [
        // Educação
        {
            icon: 'fas fa-book',
            name: 'Livro',
            keywords: ['livro', 'book', 'educação', 'leitura']
        },
        {
            icon: 'fas fa-graduation-cap',
            name: 'Formatura',
            keywords: ['graduação', 'formatura', 'educação', 'diploma']
        },
        {
            icon: 'fas fa-chalkboard-teacher',
            name: 'Professor',
            keywords: ['professor', 'ensino', 'educação', 'aula']
        },
        {
            icon: 'fas fa-university',
            name: 'Universidade',
            keywords: ['universidade', 'faculdade', 'educação']
        },
        {
            icon: 'fas fa-pencil-alt',
            name: 'Lápis',
            keywords: ['lápis', 'escrever', 'editar', 'texto']
        },
        {
            icon: 'fas fa-bookmark',
            name: 'Marcador',
            keywords: ['marcador', 'favorito', 'salvar']
        },

        // Tecnologia
        {
            icon: 'fas fa-laptop',
            name: 'Laptop',
            keywords: ['laptop', 'computador', 'tecnologia']
        },
        {
            icon: 'fas fa-code',
            name: 'Código',
            keywords: ['código', 'programação', 'desenvolvimento']
        },
        {
            icon: 'fas fa-database',
            name: 'Banco de Dados',
            keywords: ['database', 'dados', 'armazenamento']
        },
        {
            icon: 'fas fa-mobile-alt',
            name: 'Celular',
            keywords: ['celular', 'mobile', 'telefone']
        },
        {
            icon: 'fas fa-wifi',
            name: 'Wi-Fi',
            keywords: ['wifi', 'internet', 'conexão']
        },
        {
            icon: 'fas fa-cloud',
            name: 'Nuvem',
            keywords: ['nuvem', 'cloud', 'armazenamento']
        },

        // Negócios
        {
            icon: 'fas fa-briefcase',
            name: 'Maleta',
            keywords: ['maleta', 'negócios', 'trabalho', 'empresa']
        },
        {
            icon: 'fas fa-chart-line',
            name: 'Gráfico',
            keywords: ['gráfico', 'estatística', 'crescimento']
        },
        {
            icon: 'fas fa-dollar-sign',
            name: 'Dólar',
            keywords: ['dólar', 'dinheiro', 'financeiro']
        },
        {
            icon: 'fas fa-handshake',
            name: 'Aperto de Mão',
            keywords: ['parceria', 'acordo', 'negócio']
        },
        {
            icon: 'fas fa-building',
            name: 'Prédio',
            keywords: ['prédio', 'empresa', 'corporativo']
        },

        // Saúde
        {
            icon: 'fas fa-heartbeat',
            name: 'Batimento',
            keywords: ['coração', 'saúde', 'batimento']
        },
        {
            icon: 'fas fa-user-md',
            name: 'Médico',
            keywords: ['médico', 'doutor', 'saúde']
        },
        {
            icon: 'fas fa-pills',
            name: 'Remédios',
            keywords: ['remédio', 'pílula', 'medicamento']
        },
        {
            icon: 'fas fa-stethoscope',
            name: 'Estetoscópio',
            keywords: ['estetoscópio', 'médico', 'exame']
        },

        // Comunicação
        {
            icon: 'fas fa-comments',
            name: 'Comentários',
            keywords: ['comentário', 'chat', 'conversa']
        },
        {
            icon: 'fas fa-envelope',
            name: 'Envelope',
            keywords: ['email', 'carta', 'mensagem']
        },
        {
            icon: 'fas fa-phone',
            name: 'Telefone',
            keywords: ['telefone', 'ligação', 'contato']
        },
        {
            icon: 'fas fa-bullhorn',
            name: 'Megafone',
            keywords: ['megafone', 'anúncio', 'comunicação']
        },

        // Mídia
        {
            icon: 'fas fa-play-circle',
            name: 'Play',
            keywords: ['play', 'vídeo', 'reproduzir']
        },
        {
            icon: 'fas fa-camera',
            name: 'Câmera',
            keywords: ['câmera', 'foto', 'imagem']
        },
        {
            icon: 'fas fa-video',
            name: 'Vídeo',
            keywords: ['vídeo', 'filmagem', 'gravação']
        },
        {
            icon: 'fas fa-music',
            name: 'Música',
            keywords: ['música', 'áudio', 'som']
        },

        // Geral
        {
            icon: 'fas fa-home',
            name: 'Casa',
            keywords: ['casa', 'início', 'home']
        },
        {
            icon: 'fas fa-user',
            name: 'Usuário',
            keywords: ['usuário', 'pessoa', 'perfil']
        },
        {
            icon: 'fas fa-users',
            name: 'Usuários',
            keywords: ['usuários', 'grupo', 'pessoas']
        },
        {
            icon: 'fas fa-cog',
            name: 'Configuração',
            keywords: ['configuração', 'ajuste', 'opção']
        },
        {
            icon: 'fas fa-star',
            name: 'Estrela',
            keywords: ['estrela', 'favorito', 'destaque']
        },
        {
            icon: 'fas fa-heart',
            name: 'Coração',
            keywords: ['coração', 'amor', 'curtir']
        },
        {
            icon: 'fas fa-lock',
            name: 'Cadeado',
            keywords: ['cadeado', 'segurança', 'privado']
        },
        {
            icon: 'fas fa-key',
            name: 'Chave',
            keywords: ['chave', 'acesso', 'senha']
        },
        {
            icon: 'fas fa-search',
            name: 'Busca',
            keywords: ['busca', 'pesquisa', 'procurar']
        },
        {
            icon: 'fas fa-plus',
            name: 'Mais',
            keywords: ['mais', 'adicionar', 'novo']
        },
        {
            icon: 'fas fa-minus',
            name: 'Menos',
            keywords: ['menos', 'remover', 'subtrair']
        },
        {
            icon: 'fas fa-times',
            name: 'Fechar',
            keywords: ['fechar', 'cancelar', 'x']
        },
        {
            icon: 'fas fa-check',
            name: 'Check',
            keywords: ['check', 'confirmar', 'ok']
        },
        {
            icon: 'fas fa-arrow-right',
            name: 'Seta Direita',
            keywords: ['seta', 'direita', 'próximo']
        },
        {
            icon: 'fas fa-arrow-left',
            name: 'Seta Esquerda',
            keywords: ['seta', 'esquerda', 'voltar']
        },
        {
            icon: 'fas fa-globe',
            name: 'Globo',
            keywords: ['globo', 'mundo', 'internacional']
        },
        {
            icon: 'fas fa-map-marker-alt',
            name: 'Localização',
            keywords: ['localização', 'mapa', 'endereço']
        },
        {
            icon: 'fas fa-calendar',
            name: 'Calendário',
            keywords: ['calendário', 'data', 'agenda']
        },
        {
            icon: 'fas fa-clock',
            name: 'Relógio',
            keywords: ['relógio', 'tempo', 'hora']
        },
        {
            icon: 'fas fa-fire',
            name: 'Fogo',
            keywords: ['fogo', 'quente', 'popular']
        },
        {
            icon: 'fas fa-bolt',
            name: 'Raio',
            keywords: ['raio', 'rápido', 'energia']
        },
        {
            icon: 'fas fa-gem',
            name: 'Gema',
            keywords: ['gema', 'premium', 'diamante']
        },
        {
            icon: 'fas fa-trophy',
            name: 'Troféu',
            keywords: ['troféu', 'prêmio', 'vencedor']
        },
        {
            icon: 'fas fa-shield-alt',
            name: 'Escudo',
            keywords: ['escudo', 'proteção', 'segurança']
        },
    ];

    let selectedIconClass = '';
    const iconSelectorBtn = document.getElementById(`icon-selector-btn-${inputId}`);
    const iconModal = document.getElementById(`icon-modal-${inputId}`);
    const closeIconModal = document.getElementById(`close-icon-modal-${inputId}`);
    const cancelIconSelection = document.getElementById(`cancel-icon-selection-${inputId}`);
    const confirmIconSelection = document.getElementById(`confirm-icon-selection-${inputId}`);
    const iconSearch = document.getElementById(`icon-search-${inputId}`);
    const iconsGrid = document.getElementById(`icons-grid-${inputId}`);
    const iconInput = document.getElementById(inputId);
    const selectedIcon = document.getElementById(`selected-icon-${inputId}`);
    const selectedIconName = document.getElementById(`selected-icon-name-${inputId}`);

    // Renderizar grade de ícones
    function renderIcons(icons = iconsList) {
        iconsGrid.innerHTML = '';

        icons.forEach(iconData => {
            const iconDiv = document.createElement('div');
            iconDiv.className =
                'icon-item flex flex-col items-center p-3 rounded-lg cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-700 transition-colors';
            iconDiv.setAttribute('data-icon', iconData.icon);
            iconDiv.innerHTML = `
                <i class="${iconData.icon} text-2xl text-zinc-600 dark:text-zinc-300 mb-2"></i>
                <span class="text-xs text-center text-zinc-500 dark:text-zinc-400">${iconData.name}</span>
            `;

            iconDiv.addEventListener('click', function() {
                // Remover seleção anterior
                document.querySelectorAll(`#icons-grid-${inputId} .icon-item`).forEach(item => {
                    item.classList.remove('bg-trends-primary/10', 'border-2', 'border-trends-primary');
                });

                // Adicionar seleção atual
                this.classList.add('bg-trends-primary/10', 'border-2', 'border-trends-primary');
                selectedIconClass = iconData.icon;
            });

            iconsGrid.appendChild(iconDiv);
        });
    }

    // Busca de ícones
    function searchIcons(query) {
        if (!query.trim()) {
            renderIcons();
            return;
        }

        const filtered = iconsList.filter(iconData => {
            const searchQuery = query.toLowerCase();
            return iconData.name.toLowerCase().includes(searchQuery) ||
                iconData.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery));
        });

        renderIcons(filtered);
    }

    // Eventos
    iconSelectorBtn.addEventListener('click', function() {
        iconModal.classList.remove('hidden');
        renderIcons();
        iconSearch.value = '';
        iconSearch.focus();
    });

    closeIconModal.addEventListener('click', function() {
        iconModal.classList.add('hidden');
    });

    cancelIconSelection.addEventListener('click', function() {
        iconModal.classList.add('hidden');
    });

    confirmIconSelection.addEventListener('click', function() {
        if (selectedIconClass) {
            iconInput.value = selectedIconClass;
            selectedIcon.className = selectedIconClass + ' text-trends-primary';
            selectedIconName.textContent = selectedIconClass;

            // Atualizar preview em tempo real (se existir)
            const previewIcon = document.getElementById('preview-icon');
            if (previewIcon) {
                previewIcon.className = selectedIconClass + ' text-trends-primary text-xl';
            }

            // Disparar evento para preview
            iconInput.dispatchEvent(new Event('input'));
        }
        iconModal.classList.add('hidden');
    });

    iconSearch.addEventListener('input', function() {
        searchIcons(this.value);
    });

    // Fechar modal com ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !iconModal.classList.contains('hidden')) {
            iconModal.classList.add('hidden');
        }
    });

    // Fechar modal clicando fora
    iconModal.addEventListener('click', function(e) {
        if (e.target === this) {
            this.classList.add('hidden');
        }
    });
}
</script>
@endpush 