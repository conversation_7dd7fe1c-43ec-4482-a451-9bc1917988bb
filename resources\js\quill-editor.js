// Importar Quill da instalação local
import Quill from 'quill';

// Configuração do editor Quill
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        initializeQuillEditors();
    }, 100);
});

function imageUrlHandler() {
    const url = prompt('Digite o URL da imagem:');
    if (url) {
        const range = this.quill.getSelection();
        this.quill.insertEmbed(range.index, 'image', url, 'user');
    }
}

function toggleHtmlViewHandler() {
    const editorContainer = this.quill.root.parentNode;
    let textarea = editorContainer.querySelector('.html-source');

    if (!textarea) {
        textarea = document.createElement('textarea');
        textarea.className = 'html-source';
        textarea.style.width = '100%';
        textarea.style.minHeight = '200px';
        textarea.style.fontFamily = 'monospace';
        textarea.style.marginTop = '10px';
        editorContainer.appendChild(textarea);
    }

    if (this.quill.root.style.display === 'none') {
        this.quill.root.style.display = '';
        textarea.style.display = 'none';
        this.quill.root.innerHTML = textarea.value;
    } else {
        textarea.value = this.quill.root.innerHTML;
        this.quill.root.style.display = 'none';
        textarea.style.display = 'block';
    }
}

function initializeQuillEditors() {
    const defaultOptions = {
        theme: 'snow',
        placeholder: 'Digite seu texto aqui...',
        modules: {
            syntax: false,
            toolbar: {
                container: [
                    ['bold', 'italic', 'underline'],
                    ['blockquote'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                    [{ header: [1, 2, 3, false] }],
                    ['clean'],
                    ['link'],
                    ['image'],
                    ['html']
                ],
                handlers: {
                    'image': imageUrlHandler,
                    'html': toggleHtmlViewHandler
                }
            }
        }
    };

    const editorElements = document.querySelectorAll('.editor:not(.quill-initialized)');

    editorElements.forEach((element) => {
        const placeholder = element.dataset.placeholder || defaultOptions.placeholder;
        const inputName = element.dataset.input;
        if (!inputName) {
            console.error('Elemento editor sem data-input definido:', element);
            return;
        }

        const hiddenInput = document.getElementById(inputName);
        if (!hiddenInput) {
            console.error(`Campo hidden com ID '${inputName}' não encontrado`);
            return;
        }

        const quill = new Quill(element, {
            ...defaultOptions,
            placeholder: placeholder
        });

        element.classList.add('quill-initialized');

        const toolbar = quill.getModule('toolbar');
        if (toolbar) {
            const toolbarContainer = toolbar.container;
            const htmlButton = document.createElement('button');
            htmlButton.setAttribute('type', 'button');
            htmlButton.setAttribute('title', 'Ver HTML');
            htmlButton.innerHTML = '&lt;/&gt;';
            htmlButton.classList.add('ql-html');
            htmlButton.addEventListener('click', function () {
                toggleHtmlViewHandler.call({ quill });
            });
            toolbarContainer.appendChild(htmlButton);
        }

        if (hiddenInput.value && hiddenInput.value.trim() !== '') {
            try {
                quill.root.innerHTML = hiddenInput.value;
            } catch (e) {
                console.warn('Erro ao carregar HTML, usando texto simples:', e);
                quill.setText(hiddenInput.value);
            }
        }

        quill.on('text-change', () => {
            const content = quill.root.innerHTML;
            const textContent = quill.getText().trim();
            hiddenInput.value = textContent !== '' ? content : '';
            if (typeof updatePreview === 'function') updatePreview();
        });

        quill.on('selection-change', (range) => {
            if (!range) {
                hiddenInput.value = quill.root.innerHTML;
            }
        });

        window[`quill_${inputName}`] = quill;
    });
}

// ✅ Expor globalmente para uso dinâmico
window.initializeQuillEditors = initializeQuillEditors;
