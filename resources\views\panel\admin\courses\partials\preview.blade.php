<!-- <PERSON><PERSON> da direita - Preview do curso -->
<div class="live-preview">
    <div
        class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg sticky-preview">
        <div
            class="px-4 py-4 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-blue-500/5 to-transparent dark:from-blue-500/10">
            <div class="flex items-center gap-2">
                <div class="h-4 w-1 bg-blue-500 rounded-full"></div>
                <h1 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">Preview do Curso</h1>
            </div>
            <p class="text-zinc-500 dark:text-zinc-400 text-xs mt-1">
                Como aparecerá para o aluno
            </p>
        </div>

        <div class="p-4">
            <div class="preview-container dark:bg-zinc-800/80 text-sm">
                <!-- Thumbnail do curso -->
        
                <div data-preview="thumbnail" data-preview-type="image" data-default="{{ $thumbnailUrl }}" class="mb-4 {{ isset($course) && $course->hasMedia('thumbnail') ? '' : 'hidden' }}">
                    @if (!empty($thumbnailUrl))
                        <img src="{{ $thumbnailUrl }}" alt="Thumbnail do curso" class="w-full rounded-md object-cover h-40">
                    @endif
                </div>

                <!-- Título do curso -->
                <div class="mb-2">
                    <h3 class="text-lg font-semibold" data-preview="title"
                        data-default="{{ $course->title ?? 'Título do Curso' }}">
                        {{ isset($course) ? $course->title : 'Título do Curso' }}
                    </h3>
                </div>

                <!-- Descrição curta -->
                <div class="mb-4">
                    <p class="text-zinc-500 dark:text-zinc-400" data-preview="short_description"
                        data-default="{{ $course->short_description ?? 'Descrição curta do curso' }}">
                        {{ isset($course) ? $course->short_description : 'Descrição curta do curso' }}
                    </p>
                </div>

                <!-- Informações adicionais -->
                <div class="space-y-2">
                    <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                        <i class="fas fa-user"></i>
                        <span data-preview="teacher_id" data-default="{{ $course->teacher ?? 'Professor' }}">
                            {{ isset($course) && $course->teacher ? $course->teacher->name : 'Professor' }} </span>
                    </div>
                    <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                        <i class="fas fa-folder"></i>
                        <span data-preview="category_id" data-default="{{ $course->category ?? 'Categoria' }}">
                            {{ isset($course) && $course->category ? $course->category->title : 'Categoria' }}
                        </span>
                    </div>
                    <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                        <i class="fas fa-clock"></i>
                        <span data-preview="duration_minutes"
                            data-default="{{ $course->duration_minutes ?? 'Duração' }}">
                            {{ isset($course) && $course->duration_minutes ? $course->duration_minutes . ' minutos' : 'Duração' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>