<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\SysUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;

class AdminAuthController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.admin.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $user = Auth::user();
            
            // Verificar se o usuário tem role de admin/teacher
            if (!in_array($user->role, ['admin', 'super_admin', 'teacher'])) {
                Auth::logout();
                throw ValidationException::withMessages([
                    'email' => ['Este usuário não tem permissão de administrador/professor.'],
                ]);
            }
            
            $request->session()->regenerate();
            return redirect()->route('admin.dashboard');
        }
        
        throw ValidationException::withMessages([
            'email' => ['As credenciais não correspondem aos registros de administrador/professor.'],
        ]);
    }

    public function showForgotForm()
    {
        return view('auth.admin.forgot-password');
    }

    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => ['required', 'email']]);

        // Verificar se o email existe na tabela de administradores
        $user = SysUser::where('email', $request->email)
            ->whereIn('role', ['admin', 'super_admin', 'teacher'])
            ->first();
        
        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['Este email não está registrado como administrador/professor.'],
            ]);
        }

        $status = Password::sendResetLink($request->only('email'));

        if ($status === Password::RESET_LINK_SENT) {
            return back()->with('status', 'Link de recuperação enviado para seu email!');
        }

        throw ValidationException::withMessages(['email' => [__($status)]]);
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('admin.login');
    }
} 