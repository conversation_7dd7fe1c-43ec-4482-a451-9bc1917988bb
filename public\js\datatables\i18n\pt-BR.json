{"emptyTable": "Nenhum registro encontrado", "info": "Mostrando de _START_ até _END_ de _TOTAL_ registros", "infoFiltered": "(Filtrados de _MAX_ registros)", "infoThousands": ".", "loadingRecords": "Carregando...", "zeroRecords": "Nenhum registro encontrado", "search": "<PERSON><PERSON><PERSON><PERSON>", "paginate": {"next": "Próximo", "previous": "Anterior", "first": "<PERSON><PERSON>", "last": "Último"}, "aria": {"sortAscending": ": Ordenar colunas de forma ascendente", "sortDescending": ": Ordenar colunas de forma descendente"}, "select": {"rows": {"_": "Selecionado %d linhas", "1": "Selecionado 1 linha"}, "cells": {"1": "1 célula selecionada", "_": "%d células selecionadas"}, "columns": {"1": "1 coluna selecionada", "_": "%d colunas selecionadas"}}, "buttons": {"copySuccess": {"1": "Uma linha copiada com sucesso", "_": "%d linhas copiadas com sucesso"}, "collection": "Coleção </span>", "colvis": "Visibilidade da Coluna", "colvisRestore": "Restaurar Visibilidade", "copy": "Copiar", "copyKeys": "Pressione ctrl ou u2318 + C para copiar os dados da tabela para a área de transferência do sistema. Para cancelar, clique nesta mensagem ou pressione Esc..", "copyTitle": "Copiar para a Área de Transferência", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "Mostrar todos os registros", "_": "Mostrar %d registros"}, "pdf": "PDF", "print": "Imprimir", "createState": "Criar estado", "removeAllStates": "Remover todos os estados", "removeState": "Remover", "renameState": "Renomear", "savedStates": "Estados salvos", "stateRestore": "Estado %d", "updateState": "<PERSON><PERSON><PERSON><PERSON>"}, "autoFill": {"cancel": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON> to<PERSON> as c<PERSON><PERSON><PERSON> com", "fillHorizontal": "Pre<PERSON>cher células horizontalmente", "fillVertical": "Pre<PERSON>cher células verticalmente"}, "lengthMenu": "Exibir _MENU_ resultados por página", "searchBuilder": {"add": "Adicionar <PERSON>", "button": {"0": "<PERSON><PERSON><PERSON><PERSON>", "_": "Const<PERSON><PERSON> de Pesquisa (%d)"}, "clearAll": "<PERSON><PERSON>", "condition": "Condição", "conditions": {"date": {"after": "<PERSON><PERSON><PERSON>", "before": "<PERSON><PERSON>", "between": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "equals": "Igual", "not": "Não", "notBetween": "Não Entre", "notEmpty": "Não Vazio"}, "number": {"between": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "equals": "Igual", "gt": "<PERSON><PERSON>", "gte": "<PERSON><PERSON> ou <PERSON> a", "lt": "<PERSON><PERSON>", "lte": "Menor ou Igual a", "not": "Não", "notBetween": "Não Entre", "notEmpty": "Não Vazio"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "endsWith": "Termina Com", "equals": "Igual", "not": "Não", "notEmpty": "Não Vazio", "startsWith": "Começa Com", "notContains": "Não contém", "notStartsWith": "Não começa com", "notEndsWith": "Não termina com"}, "array": {"contains": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "equals": "Igual à", "not": "Não", "notEmpty": "Não vazio", "without": "<PERSON><PERSON> possui"}}, "data": "Data", "deleteTitle": "Excluir regra de filtragem", "logicAnd": "E", "logicOr": "Ou", "title": {"0": "<PERSON><PERSON><PERSON><PERSON>", "_": "Const<PERSON><PERSON> de Pesquisa (%d)"}, "value": "Valor", "leftTitle": "Critérios Externos", "rightTitle": "Critérios Internos"}, "searchPanes": {"clearMessage": "<PERSON><PERSON>", "collapse": {"0": "<PERSON><PERSON><PERSON> de Pesquisa", "_": "Pain<PERSON>is de Pesquisa (%d)"}, "count": "{total}", "countFiltered": "{shown} ({total})", "emptyPanes": "<PERSON><PERSON><PERSON>", "loadMessage": "Carregan<PERSON> Pesquisa...", "title": "Filtros Ativos", "showMessage": "<PERSON><PERSON> todos", "collapseMessage": "<PERSON><PERSON><PERSON>"}, "thousands": ".", "datetime": {"previous": "Anterior", "next": "Próximo", "hours": "<PERSON><PERSON>", "minutes": "Min<PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "amPm": ["am", "pm"], "unknown": "-", "months": {"0": "Janeiro", "1": "<PERSON><PERSON>", "10": "Novembro", "11": "Dezembro", "2": "Março", "3": "Abril", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON>", "7": "Agosto", "8": "Setembro", "9": "Out<PERSON>ro"}, "weekdays": ["Dom", "Seg", "<PERSON><PERSON>", "<PERSON>ua", "<PERSON>ui", "Sex", "<PERSON><PERSON><PERSON>"]}, "editor": {"close": "<PERSON><PERSON><PERSON>", "create": {"button": "Novo", "submit": "<PERSON><PERSON><PERSON>", "title": "Criar novo registro"}, "edit": {"button": "<PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Editar registro"}, "error": {"system": "Ocorreu um erro no sistema ([Mais informações</a>)."}, "multi": {"noMulti": "Essa entrada pode ser editada individualmente, mas não como parte do grupo", "restore": "Desfazer alterações", "title": "Multiplos valores", "info": "Os itens selecionados contêm valores diferentes para esta entrada. Para editar e definir todos os itens para esta entrada com o mesmo valor, clique ou toque aqui, caso contr<PERSON><PERSON>, eles manterão seus valores individuais."}, "remove": {"button": "Remover", "confirm": {"_": "Tem certeza que quer deletar %d linhas?", "1": "Tem certeza que quer deletar 1 linha?"}, "submit": "Remover", "title": "Remover registro"}}, "decimal": ",", "stateRestore": {"creationModal": {"button": "<PERSON><PERSON><PERSON>", "columns": {"search": "Busca de colunas", "visible": "Visibilidade da coluna"}, "name": "Nome:", "order": "<PERSON>nar", "paging": "Paginação", "scroller": "Posição da barra de rolagem", "search": "Busca", "searchBuilder": "Mecanismo de busca", "select": "Selecionar", "title": "Criar novo estado", "toggleLabel": "Inclui:"}, "emptyStates": "Nenhum estado salvo", "removeConfirm": "Confirma remover %s?", "removeJoiner": "e", "removeSubmit": "Remover", "removeTitle": "Remover estado", "renameButton": "Renomear", "renameLabel": "Novo nome para %s:", "renameTitle": "Renomear estado", "duplicateError": "Já existe um estado com esse nome!", "emptyError": "<PERSON>ão pode ser vazio!", "removeError": "Falha ao remover estado!"}, "infoEmpty": "Mostrando 0 até 0 de 0 registro(s)", "processing": "Carregando...", "searchPlaceholder": "Buscar registros"}