@extends('layouts.panel')

@section('title', $course->title)
@section('page_title', 'Visualizar Curso')

@section('content')
<div class="container mx-auto px-4">
    <!-- Header com breadcrumb -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
            <div class="flex items-center text-sm text-accent-400 mb-2">
                <a href="{{ route('admin.courses.index') }}" class="hover:text-accent-500">Cursos</a>
                <span class="mx-2">/</span>
                <span class="text-accent-300">Visualizar</span>
            </div>
            <h1 class="text-2xl font-semibold text-accent-500">{{ $course->title }}</h1>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('admin.courses.edit', $course->id) }}" 
               class="flex items-center gap-2 px-4 py-2 bg-primary-600 text-accent-500 rounded-lg hover:bg-primary-700 transition-colors">
                <i class="fas fa-edit"></i>
                Editar
            </a>
            <a href="{{ route('admin.courses.index') }}" 
               class="flex items-center gap-2 px-4 py-2 bg-dark-300 text-accent-300 rounded-lg hover:bg-dark-400 transition-colors">
                <i class="fas fa-arrow-left"></i>
                Voltar
            </a>
        </div>
    </div>

    <!-- Card Principal -->
    <div class="bg-dark-200 rounded-lg shadow-lg overflow-hidden">
        <!-- Thumbnail e Informações Básicas -->
        <div class="flex flex-col md:flex-row">
            <!-- Thumbnail -->
            <div class="w-full md:w-1/3 relative group">
                @if($course->thumbnail)
                    <img src="{{ asset($course->thumbnail) }}" 
                         alt="{{ $course->title }}" 
                         class="w-full h-64 object-cover">
                @else
                    <div class="w-full h-64 flex items-center justify-center bg-dark-300">
                        <i class="fas fa-image text-5xl text-accent-400 opacity-50"></i>
                    </div>
                @endif
                <!-- Overlay com status -->
                <div class="absolute top-4 right-4">
                    @if($course->status == 'published')
                        <div class="flex items-center bg-green-500 bg-opacity-90 text-white px-3 py-1 rounded-full text-sm">
                            <span class="w-2 h-2 bg-white rounded-full mr-2"></span>
                            Publicado
                        </div>
                    @else
                        <div class="flex items-center bg-yellow-500 bg-opacity-90 text-white px-3 py-1 rounded-full text-sm">
                            <span class="w-2 h-2 bg-white rounded-full mr-2"></span>
                            Rascunho
                        </div>
                    @endif
                </div>
            </div>

            <!-- Informações Básicas -->
            <div class="w-full md:w-2/3 p-6 bg-dark-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Duração -->
                    <div class="bg-dark-300 rounded-lg p-4 hover:bg-dark-400 transition-colors">
                        <h3 class="text-sm text-accent-400 mb-2 font-medium">Duração</h3>
                        <div class="flex items-center text-accent-300">
                            <i class="far fa-clock text-lg mr-3 text-primary-500"></i>
                            <span class="text-lg">{{ $course->duration_minutes ?? 0 }} minutos</span>
                        </div>
                    </div>

                    <!-- Professor -->
                    <div class="bg-dark-300 rounded-lg p-4 hover:bg-dark-400 transition-colors">
                        <h3 class="text-sm text-accent-400 mb-2 font-medium">Professor</h3>
                        <div class="flex items-center text-accent-300">
                            <i class="fas fa-user-tie text-lg mr-3 text-primary-500"></i>
                            <span class="text-lg">{{ $course->teacher->name ?? 'Sem professor' }}</span>
                        </div>
                    </div>

                    <!-- Categoria -->
                    <div class="bg-dark-300 rounded-lg p-4 hover:bg-dark-400 transition-colors">
                        <h3 class="text-sm text-accent-400 mb-2 font-medium">Categoria</h3>
                        <div class="flex items-center text-accent-300">
                            <i class="fas fa-folder text-lg mr-3 text-primary-500"></i>
                            <span class="text-lg">{{ $course->category->name ?? 'Sem categoria' }}</span>
                        </div>
                    </div>

                    <!-- Destaque -->
                    <div class="bg-dark-300 rounded-lg p-4 hover:bg-dark-400 transition-colors">
                        <h3 class="text-sm text-accent-400 mb-2 font-medium">Destaque</h3>
                        <div class="flex items-center text-accent-300">
                            <i class="fas fa-star text-lg mr-3 {{ $course->featured ? 'text-yellow-500' : 'text-primary-500' }}"></i>
                            <span class="text-lg">{{ $course->featured ? 'Em destaque' : 'Não destacado' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Descrições -->
        <div class="p-6 space-y-6">
            <!-- Descrição Curta -->
            <div>
                <h2 class="flex items-center gap-2 text-lg font-medium text-accent-500 mb-3">
                    <i class="fas fa-align-left text-primary-500"></i>
                    Descrição Curta
                </h2>
                <div class="bg-dark-300 rounded-lg p-4 text-accent-300">
                    {{ $course->short_description }}
                </div>
            </div>

            <!-- Descrição Completa -->
            <div>
                <h2 class="flex items-center gap-2 text-lg font-medium text-accent-500 mb-3">
                    <i class="fas fa-book text-primary-500"></i>
                    Descrição Completa
                </h2>
                <div class="bg-dark-300 rounded-lg p-4 text-accent-300 prose prose-dark max-w-none">
                    {!! nl2br(e($course->description)) !!}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 