<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->string('name');
            $table->string('email')->unique();
            $table->string('password');
            $table->enum('role', ['super_admin', 'teacher', 'student'])->default('teacher');
            $table->string('document')->nullable(); // CPF/CNPJ
            $table->string('phone')->nullable(); // Telefone
            $table->boolean('is_phone_whatsapp')->default(false); // Se telefone é WhatsApp
            $table->text('bio')->nullable();
            $table->string('profile_image')->nullable();
            $table->boolean('active')->default(true);
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();

            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');

            // Índices para performance
            $table->index(['company_id', 'role']); // Multi-tenancy + role
            $table->index(['role', 'active']);
            $table->index(['active']);
            $table->index(['email', 'active']);
            $table->index(['document']); // Para busca por CPF/CNPJ
            $table->index(['phone']); // Para busca por telefone
            $table->index(['company_id', 'email']); // Multi-tenancy + email
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_users');
    }
};
