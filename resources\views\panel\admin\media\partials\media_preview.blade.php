<div id="media-preview-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
    <div class="bg-white dark:bg-zinc-900 rounded-lg shadow-xl w-full max-w-3xl">
        <div class="flex items-center justify-between p-4 border-b border-zinc-200 dark:border-zinc-800">
            <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100" id="preview-modal-title">Visualizar Mídia</h3>
            <button type="button" class="text-zinc-400 hover:text-zinc-500 focus:outline-none" id="close-preview-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6" id="preview-content">
            {{-- Nome editável --}}
            <div class="mb-4">
                <label class="block text-sm font-medium text-zinc-700">Nome do arquivo</label>
                <input id="input-media-filename" type="text" class="mt-1 block w-full rounded border-zinc-300 shadow-sm" value="{{ $media->filename ?? '' }}">
            </div>
            {{-- Alt editável para imagens --}}
            @if(str_starts_with($media->mime_type ?? '', 'image/'))
            <div class="mb-4">
                <label class="block text-sm font-medium text-zinc-700">Texto alternativo (alt)</label>
                <input id="input-media-alt" type="text" class="mt-1 block w-full rounded border-zinc-300 shadow-sm" value="{{ $media->alt ?? '' }}">
            </div>
            @endif
            <button id="btn-save-media-info" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow">Salvar</button>
            <div id="media-info-feedback" class="mt-2 text-sm"></div>
        </div>
        <div class="px-6 py-4 bg-zinc-50 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800 flex justify-between">
            <button type="button"
                class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors"
                id="copy-media-url">
                <i class="fas fa-link"></i>
                <span>Copiar URL</span>
            </button>
            <button type="button"
                class="inline-flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                id="delete-media-item">
                <i class="fas fa-trash-alt"></i>
                <span>Excluir</span>
            </button>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const btn = document.getElementById('btn-save-media-info');
    if (btn) {
        btn.onclick = function() {
            const mediaId = {{ $media->id ?? 'null' }};
            const filename = document.getElementById('input-media-filename')?.value;
            const alt = document.getElementById('input-media-alt')?.value;
            updateMediaInfo(mediaId, { filename, alt }, function(res) {
                document.getElementById('media-info-feedback').textContent = 'Salvo com sucesso!';
                document.getElementById('media-info-feedback').className = 'mt-2 text-green-600 text-sm';
            }, function(err) {
                document.getElementById('media-info-feedback').textContent = err?.message || 'Erro ao salvar.';
                document.getElementById('media-info-feedback').className = 'mt-2 text-red-600 text-sm';
            });
        }
    }
});
</script>