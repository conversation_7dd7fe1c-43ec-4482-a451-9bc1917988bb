# name: Deploy Quizz

# on:
#   push:
#     branches: [ "main" ]

# jobs:
#   deploy:
#     runs-on: ubuntu-latest

#     steps:
#     - name: Deploy to Server
#       uses: appleboy/ssh-action@master
#       with:
#         host: ${{ secrets.PQUIZZ_SSH_HOST }}
#         username: ${{ secrets.PQUIZZ_SSH_USERNAME }}
#         key: ${{ secrets.PQUIZZ_SSH_PRIVATE_KEY }}
#         port: ${{ secrets.PQUIZZ_SSH_PORT }}
#         script: |
#           set -e
#           cd /var/www/html2/quizz

#           echo "Pulling latest changes..."
#           git pull origin main

#           echo "Clearing route cache..."

#           php artisan cache:clear
#           php artisan config:clear
#           php artisan view:clear
#           php artisan route:clear
#           php artisan optimize:clear
#           php artisan route:clear
          
#           rm -f storage/framework/routes/*.php || true

#           echo "Running migrations..."
#           php artisan migrate --force

#           echo "Checking if frontend changed..."
#           if git diff --name-only HEAD@{1} HEAD | grep -E "resources/|package.json|vite.config.js"; then
#             echo "Frontend changes detected. Installing dependencies and building assets..."
#             npm install
#             npm run build
#           else
#             echo "No frontend changes detected. Skipping npm install/build."
#           fi

#           echo "Final route cache clear..."
#           php artisan route:clear
#           rm -f storage/framework/routes/*.php || true
