<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Database\Factories\PlgStudentFactory;

class PlgStudent extends Authenticatable
{
    use HasFactory, SoftDeletes, Notifiable;

    protected $table = 'plg_students';

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return PlgStudentFactory::new();
    }

    protected $fillable = [
        'company_id',
        'name',
        'apelido',
        'email',
        'password',
        'document',
        'crm',
        'avatar',
        'phone1',
        'is_phone1_whatsapp',
        'phone2',
        'is_phone2_whatsapp',
        'birth_date',
        'active',
        'registration_status',
        'approved_at',
        'approved_by',
        'admin_notes',
        'last_login'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'active' => 'boolean',
        'is_phone1_whatsapp' => 'boolean',
        'is_phone2_whatsapp' => 'boolean',
        'birth_date' => 'date',
        'last_login' => 'datetime',
        'approved_at' => 'datetime'
    ];

    public function courses()
    {
        return $this->belongsToMany(PlgCourse::class, 'plg_enrollments', 'student_id', 'course_id')
            ->withTimestamps()
            ->withPivot(['company_id', 'status', 'enrolled_at', 'expires_at', 'enrollment_method', 'approval_status', 'requested_at', 'approved_at', 'approved_by', 'rejection_reason']);
    }


    /*Relacionamento do Endereço*/
    public function address()
    {
        return $this->hasOne(PlgStudentAddress::class, 'student_id');
    }



    /**
     * Relacionamento com módulos matriculados
     */
    public function enrolledModules()
    {
        return $this->belongsToMany(PlgModule::class, 'plg_enrollments', 'student_id', 'module_id')
            ->withTimestamps()
            ->withPivot(['company_id', 'enrolled_at', 'expires_at', 'module_price_at_time', 'status']);
    }

    /**
     * Matrículas do estudante
     */
    public function enrollments()
    {
        return $this->hasMany(PlgEnrollment::class, 'student_id');
    }

    /**
     * Relacionamento com tentativas de teste (NOVO).
     */
    public function attempts()
    {
        return $this->hasMany(PlgTestAttempt::class, 'student_id');
    }

    /**
     * Verifica se o aluno pode fazer um teste (NOVA LÓGICA).
     */
    public function canTakeTest(PlgTest $test): bool
    {
        if ($test->isQuiz()) {
            return true; // Quiz pode ser feito quantas vezes quiser
        }

        // Para exames e desafios, verificar tentativas
        $completedAttempts = $this->getCompletedAttempts($test);
        $maxAttempts = $test->max_attempts;

        // Se max_attempts é -1, permite tentativas ilimitadas
        if ($maxAttempts === -1) {
            return true;
        }

        // Verificar se ainda pode fazer tentativas
        return $completedAttempts->count() < $maxAttempts;

        // Verificar cooldown apenas se já fez tentativas
        if ($enrollment->total_attempts > 0) {
            $lastAttempt = $enrollment->attempts()->latest('finished_at')->first();

            if ($lastAttempt && $lastAttempt->isCompleted()) {
                // Se passou, não precisa fazer novamente (a menos que seja quiz)
                if ($lastAttempt->passed && !$test->isQuiz()) {
                    return false;
                }

                // Verificar cooldown
                $cooldownHours = $test->cooldown_hours;
                $canTakeAgainAt = $lastAttempt->finished_at->addHours($cooldownHours);

                return now()->gte($canTakeAgainAt);
            }
        }

        return true;
    }



    // ========================================
    // SISTEMA DE ESTADOS DO QUIZ - ROBUSTO
    // ========================================

    /**
     * Determina o estado atual do quiz para o estudante.
     * Estados: NEVER_STARTED, IN_PROGRESS, COMPLETED
     */
    public function getQuizState(PlgTest $test): string
    {
        // Buscar tentativa em progresso
        $inProgressAttempt = $this->getIncompleteAttempt($test);
        if ($inProgressAttempt) {
            return 'IN_PROGRESS';
        }

        // Buscar tentativas concluídas
        $completedAttempts = $this->getCompletedAttempts($test);
        if ($completedAttempts->count() > 0) {
            return 'COMPLETED';
        }

        return 'NEVER_STARTED';
    }

    /**
     * Verifica se tem tentativa incompleta (em progresso).
     */
    public function hasIncompleteAttempt(PlgTest $test): bool
    {
        return $this->getIncompleteAttempt($test) !== null;
    }

    /**
     * Retorna tentativa em progresso ou null.
     */
    public function getIncompleteAttempt(PlgTest $test): ?PlgTestAttempt
    {
        return $this->attempts()
            ->where('test_id', $test->id)
            ->where('status', 'in_progress')
            ->latest('started_at')
            ->first();
    }

    /**
     * Retorna todas as tentativas concluídas.
     */
    public function getCompletedAttempts(PlgTest $test)
    {
        return $this->attempts()
            ->where('test_id', $test->id)
            ->where('status', 'completed')
            ->orderBy('finished_at', 'desc')
            ->get();
    }

    /**
     * Retorna a última tentativa (concluída ou em progresso).
     */
    public function getLastAttempt(PlgTest $test): ?PlgTestAttempt
    {
        return $this->attempts()
            ->where('test_id', $test->id)
            ->whereIn('status', ['completed', 'in_progress'])
            ->latest('started_at')
            ->first();
    }

    /**
     * Retorna a melhor tentativa concluída.
     */
    public function getBestAttempt(PlgTest $test): ?PlgTestAttempt
    {
        return $this->attempts()
            ->where('test_id', $test->id)
            ->where('status', 'completed')
            ->orderBy('score', 'desc')
            ->orderBy('finished_at', 'desc')
            ->first();
    }

    /**
     * Verifica se passou no quiz (tem pelo menos uma tentativa aprovada).
     */
    public function hasPassedQuiz(PlgTest $test): bool
    {
        return $this->attempts()
            ->where('test_id', $test->id)
            ->where('status', 'completed')
            ->where('passed', true)
            ->exists();
    }

    /**
     * Calcula estatísticas do quiz para o estudante.
     */
    public function getQuizStats(PlgTest $test): array
    {
        $completedAttempts = $this->getCompletedAttempts($test);
        $bestAttempt = $this->getBestAttempt($test);
        $lastAttempt = $this->getLastAttempt($test);

        return [
            'total_attempts' => $completedAttempts->count(),
            'best_score' => $bestAttempt ? $bestAttempt->score : 0,
            'last_score' => $lastAttempt && $lastAttempt->status === 'completed' ? $lastAttempt->score : null,
            'has_passed' => $this->hasPassedQuiz($test),
            'passing_score' => $test->passing_score,
            'state' => $this->getQuizState($test),
            'can_continue' => $this->hasIncompleteAttempt($test),
            'incomplete_attempt' => $this->getIncompleteAttempt($test),
        ];
    }

    /**
     * Obter estatísticas completas de um módulo
     */
    public function getModuleStats($module)
    {
        // Buscar todos os testes do módulo (sem distinção de tipo)
        $allTests = $module->tests()->where('active', true)->get();

        // Buscar todas as tentativas do estudante para testes deste módulo
        $allAttempts = $this->attempts()
            ->whereIn('test_id', $allTests->pluck('id'))
            ->where('status', 'completed')
            ->get();

        // Calcular estatísticas gerais
        $totalAttempts = $allAttempts->count();
        $totalPassed = 0;
        $totalScore = 0;
        $bestScore = 0;
        $firstAttempt = null;

        // Agrupar tentativas por teste para calcular melhor pontuação de cada teste
        $testStats = [];
        foreach ($allTests as $test) {
            $testAttempts = $allAttempts->where('test_id', $test->id);
            if ($testAttempts->count() > 0) {
                $bestAttempt = $testAttempts->sortByDesc('score')->first();
                $testStats[$test->id] = [
                    'attempts' => $testAttempts->count(),
                    'best_score' => $bestAttempt->score ?? 0,
                    'passed' => $bestAttempt && $bestAttempt->score >= ($test->passing_score ?? 70)
                ];

                if ($testStats[$test->id]['passed']) {
                    $totalPassed++;
                }

                $totalScore += $testStats[$test->id]['best_score'];
                $bestScore = max($bestScore, $testStats[$test->id]['best_score']);
            }
        }

        $averageScore = $allTests->count() > 0 ? $totalScore / $allTests->count() : 0;

        // Primeira tentativa (mais antiga)
        if ($allAttempts->count() > 0) {
            $firstAttempt = $allAttempts->sortBy('created_at')->first()->created_at;
        }

        // Calcular progresso do módulo baseado nos testes aprovados
        $moduleProgress = $allTests->count() > 0 ? round(($totalPassed / $allTests->count()) * 100) : 0;

        return [
            'overall' => [
                'tests_passed' => $totalPassed,
                'module_progress' => $moduleProgress,
                'started_at' => $firstAttempt
            ],
            'quizzes' => [
                'total_attempts' => $totalAttempts,
                'average_score' => round($averageScore),
                'total_passed' => $totalPassed,
                'best_score' => $bestScore
            ],
            'exams' => [
                'total_attempts' => 0, // Mantido para compatibilidade
                'average_score' => 0,
                'total_passed' => 0,
                'best_score' => 0
            ],
            'totals' => [
                'total_quizzes' => $allTests->count(),
                'total_exams' => 0 // Não temos mais distinção de tipos
            ]
        ];
    }

    /**
     * Verificar se o estudante tem acesso a um módulo específico
     */
    public function hasAccessToModule(PlgModule $module): bool
    {
        // Se o módulo é gratuito, permitir acesso
        if ($module->is_free) {
            return true;
        }

        // Verificar se tem matrícula ativa (considera expiração)
        $enrollment = PlgEnrollment::where('student_id', $this->id)
            ->where('module_id', $module->id)
            ->first();

        return $enrollment && $enrollment->hasValidAccess();
    }

    /**
     * Obter matrícula do estudante em um módulo específico
     */
    public function getModuleEnrollment(PlgModule $module): ?PlgEnrollment
    {
        return PlgEnrollment::where('student_id', $this->id)
            ->where('module_id', $module->id)
            ->first();
    }

    /**
     * Verificar status de acesso a um módulo com detalhes
     */
    public function getModuleAccessStatus(PlgModule $module): array
    {
        if ($module->is_free) {
            return [
                'has_access' => true,
                'status' => 'free',
                'message' => 'Módulo gratuito'
            ];
        }

        $enrollment = $this->getModuleEnrollment($module);

        if (!$enrollment) {
            return [
                'has_access' => false,
                'status' => 'not_enrolled',
                'message' => 'Você não está matriculado neste módulo'
            ];
        }

        // Usar o método getAccessStatus() do enrollment que já considera expiração
        $accessStatus = $enrollment->getAccessStatus();

        // Adicionar informações do enrollment se tiver acesso
        if ($accessStatus['has_access']) {
            $accessStatus['enrollment'] = $enrollment;
        }

        return $accessStatus;
    }

    /**
     * Auto-matricular em módulo gratuito
     */
    public function autoEnrollInFreeModule(PlgModule $module): PlgEnrollment
    {
        // Verificar se o módulo é realmente gratuito
        if (!$module->is_free) {
            throw new \Exception('Apenas módulos gratuitos permitem auto-matrícula');
        }

        // Verificar se já está matriculado
        $existingEnrollment = $this->getModuleEnrollment($module);
        if ($existingEnrollment) {
            return $existingEnrollment;
        }

        // Criar matrícula automática
        $enrolledAt = now();
        $expiresAt = $module->duration_months ?
            $enrolledAt->copy()->addMonths($module->duration_months) : null;

        return PlgEnrollment::create([
            'company_id' => $this->company_id ?? 1,
            'user_id' => $module->user_id, // Professor que criou o módulo
            'student_id' => $this->id,
            'module_id' => $module->id,
            'enrolled_at' => $enrolledAt,
            'expires_at' => $expiresAt,
            'module_price_at_time' => 0.00, // Gratuito
            'status' => 'active',
        ]);
    }

    /**
     * Retorna o tempo restante para poder fazer o teste novamente.
     */
    public function getTestCooldownRemaining(PlgTest $test): ?string
    {
        if ($test->isQuiz() || $this->canTakeTest($test)) {
            return null;
        }

        $lastAttempt = $this->attempts()
            ->where('test_id', $test->id)
            ->where('status', 'completed')
            ->latest('finished_at')
            ->first();

        if (!$lastAttempt) {
            return null;
        }

        $cooldownHours = $test->cooldown_hours;
        $canTakeAgainAt = $lastAttempt->finished_at->addHours($cooldownHours);

        return $canTakeAgainAt->diffForHumans();
    }
}