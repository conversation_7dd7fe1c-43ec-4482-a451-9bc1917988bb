@extends('layouts.panel')

@section('title', 'Visualizar Módulo')
@section('page_title', 'Visualizar Módulo')

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Visualizar Módulo: ' . $module->title,
            'description' => 'Detalhes completos deste módulo.',
            'actions' => [
                [
                    'route' => route('admin.modules.index'),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ],
                [
                    'route' => route('admin.modules.edit', $module->id),
                    'text' => 'Editar <PERSON>ódulo',
                    'icon' => 'fas fa-edit',
                    'class' => 'bg-blue-600 text-white',
                    'hover_class' => 'bg-blue-700'
                ]
            ]
        ])

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Detalhes do módulo -->
                <div class="bg-white dark:bg-zinc-800 p-6 rounded-lg border border-zinc-200 dark:border-zinc-700 shadow-sm">
                    <h3 class="text-lg font-semibold mb-4 text-zinc-900 dark:text-zinc-100">Informações do Módulo</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Curso</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">
                                <a href="{{ route('admin.courses.show', $module->course->id) }}" class="text-trends-primary hover:underline">
                                    {{ $module->course->title }}
                                </a>
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Descrição</h4>
                            <div class="text-zinc-900 dark:text-zinc-100">
                                {!! $module->description ?: 'Não especificada' !!}
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Ordem</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $module->order }}</p>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Status</h4>
                            <p>
                                @if($module->active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                        Ativo
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                        Inativo
                                    </span>
                                @endif
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Criado em</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $module->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Atualizado em</h4>
                            <p class="text-zinc-900 dark:text-zinc-100">{{ $module->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-4 border-t border-zinc-200 dark:border-zinc-700">
                        <button type="button" 
                                class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                onclick="document.getElementById('modal-delete').classList.remove('hidden')">
                            <i class="fas fa-trash mr-2"></i> Excluir Módulo
                        </button>
                    </div>
                </div>
                
                <!-- Lista de conteúdos -->
                <div class="bg-white dark:bg-zinc-800 p-6 rounded-lg border border-zinc-200 dark:border-zinc-700 shadow-sm lg:col-span-2">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">Conteúdos do Módulo</h3>
                        <a href="{{ route('admin.modules.contents.create', $module->id) }}" 
                           class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-trends-primary border border-transparent rounded-md hover:bg-trends-primary/90 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                            <i class="fas fa-plus mr-2"></i>
                            Adicionar Conteúdo
                        </a>
                    </div>
                    
                    @if($module->contents->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                                <thead>
                                    <tr class="bg-zinc-50 dark:bg-zinc-700/30">
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">#</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">Título</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">Tipo</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">Duração</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">Ordem</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">Ações</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
                                    @foreach($module->contents->sortBy('order') as $content)
                                    <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/30">
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-zinc-500 dark:text-zinc-400">{{ $loop->iteration }}</td>
                                        <td class="px-3 py-2 text-sm text-zinc-900 dark:text-zinc-100">{{ $content->title }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm">
                                            @if($content->content_type == 'text')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                    Texto
                                                </span>
                                            @elseif($content->content_type == 'video')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                                    Vídeo
                                                </span>
                                            @elseif($content->content_type == 'quiz')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                    Quiz
                                                </span>
                                            @elseif($content->content_type == 'file')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300">
                                                    Arquivo
                                                </span>
                                            @elseif($content->content_type == 'link')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                                    Link
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                                                    {{ $content->content_type }}
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-zinc-500 dark:text-zinc-400">{{ $content->duration ? $content->duration . ' min' : '-' }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-zinc-500 dark:text-zinc-400">{{ $content->order }}</td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('admin.modules.contents.show', ['moduleId' => $module->id, 'content' => $content->id]) }}" 
                                                   class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" 
                                                   title="Visualizar">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.modules.contents.edit', ['moduleId' => $module->id, 'content' => $content->id]) }}" 
                                                   class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300" 
                                                   title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.modules.contents.destroy', ['moduleId' => $module->id, 'content' => $content->id]) }}" 
                                                      method="POST" 
                                                      class="inline"
                                                      onsubmit="return confirm('Tem certeza que deseja excluir este conteúdo?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300" 
                                                            title="Excluir">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="bg-zinc-50 dark:bg-zinc-800/50 text-center rounded-lg p-8">
                            <p class="text-zinc-500 dark:text-zinc-400 mb-4">Nenhum conteúdo cadastrado para este módulo.</p>
                            <a href="{{ route('admin.modules.contents.create', $module->id) }}" 
                               class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-trends-primary border border-transparent rounded-md hover:bg-trends-primary/90 focus:outline-none focus:ring-2 focus:ring-trends-primary/30">
                                <i class="fas fa-plus mr-2"></i>
                                Adicionar o primeiro conteúdo
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão -->
<div id="modal-delete" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-zinc-800 rounded-lg max-w-md w-full mx-4">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">Confirmar Exclusão</h3>
            <p class="mb-6 text-zinc-700 dark:text-zinc-300">Tem certeza que deseja excluir este módulo? Esta ação não pode ser desfeita.</p>
            
            <div class="flex justify-end gap-3">
                <button type="button" 
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 rounded-md hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-trends-primary/30"
                        onclick="document.getElementById('modal-delete').classList.add('hidden')">
                    Cancelar
                </button>
                
                <form action="{{ route('admin.modules.destroy', $module->id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        Excluir
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection 