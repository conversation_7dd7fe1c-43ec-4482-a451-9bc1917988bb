<?php

namespace App\Services;

use App\Models\SysUserActiveSession;
use App\Models\SysUserLoginAttempt;
use App\Models\PlgSystemSettings;
use App\Events\LoginAttemptDetected;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Jenssegers\Agent\Agent;

class SessionSecurityService
{
    protected $companyId;
    protected $settings;

    public function __construct()
    {
        $this->companyId = 1; // Por enquanto apenas TrendsQuiz
        $this->loadSettings();
    }

    /**
     * Carregar configurações do sistema
     */
    protected function loadSettings(): void
    {
        $this->settings = PlgSystemSettings::byCompany($this->companyId)
            ->where('category', 'security')
            ->where('key', 'like', 'session_security_%')
            ->pluck('value', 'key')
            ->toArray();
    }

    /**
     * Verificar se o controle de sessão está habilitado
     */
    public function isEnabled(): bool
    {
        return ($this->settings['session_security_enabled'] ?? 'false') === 'true';
    }

    /**
     * Verificar se o role do usuário deve ter controle de sessão
     */
    public function shouldControlUserRole(string $role): bool
    {
        $allowedRoles = explode(',', $this->settings['session_security_apply_to_roles'] ?? 'student,teacher');
        return in_array($role, $allowedRoles);
    }

    /**
     * Verificar se usuário já tem sessão ativa
     */
    public function hasActiveSession(int $userId): ?SysUserActiveSession
    {
        return SysUserActiveSession::byCompany($this->companyId)
            ->byUser($userId)
            ->active()
            ->first();
    }

    /**
     * Criar nova sessão ativa
     */
    public function createActiveSession(int $userId, Request $request): SysUserActiveSession
    {
        $agent = new Agent();
        $agent->setUserAgent($request->userAgent());

        return SysUserActiveSession::create([
            'company_id' => $this->companyId,
            'user_id' => $userId,
            'session_id' => Session::getId(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'device_type' => $this->getDeviceType($agent),
            'browser' => $agent->browser(),
            'platform' => $agent->platform(),
            'location' => $this->getLocationFromIp($request->ip()),
            'login_at' => now(),
            'last_activity' => now(),
            'is_active' => true,
        ]);
    }

    /**
     * Criar tentativa de login
     */
    public function createLoginAttempt(int $userId, string $currentSessionId, Request $request): SysUserLoginAttempt
    {
        $agent = new Agent();
        $agent->setUserAgent($request->userAgent());

        return SysUserLoginAttempt::create([
            'company_id' => $this->companyId,
            'user_id' => $userId,
            'current_session_id' => $currentSessionId,
            'attempt_ip' => $request->ip(),
            'attempt_user_agent' => $request->userAgent(),
            'attempt_device_info' => $this->getDeviceInfo($agent),
            'attempt_location' => $this->getLocationFromIp($request->ip()),
            'attempt_at' => now(),
            'status' => 'pending',
        ]);
    }

    /**
     * Processar tentativa de login
     */
    public function processLoginAttempt(int $userId, Request $request): array
    {
        // Verificar se controle está habilitado
        if (!$this->isEnabled()) {
            return ['action' => 'allow', 'reason' => 'disabled'];
        }

        // Verificar se role deve ser controlado
        $user = Auth::user() ?? \App\Models\SysUser::find($userId);
        if (!$this->shouldControlUserRole($user->role)) {
            return ['action' => 'allow', 'reason' => 'role_not_controlled'];
        }

        // Verificar sessão ativa
        $activeSession = $this->hasActiveSession($userId);
        if (!$activeSession) {
            return ['action' => 'allow', 'reason' => 'no_active_session'];
        }

        // Verificar se é a mesma sessão
        if ($activeSession->session_id === Session::getId()) {
            return ['action' => 'allow', 'reason' => 'same_session'];
        }

        // Criar tentativa de login
        $attempt = $this->createLoginAttempt($userId, $activeSession->session_id, $request);

        // Disparar evento para notificar usuário logado
        event(new LoginAttemptDetected($attempt, $activeSession));

        return [
            'action' => 'wait',
            'attempt_id' => $attempt->id,
            'timeout' => (int)($this->settings['session_security_timeout'] ?? 60),
        ];
    }

    /**
     * Atualizar atividade da sessão
     */
    public function updateSessionActivity(int $userId): void
    {
        SysUserActiveSession::byCompany($this->companyId)
            ->byUser($userId)
            ->where('session_id', Session::getId())
            ->active()
            ->first()?->updateActivity();
    }

    /**
     * Encerrar sessão
     */
    public function endSession(int $userId, ?string $sessionId = null, string $reason = 'manual'): bool
    {
        $query = SysUserActiveSession::byCompany($this->companyId)->byUser($userId);
        
        if ($sessionId) {
            $query->where('session_id', $sessionId);
        } else {
            $query->where('session_id', Session::getId());
        }

        $session = $query->active()->first();
        
        return $session ? $session->deactivate($reason) : false;
    }

    /**
     * Limpar sessões expiradas
     */
    public function cleanExpiredSessions(): int
    {
        $timeoutMinutes = (int)($this->settings['session_security_timeout'] ?? 120);

        $expiredSessions = SysUserActiveSession::byCompany($this->companyId)
            ->expired($timeoutMinutes)
            ->get();

        foreach ($expiredSessions as $session) {
            $session->deactivate('timeout');
        }

        return $expiredSessions->count();
    }

    /**
     * Obter tipo de dispositivo
     */
    protected function getDeviceType(Agent $agent): string
    {
        if ($agent->isMobile()) return 'mobile';
        if ($agent->isTablet()) return 'tablet';
        if ($agent->isDesktop()) return 'desktop';
        return 'unknown';
    }

    /**
     * Obter informações do dispositivo
     */
    protected function getDeviceInfo(Agent $agent): string
    {
        return sprintf('%s / %s / %s', 
            $agent->browser() ?: 'Unknown Browser',
            $agent->platform() ?: 'Unknown Platform',
            $this->getDeviceType($agent)
        );
    }

    /**
     * Obter localização do IP (implementação básica)
     */
    protected function getLocationFromIp(string $ip): ?string
    {
        // Implementação básica - pode ser melhorada com serviços de geolocalização
        if ($ip === '127.0.0.1' || $ip === '::1') {
            return 'Local';
        }
        
        // Aqui você pode integrar com serviços como ipinfo.io, geoip, etc.
        return null;
    }
}
