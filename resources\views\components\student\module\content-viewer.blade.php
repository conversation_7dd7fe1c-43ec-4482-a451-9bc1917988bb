@props(['content', 'module', 'previousContent' => null, 'nextContent' => null])

<div  class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <!-- Header -->
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
                <i class="fas fa-book text-blue-600"></i>
                <span class="text-sm font-medium text-blue-600">{{ ucfirst($content->content_type) }}</span>
            </div>
            <div class="flex items-center space-x-2">
                <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-bookmark"></i>
                </button>
                <div class="flex items-center space-x-1">
                    <input type="checkbox" id="mark-complete" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="mark-complete" class="text-sm text-gray-600"><PERSON><PERSON> concluído</label>
                </div>
            </div>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $content->title }}</h1>
        
        @if($content->description)
            <p class="text-gray-600 mb-4">{{ $content->description }}</p>
        @endif
        
        <div class="flex items-center space-x-4 text-sm text-gray-500">
            @if($content->duration)
                <span>
                    <i class="fas fa-clock mr-1"></i>
                    {{ $content->duration }} min
                </span>
            @endif
            <span>
                <i class="fas fa-eye mr-1"></i>
                Visualizações: 0
            </span>
        </div>
    </div>
    
    <!-- Content Body -->
    <div class="p-6">
        @if($content->content_type === 'video')
            <!-- Video Content -->
            @if($content->video_url)
                <div class="aspect-video bg-gray-900 rounded-lg mb-6">
                    <video controls class="w-full h-full rounded-lg">
                        <source src="{{ $content->video_url }}" type="video/mp4">
                        Seu navegador não suporta o elemento de vídeo.
                    </video>
                </div>
            @else
                <!-- Video Embed with Aspect Ratio -->
                <div class="aspect-video mb-6">
                    <div class="w-full h-full [&>iframe]:w-full [&>iframe]:h-full [&>iframe]:rounded-lg">
                        @if(str_contains($content->content, '<iframe'))
                            {!! $content->content !!}
                        @else
                            <iframe src="{{ $content->content }}"
                                    frameborder="0"
                                    allowfullscreen
                                    class="w-full h-full rounded-lg">
                            </iframe>
                        @endif
                    </div>
                </div>
            @endif
        @elseif($content->content_type === 'text')
            <!-- Text Content -->
            <div class="prose max-w-none">
                {!! $content->content !!}
            </div>
        @elseif($content->content_type === 'pdf')
            <!-- PDF Content -->
            <div class="border border-gray-200 rounded-lg p-4 text-center">
                <i class="fas fa-file-pdf text-red-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Documento PDF</h3>
                <p class="text-gray-600 mb-4">{{ $content->title }}</p>
                @if($content->file_url)
                    <a href="{{ $content->file_url }}" target="_blank" 
                       class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        Baixar PDF
                    </a>
                @endif
            </div>
        @elseif($content->content_type === 'link')
            <!-- Link Content -->
            <div class="border border-gray-200 rounded-lg p-4 text-center">
                <i class="fas fa-external-link-alt text-blue-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Link Externo</h3>
                <p class="text-gray-600 mb-4">{{ $content->description ?? 'Acesse o conteúdo externo' }}</p>
                @if($content->external_url)
                    <a href="{{ $content->external_url }}" target="_blank" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Acessar Link
                    </a>
                @endif
            </div>
        @endif
        
        <!-- Additional Content -->
        @if($content->additional_content)
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Material Complementar</h3>
                <div class="prose max-w-none">
                    {!! $content->additional_content !!}
                </div>
            </div>
        @endif
    </div>
    
    <!-- Navigation Footer -->
    <div class="p-6 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <!-- Previous Button -->
            @if($previousContent)
                <a href="{{ route('student.module.content', [$module->course->slug, $module->slug, $previousContent->id]) }}"
                   class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors">
                    <i class="fas fa-chevron-left mr-2"></i>
                    <span class="text-sm">Anterior</span>
                </a>
            @else
                <div></div>
            @endif
            
            <!-- Progress Dots -->
            <div class="flex items-center space-x-2">
                @foreach($module->contents as $index => $moduleContent)
                    <div class="w-2 h-2 rounded-full {{ $moduleContent->id === $content->id ? 'bg-blue-600' : 'bg-gray-300' }}"></div>
                @endforeach
            </div>
            
            <!-- Next Button -->
            @if($nextContent)
                <a href="{{ route('student.module.content', [$module->course->slug, $module->slug, $nextContent->id]) }}"
                   class="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <span class="text-sm mr-2">Próximo</span>
                    <i class="fas fa-chevron-right"></i>
                </a>
            @else
                <a href="{{ route('student.course.show', $module->course->slug) }}"
                   class="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <span class="text-sm mr-2">Concluir Módulo</span>
                    <i class="fas fa-check"></i>
                </a>
            @endif
        </div>
    </div>
</div>

<style>
    /* Garantir que iframes dentro de containers aspect-video se ajustem corretamente */
    .aspect-video iframe {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0.5rem;
    }
</style>
