<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_students_address', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->foreignId('student_id')->constrained('plg_students')->onDelete('cascade');
            $table->string('country', 50)->default('Brasil');
            $table->string('cep', 10)->nullable();
            $table->string('street', 100)->nullable();
            $table->string('number', 20)->nullable();
            $table->string('neighborhood', 100)->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 50)->nullable();
            $table->string('complement', 100)->nullable();
            $table->boolean('is_primary')->default(true); // Endereço principal
            $table->timestamps();
            $table->softDeletes();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            
            // Índices para performance
            $table->index(['company_id', 'student_id']); // Multi-tenancy + estudante
            $table->index(['student_id', 'is_primary']); // Buscar endereço principal
            $table->index(['cep']); // Busca por CEP
            $table->index(['city', 'state']); // Busca por localização
            $table->index(['is_primary']); // Filtrar endereços principais
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_students_address');
    }
};
