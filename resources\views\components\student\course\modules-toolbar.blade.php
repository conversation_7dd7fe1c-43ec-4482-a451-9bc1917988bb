@props(['search' => '', 'filter' => ''])

<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Módulos do Curso</h2>

    <div class="flex flex-col sm:flex-row gap-3 sm:w-auto w-full">
        <!-- Campo de Busca -->
        <div class="relative flex-1 sm:w-64">
            <input type="text" 
                   id="module-search"
                   placeholder="Buscar módulo..."
                   value="{{ $search }}"
                   class="w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm 
                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
                          text-gray-900 dark:text-gray-300 
                          bg-white dark:bg-gray-700 
                          placeholder-gray-400 dark:placeholder-gray-400
                          transition-all duration-200">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
        </div>

        <!-- Filtro de Status -->
        <div class="relative flex-1 sm:w-48">
            <select name="filter" 
                    id="module-filter"
                    class="w-full appearance-none bg-white dark:bg-gray-700 
                           border border-gray-300 dark:border-gray-600 
                           rounded-lg px-4 py-2.5 text-sm 
                           text-gray-900 dark:text-gray-300 
                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
                           transition-all duration-200 hover:border-gray-400 cursor-pointer">
                <option value="" {{ $filter === '' ? 'selected' : '' }}>Todos os módulos</option>
                <option value="free" {{ $filter === 'free' ? 'selected' : '' }}>Módulos grátis</option>
                <option value="paid" {{ $filter === 'paid' ? 'selected' : '' }}>Módulos pagos</option>
                <option value="completed" {{ $filter === 'completed' ? 'selected' : '' }}>Concluídos</option>
                <option value="in-progress" {{ $filter === 'in-progress' ? 'selected' : '' }}>Em andamento</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </div>
        </div>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('module-search');
    const filterSelect = document.getElementById('module-filter');
    
    // Função para filtrar módulos
    function filterModules() {
        const searchTerm = searchInput.value.toLowerCase();
        const filterValue = filterSelect.value;
        const modules = document.querySelectorAll('[data-module-item]');
        
        modules.forEach(module => {
            const title = module.querySelector('[data-module-title]')?.textContent.toLowerCase() || '';
            const description = module.querySelector('[data-module-description]')?.textContent.toLowerCase() || '';
            const isFree = module.hasAttribute('data-module-free');
            const isCompleted = module.hasAttribute('data-module-completed');
            const isInProgress = module.hasAttribute('data-module-in-progress');
            
            // Verificar busca por texto
            const matchesSearch = searchTerm === '' || 
                                title.includes(searchTerm) || 
                                description.includes(searchTerm);
            
            // Verificar filtro
            let matchesFilter = true;
            if (filterValue === 'free') {
                matchesFilter = isFree;
            } else if (filterValue === 'paid') {
                matchesFilter = !isFree;
            } else if (filterValue === 'completed') {
                matchesFilter = isCompleted;
            } else if (filterValue === 'in-progress') {
                matchesFilter = isInProgress;
            }
            
            // Mostrar/ocultar módulo
            if (matchesSearch && matchesFilter) {
                module.style.display = '';
            } else {
                module.style.display = 'none';
            }
        });
    }
    
    // Event listeners
    searchInput.addEventListener('input', filterModules);
    filterSelect.addEventListener('change', filterModules);
});
</script>
