:root {
    /* Light Theme (default) */
    --dark-100: #ffffff;
    --dark-200: #f8f9fa;
    --dark-300: #e9ecef;
    --dark-400: #dee2e6;

    --accent-200: #212529;
    --accent-300: #343a40;
    --accent-400: #495057;
    --accent-500: #6c757d;
    --accent-600: #868e96;
    --accent-700: #99a0a7;

    --primary-300: #ef4444;
    --primary-400: #dc2626;
    --primary-500: #b91c1c;

    --success-dark: #198754;
    
    /* Netflix Colors */
    --netflix-red: #e50914;
    --netflix-black: #000000;
    --netflix-dark-gray: #181818;

    background-color: var(--dark-100);
}

/* Dark Theme */
.dark {
    --dark-100: #121212;
    --dark-200: #1e1e1e;
    --dark-300: #252525;
    --dark-400: #2c2c2c;

    --accent-200: #f8f9fa;
    --accent-300: #e9ecef;
    --accent-400: #dee2e6;
    --accent-500: #ced4da;
    --accent-600: #adb5bd;
    --accent-700: #868e96;

    --primary-300: #ef4444;
    --primary-400: #dc2626;
    --primary-500: #b91c1c;

    --success-dark: #198754;
    
    /* Netflix Colors */
    --netflix-red: #e50914;
    --netflix-black: #000000;
    --netflix-dark-gray: #181818;

    background-color: var(--dark-100);
}

/* Transições suaves apenas quando o tema estiver carregado */
.theme-loaded * {
    transition: background-color 0.2s ease-in-out,
                color 0.2s ease-in-out,
                border-color 0.2s ease-in-out,
                opacity 0.2s ease-in-out;
}

/* Desativar transições em elementos específicos */
.no-transition {
    transition: none !important;
} 