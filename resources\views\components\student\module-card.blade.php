@props(['module', 'course'])

@php
$accessStatus = $module->access_status ?? [];
$enrollment = $module->enrollment ?? null;
$hasAccess = $accessStatus['has_access'] ?? false;
$isFree = $module->is_free;
$isExpired = ($accessStatus['status'] ?? '') === 'expired';
$isPendingApproval = ($accessStatus['status'] ?? '') === 'pending_approval';
$isNotEnrolled = ($accessStatus['status'] ?? '') === 'not_enrolled';
$isRejected = ($accessStatus['status'] ?? '') === 'rejected';

// Debug: Para módulos pagos sem acesso, forçar isNotEnrolled
if (!$isFree && !$hasAccess) {
    $isNotEnrolled = true;
}
@endphp

<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg overflow-hidden transition-transform transform hover:scale-105 {{ !$hasAccess ? 'opacity-75' : '' }}">
    <!-- Thumbnail -->
    @if($module->hasMedia('thumbnail'))
        @php
            $media = $module->getMedia('thumbnail')->first();
            $thumbnailUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=400&fit=crop&fm=webp';
        @endphp
        <div class="relative aspect-video">
            <img src="{{ $thumbnailUrl }}" alt="{{ $module->title }}" class="w-full object-cover">
            
            <!-- Badge de Status -->
            <div class="absolute top-3 left-3">
                @if($isFree)
                    <span class="bg-emerald-600 text-white text-xs font-semibold px-2 py-1 rounded">GRÁTIS</span>
                @elseif($hasAccess)
                    <span class="bg-green-600 text-white text-xs font-semibold px-2 py-1 rounded">LIBERADO</span>
                @elseif($isExpired)
                    <span class="bg-orange-600 text-white text-xs font-semibold px-2 py-1 rounded">EXPIRADO</span>
                @elseif($isPendingApproval)
                    <span class="bg-yellow-600 text-white text-xs font-semibold px-2 py-1 rounded">PENDENTE</span>
                @elseif($isRejected)
                    <span class="bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded">REJEITADO</span>
                @else
                    <span class="bg-gray-600 text-white text-xs font-semibold px-2 py-1 rounded">BLOQUEADO</span>
                @endif
            </div>

            <!-- Overlay para módulos bloqueados -->
            @if(!$hasAccess)
                <div class="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <div class="text-center text-white">
                        @if($isExpired)
                            <i class="fas fa-clock text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Matrícula Expirada</p>
                        @elseif($isPendingApproval)
                            <i class="fas fa-hourglass-half text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Aguardando Aprovação</p>
                        @elseif($isRejected)
                            <i class="fas fa-times-circle text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Matrícula Rejeitada</p>
                        @elseif($isNotEnrolled)
                            <i class="fas fa-lock text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Módulo Pago</p>
                        @else
                            <i class="fas fa-lock text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Acesso Restrito</p>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    @else
        <div class="relative aspect-video">
            <img src="https://placehold.co/400x500/dc2626/ffffff?text={{ urlencode($module->title) }}"
                 alt="{{ $module->title }}"
                 class="w-full object-cover">

            <!-- Badge de Status -->
            <div class="absolute top-3 left-3">
                @if($isFree)
                    <span class="bg-emerald-600 text-white text-xs font-semibold px-2 py-1 rounded">GRÁTIS</span>
                @elseif($hasAccess)
                    <span class="bg-green-600 text-white text-xs font-semibold px-2 py-1 rounded">LIBERADO</span>
                @elseif($isExpired)
                    <span class="bg-orange-600 text-white text-xs font-semibold px-2 py-1 rounded">EXPIRADO</span>
                @elseif($isPendingApproval)
                    <span class="bg-yellow-600 text-white text-xs font-semibold px-2 py-1 rounded">PENDENTE</span>
                @elseif($isRejected)
                    <span class="bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded">REJEITADO</span>
                @else
                    <span class="bg-gray-600 text-white text-xs font-semibold px-2 py-1 rounded">BLOQUEADO</span>
                @endif
            </div>

            <!-- Overlay para módulos bloqueados -->
            @if(!$hasAccess)
                <div class="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <div class="text-center text-white">
                        @if($isExpired)
                            <i class="fas fa-clock text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Matrícula Expirada</p>
                        @elseif($isPendingApproval)
                            <i class="fas fa-hourglass-half text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Aguardando Aprovação</p>
                        @elseif($isRejected)
                            <i class="fas fa-times-circle text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Matrícula Rejeitada</p>
                        @elseif($isNotEnrolled)
                            <i class="fas fa-lock text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Módulo Pago</p>
                        @else
                            <i class="fas fa-lock text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Acesso Restrito</p>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    @endif

    <!-- Conteúdo -->
    <div class="p-4">
        <h3 class="font-semibold text-lg mb-2 text-zinc-900 dark:text-zinc-100">
            {{ $module->title }}
        </h3>
        
        @if($module->description)
            <div class="text-sm text-zinc-500 dark:text-zinc-400 mb-3 line-clamp-2">
                {!! $module->description !!}
            </div>
        @endif

        <!-- Status de Matrícula -->
        <div class="mb-3">
            @if($isFree)
                <span class="inline-flex items-center bg-emerald-100 text-emerald-700 text-xs font-semibold px-2 py-1 rounded-full">
                    <i class="fas fa-gift mr-1"></i>
                    Gratuito
                </span>
            @elseif($hasAccess)
                <span class="inline-flex items-center bg-green-100 text-green-700 text-xs font-semibold px-2 py-1 rounded-full">
                    <i class="fas fa-check-circle mr-1"></i>
                    Acesso Liberado
                </span>
            @elseif($isExpired)
                <span class="inline-flex items-center bg-orange-100 text-orange-700 text-xs font-semibold px-2 py-1 rounded-full">
                    <i class="fas fa-clock mr-1"></i>
                    Matrícula Expirada
                </span>
            @elseif($isPendingApproval)
                <span class="inline-flex items-center bg-yellow-100 text-yellow-700 text-xs font-semibold px-2 py-1 rounded-full">
                    <i class="fas fa-hourglass-half mr-1"></i>
                    Aguardando Aprovação
                </span>
            @elseif($isRejected)
                <span class="inline-flex items-center bg-red-100 text-red-700 text-xs font-semibold px-2 py-1 rounded-full">
                    <i class="fas fa-times-circle mr-1"></i>
                    Matrícula Rejeitada
                </span>
            @elseif($isNotEnrolled)
                <div class="flex items-center justify-between">
                    <span class="inline-flex items-center bg-gray-100 text-gray-700 text-xs font-semibold px-2 py-1 rounded-full">
                        <i class="fas fa-lock mr-1"></i>
                        Não Matriculado
                    </span>
                    @if($module->price > 0)
                        <span class="text-sm font-bold text-green-600">
                            R$ {{ number_format($module->getCurrentPrice(), 2, ',', '.') }}
                        </span>
                    @endif
                </div>
            @else
                <span class="inline-flex items-center bg-gray-100 text-gray-700 text-xs font-semibold px-2 py-1 rounded-full">
                    <i class="fas fa-lock mr-1"></i>
                    Bloqueado
                </span>
            @endif
        </div>

        <!-- Informações adicionais -->
        <div class="flex items-center justify-between text-sm text-zinc-500 dark:text-zinc-400 mb-4">
            @if($module->contents->count() > 0)
                <div class="flex items-center">
                    <i class="fas fa-file mr-1"></i>
                    <span>{{ $module->contents->count() }} materiais</span>
                </div>
            @endif
            
            @if($module->tests->count() > 0)
                <div class="flex items-center">
                    <i class="fas fa-question-circle mr-1"></i>
                    <span>{{ $module->tests->count() }} testes</span>
                </div>
            @endif
        </div>

        <!-- Botão de Ação -->
        @if($hasAccess)
            <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
               class="w-full inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg bg-green-600 text-white text-sm font-semibold hover:bg-green-700 transition-colors">
                <i class="fas fa-book-open"></i>
                Acessar Módulo
            </a>
        @elseif($isNotEnrolled)
            <button onclick="openContactModal('{{ addslashes($module->title) }}', '{{ number_format($module->getCurrentPrice(), 2, ',', '.') }}')"
                    class="w-full inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg bg-blue-600 text-white text-sm font-semibold hover:bg-blue-700 transition-colors">
                <i class="fas fa-credit-card"></i>
                Solicitar Matrícula
            </button>
        @elseif($isExpired)
            <button onclick="openContactModal('{{ addslashes($module->title) }}', '{{ number_format($module->getCurrentPrice(), 2, ',', '.') }}')"
                    class="w-full inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg bg-orange-600 text-white text-sm font-semibold hover:bg-orange-700 transition-colors">
                <i class="fas fa-refresh"></i>
                Renovar Matrícula
            </button>
        @else
            <button onclick="openContactModal('{{ addslashes($module->title) }}', '{{ number_format($module->getCurrentPrice(), 2, ',', '.') }}')"
                    class="w-full inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg bg-gray-600 text-white text-sm font-semibold hover:bg-gray-700 transition-colors">
                <i class="fas fa-envelope"></i>
                Entrar em Contato
            </button>
        @endif
    </div>
</div>