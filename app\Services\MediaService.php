<?php

namespace App\Services;

use Plank\Mediable\Media;
use Plank\Mediable\MediaUploader;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\MediaResource;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;

class MediaService
{
    protected MediaUploader $uploader;
    protected int $perPage = 18;

    public function __construct(MediaUploader $uploader)
    {
        $this->uploader = $uploader;
    }

    /**
     * Busca mídias com filtros e paginação
     */
    public function getMediaWithFilters(array $filters = [], int $page = 1, int $perPage = null): LengthAwarePaginator
    {
        // Determinar quantidade por página
        if (isset($filters['per_page'])) {
            if ($filters['per_page'] === 'all') {
                $perPage = 1000; // Limite razoável para "todos"
            } else {
                $perPage = (int) $filters['per_page'];
            }
        } else {
            $perPage = $perPage ?? $this->perPage;
        }

        // Criar query base com ordenação
        $query = Media::query();
        
        // Aplicar ordenação
        if (isset($filters['sort']) && $filters['sort'] === 'oldest') {
            $query->orderBy('created_at', 'asc');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Aplicar filtros
        $this->applyFilters($query, $filters);
        
        // Por padrão, excluir mídias órfãs (a menos que seja especificamente solicitado)
        if (empty($filters['orphaned']) || $filters['orphaned'] !== 'true') {
            $this->applyValidMediaFilter($query);
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Upload de mídia melhorado
     */
    public function uploadMedia(UploadedFile $file, array $options = []): Media
    {
        try {
            DB::beginTransaction();

            // Configurar e fazer upload
            $media = $this->configureUploader($file, $options)->upload();

            // Adicionar metadados
            $this->addMetadata($media, $options);

            // Associar a modelo se especificado
            if (!empty($options['mediable']) && !empty($options['tag'])) {
                $options['mediable']->attachMedia($media, $options['tag']);
            }

            DB::commit();

            Log::info('Mídia uploaded', ['media_id' => $media->id]);

            return $media;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro no upload de mídia', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $file->getClientOriginalName(),
                'options' => $options
            ]);
            throw $e;
        }
    }

    /**
     * Atualiza informações da mídia
     */
    public function updateMedia(Media $media, array $data): Media
    {
        $updateData = array_filter([
            'filename' => $data['filename'] ?? null,
            'alt' => $data['alt_text'] ?? null,
            'caption' => $data['caption'] ?? null,
        ], fn($value) => $value !== null);

        if (!empty($updateData)) {
            $media->update($updateData);
        }

        return $media;
    }

    /**
     * Substitui arquivo de mídia existente
     */
    public function replaceMediaFile(Media $media, UploadedFile $newFile): Media
    {
        try {
            DB::beginTransaction();

            // Backup dos metadados
            $metadata = [
                'alt' => $media->alt,
                'caption' => $media->caption,
                'mediables' => $media->mediables()->get()
            ];

            // Remover arquivo antigo
            $this->deleteFile($media);

            // Upload do novo arquivo mantendo o ID
            $newPath = $this->uploader
                ->fromSource($newFile)
                ->toDestination($media->disk, dirname($media->getDiskPath()))
                ->upload();

            // Atualizar registro existente
            $media->update([
                'filename' => $newPath->filename,
                'extension' => $newPath->extension,
                'mime_type' => $newPath->mime_type,
                'size' => $newPath->size,
            ]);

            // Restaurar metadados
            $media->update($metadata);

            // Limpar upload temporário
            $newPath->delete();

            DB::commit();

            return $media->fresh();

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Exclui mídia e arquivo físico
     */
    public function deleteMedia(Media $media): bool
    {
        try {
            DB::beginTransaction();

            $this->deleteFile($media);
            $media->delete();

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Exclui múltiplas mídias
     */
    public function deleteMultipleMedia(array $mediaIds): array
    {
        $results = ['deleted' => 0, 'errors' => []];

        foreach ($mediaIds as $mediaId) {
            try {
                $media = Media::find($mediaId);
                if ($media && $this->deleteMedia($media)) {
                    $results['deleted']++;
                }
            } catch (\Exception $e) {
                $results['errors'][] = "Erro ao excluir mídia ID {$mediaId}";
            }
        }

        return $results;
    }

    /**
     * Limpa mídias órfãs (registros no banco sem arquivo físico)
     * Versão otimizada que verifica todas as mídias mas de forma mais eficiente
     */
    public function cleanOrphanedMedia(): array
    {
        $results = ['deleted' => 0, 'errors' => []];
        $orphanedIds = [];
        $totalChecked = 0;
        
        try {
            // Verificar todas as mídias em chunks menores para melhor performance
            Media::chunk(50, function($mediaChunk) use (&$orphanedIds, &$totalChecked) {
                foreach ($mediaChunk as $media) {
                    $totalChecked++;
                    $path = $media->getDiskPath();
                    $exists = Storage::disk($media->disk)->exists($path);
                    
                    if (!$exists) {
                        $orphanedIds[] = $media->id;
                        Log::info("Mídia órfã encontrada", [
                            'media_id' => $media->id,
                            'filename' => $media->filename,
                            'path' => $path
                        ]);
                    }
                }
            });
            
            Log::info("Verificação de mídias órfãs concluída", [
                'total_checked' => $totalChecked,
                'orphaned_found' => count($orphanedIds)
            ]);
            
            // Excluir mídias órfãs em lote para melhor performance
            if (!empty($orphanedIds)) {
                DB::beginTransaction();
                
                try {
                    // Remover todas as associações de uma vez
                    DB::table('mediables')->whereIn('media_id', $orphanedIds)->delete();
                    
                    // Excluir os registros de mídia
                    $deleted = Media::whereIn('id', $orphanedIds)->delete();
                    $results['deleted'] = $deleted;
                    
                    DB::commit();
                    
                    Log::info("Mídias órfãs excluídas com sucesso", [
                        'deleted_count' => $deleted,
                        'deleted_ids' => $orphanedIds
                    ]);
                    
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error("Erro ao excluir mídias órfãs em lote", ['error' => $e->getMessage()]);
                    $results['errors'][] = "Erro ao excluir mídias órfãs: " . $e->getMessage();
                }
            }
            
        } catch (\Exception $e) {
            Log::error("Erro durante verificação de mídias órfãs", ['error' => $e->getMessage()]);
            $results['errors'][] = "Erro durante verificação: " . $e->getMessage();
        }
        
        return $results;
    }

    /**
     * Obtém estatísticas de mídia
     */
    public function getMediaStats(): array
    {
        $stats = Media::selectRaw('
            COUNT(*) as total,
            SUM(size) as total_size,
            MAX(created_at) as last_upload
        ')->first();

        $byType = Media::selectRaw('
            SUBSTRING_INDEX(mime_type, "/", 1) as type,
            COUNT(*) as count,
            SUM(size) as size
        ')
        ->groupBy('type')
        ->get()
        ->keyBy('type');

        return [
            'total_files' => $stats->total ?? 0,
            'total_size' => $stats->total_size ?? 0,
            'total_size_human' => $this->formatBytes($stats->total_size ?? 0),
            'last_upload' => $stats->last_upload,
            'by_type' => $byType,
        ];
    }

    /**
     * Aplica filtros na query
     */
    private function applyFilters($query, array $filters): void
    {
        // Busca por texto
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function($q) use ($search) {
                $q->where('filename', 'like', "%{$search}%")
                  ->orWhere('alt', 'like', "%{$search}%");
            });
        }

        // Filtro por tipo
        if (!empty($filters['type']) && $filters['type'] !== 'all') {
            $this->applyTypeFilter($query, $filters['type']);
        }

        // Filtro de mídias órfãs (arquivos que não existem fisicamente)
        if (!empty($filters['orphaned']) && $filters['orphaned'] === 'true') {
            $this->applyOrphanedFilter($query);
        }

        // Filtro por data
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
    }

    /**
     * Aplica filtro por tipo de mídia
     */
    private function applyTypeFilter($query, string $type): void
    {
        $typeMap = [
            'images' => 'image/%',
            'documents' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain',
                'application/rtf'
            ],
            'videos' => 'video/%',
            'audio' => 'audio/%',
            'other' => [] // Will be handled specially
        ];

        if (isset($typeMap[$type])) {
            if ($type === 'other') {
                // Para "outros", excluir todos os tipos conhecidos
                $knownMimes = array_merge(
                    ['image/%', 'video/%', 'audio/%'],
                    $typeMap['documents']
                );
                
                $query->where(function($q) use ($knownMimes) {
                    $q->where('mime_type', 'not like', 'image/%')
                      ->where('mime_type', 'not like', 'video/%')
                      ->where('mime_type', 'not like', 'audio/%');
                    
                    foreach ($typeMap['documents'] as $docMime) {
                        $q->where('mime_type', '!=', $docMime);
                    }
                });
            } elseif (is_array($typeMap[$type])) {
                $query->where(function($q) use ($typeMap, $type) {
                    foreach ($typeMap[$type] as $mime) {
                        $q->orWhere('mime_type', '=', $mime);
                    }
                });
            } else {
                $query->where('mime_type', 'like', $typeMap[$type]);
            }
        }
    }

    /**
     * Aplica filtro para mídias órfãs (arquivos que não existem fisicamente)
     * Versão otimizada que verifica apenas um subconjunto por vez
     */
    private function applyOrphanedFilter($query): void
    {
        // Para o filtro, vamos verificar apenas as 100 mídias mais recentes
        // para evitar lentidão na interface
        $recentMediaIds = Media::orderBy('created_at', 'desc')
            ->limit(100)
            ->pluck('id')
            ->toArray();
        
        $orphanedIds = [];
        
        if (!empty($recentMediaIds)) {
            $recentMedia = Media::whereIn('id', $recentMediaIds)->get();
            
            foreach ($recentMedia as $media) {
                $path = $media->getDiskPath();
                $exists = Storage::disk($media->disk)->exists($path);
                
                if (!$exists) {
                    $orphanedIds[] = $media->id;
                }
            }
        }
        
        Log::info("Filtro de mídias órfãs (otimizado)", [
            'checked_recent' => count($recentMediaIds),
            'orphaned_found' => count($orphanedIds),
            'orphaned_ids' => $orphanedIds
        ]);
        
        if (!empty($orphanedIds)) {
            $query->whereIn('id', $orphanedIds);
        } else {
            // Se não há mídias órfãs nas recentes, retornar resultado vazio
            $query->whereRaw('1 = 0');
        }
    }

    /**
     * Configura o uploader
     */
    private function configureUploader(UploadedFile $file, array $options): MediaUploader
    {
        $uploader = $this->uploader
            ->fromSource($file)
            ->toDestination('public', date('Y/m'));

        // Só usar filename customizado se for uma string válida
        if (!empty($options['filename']) && is_string($options['filename'])) {
            $uploader->useFilename($options['filename']);
        }

        return $uploader;
    }

    /**
     * Adiciona metadados à mídia
     */
    private function addMetadata(Media $media, array $options): void
    {
        $metadata = array_filter([
            'alt' => $options['alt_text'] ?? null,
            'caption' => $options['caption'] ?? null,
        ], fn($value) => $value !== null);

        if (!empty($metadata)) {
            $media->update($metadata);
        }
    }

    /**
     * Remove arquivo físico
     */
    private function deleteFile(Media $media): void
    {
        $path = $media->getDiskPath();
        if (Storage::disk($media->disk)->exists($path)) {
            Storage::disk($media->disk)->delete($path);
        }
    }

    /**
     * Formata bytes em formato legível
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Obtém tipos de mídia disponíveis para filtros
     */
    public function getAvailableMediaTypes(): array
    {
        // Obter apenas mídias válidas para os contadores
        $query = Media::selectRaw('mime_type, COUNT(*) as count')
            ->groupBy('mime_type')
            ->orderBy('count', 'desc');
            
        // Aplicar filtro para excluir órfãs dos contadores
        $this->applyValidMediaFilter($query);
        
        $mediaTypes = $query->get();

        $categories = [
            'all' => [
                'label' => 'Todas as Mídias',
                'icon' => 'fas fa-layer-group',
                'count' => $mediaTypes->sum('count')
            ]
        ];

        $imageCount = 0;
        $documentCount = 0;
        $videoCount = 0;
        $audioCount = 0;
        $otherCount = 0;

        foreach ($mediaTypes as $type) {
            $mimeType = $type->mime_type;
            $count = $type->count;

            if (str_starts_with($mimeType, 'image/')) {
                $imageCount += $count;
            } elseif (str_starts_with($mimeType, 'video/')) {
                $videoCount += $count;
            } elseif (str_starts_with($mimeType, 'audio/')) {
                $audioCount += $count;
            } elseif (in_array($mimeType, [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain',
                'application/rtf'
            ])) {
                $documentCount += $count;
            } else {
                $otherCount += $count;
            }
        }

        // Adicionar categorias apenas se houver arquivos desse tipo
        if ($imageCount > 0) {
            $categories['images'] = [
                'label' => 'Imagens',
                'icon' => 'fas fa-image',
                'count' => $imageCount
            ];
        }

        if ($documentCount > 0) {
            $categories['documents'] = [
                'label' => 'Documentos',
                'icon' => 'fas fa-file-alt',
                'count' => $documentCount
            ];
        }

        if ($videoCount > 0) {
            $categories['videos'] = [
                'label' => 'Vídeos',
                'icon' => 'fas fa-video',
                'count' => $videoCount
            ];
        }

        if ($audioCount > 0) {
            $categories['audio'] = [
                'label' => 'Áudio',
                'icon' => 'fas fa-music',
                'count' => $audioCount
            ];
        }

        if ($otherCount > 0) {
            $categories['other'] = [
                'label' => 'Outros',
                'icon' => 'fas fa-file',
                'count' => $otherCount
            ];
        }

        return $categories;
    }

    /**
     * Aplica filtro para mostrar apenas mídias válidas (que existem fisicamente)
     * Versão otimizada que verifica uma amostra e assume que as demais são válidas
     */
    private function applyValidMediaFilter($query): void
    {
        // Para melhor performance, vamos verificar apenas uma amostra das mídias mais recentes
        // e excluir apenas as que sabemos que são órfãs
        $recentMediaIds = Media::orderBy('created_at', 'desc')
            ->limit(200) // Verificar as 200 mais recentes
            ->pluck('id')
            ->toArray();
        
        $orphanedIds = [];
        
        if (!empty($recentMediaIds)) {
            $recentMedia = Media::whereIn('id', $recentMediaIds)->get();
            
            foreach ($recentMedia as $media) {
                $path = $media->getDiskPath();
                $exists = Storage::disk($media->disk)->exists($path);
                
                if (!$exists) {
                    $orphanedIds[] = $media->id;
                }
            }
        }
        
        // Excluir as mídias órfãs encontradas
        if (!empty($orphanedIds)) {
            $query->whereNotIn('id', $orphanedIds);
            
            Log::info("Filtro de mídias válidas aplicado", [
                'checked_recent' => count($recentMediaIds),
                'excluded_orphaned' => count($orphanedIds)
            ]);
        }
    }
} 