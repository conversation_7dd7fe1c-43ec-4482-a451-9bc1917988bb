export class MediaAPI {
    static baseUrl = '/panel/admin/media';
    
    static getCsrfToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        if (!token) {
            throw new Error('Token CSRF não encontrado');
        }
        return token.content;
    }
    
    static async request(url, options = {}) {
        const defaultHeaders = {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': this.getCsrfToken()
        };
        
        // Garantir que o CSRF token seja sempre incluído
        const headers = {
            ...defaultHeaders,
            ...options.headers,
            'X-CSRF-TOKEN': this.getCsrfToken() // Força o token sempre
        };
        
        const config = {
            ...options,
            headers
        };
        
        const response = await fetch(url, config);
        
        if (!response.ok) {
            let errorMessage = `HTTP ${response.status}`;
            try {
                const errorData = await response.json();
                errorMessage = errorData.message || errorData.error || errorMessage;
            } catch (e) {
                // Se não conseguir parsear JSON, usar mensagem padrão
            }
            throw new Error(errorMessage);
        }
        
        return response.json();
    }
    
    // ==================== MÍDIA ====================
    
    static async loadMedia(page = 1, filters = {}) {
        const params = new URLSearchParams({ page, ...filters });
        return this.request(`${this.baseUrl}/load-more?${params}`);
    }
    
    static async uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        return this.request(this.baseUrl, {
            method: 'POST',
            body: formData
        });
    }
    
    static async deleteMedia(id) {
        return this.request(`${this.baseUrl}/${id}`, {
            method: 'DELETE'
        });
    }
    
    static async deleteMultiple(mediaIds) {
        return this.request(`${this.baseUrl}/delete-multiple`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ids: mediaIds })
        });
    }
    
    static async updateMedia(mediaId, data) {
        return this.request(`${this.baseUrl}/${mediaId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
    }
    
    // ==================== MODAL ====================
    
    static async getModalHtml() {
        const response = await fetch(`${this.baseUrl}/modal`);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        return response.text();
    }
    
    // ==================== UTILITÁRIOS ====================
    
    static async getMediaTypes() {
        return this.request(`${this.baseUrl}/types`);
    }
    
    static async cleanOrphaned() {
        return this.request(`${this.baseUrl}/clean-orphaned`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
} 