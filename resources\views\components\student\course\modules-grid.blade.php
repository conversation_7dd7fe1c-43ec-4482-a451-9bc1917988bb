@props(['modules', 'course'])

{{-- Grid responsivo de módulos do curso --}}
<div class="space-y-6">
    @forelse($modules as $index => $module)
        <x-student.course.module-item 
            :module="$module" 
            :index="$index" 
            :course="$course" />
    @empty
        <!-- Estado Vazio -->
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhum módulo encontrado</h3>
            <p class="text-gray-500 dark:text-gray-400">Este curso ainda não possui módulos configurados.</p>
        </div>
    @endforelse
</div>
