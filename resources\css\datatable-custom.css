/* Importar estilos do DataTables */
@import 'datatables.net-dt/css/dataTables.dataTables.css';
@import 'datatables.net-responsive-dt/css/responsive.dataTables.css';

/* Estilo dos selects e input */
.dataTables_wrapper select,
.dataTables_wrapper .dataTables_filter input,
.dataTables_wrapper .dataTables_length select {
    border-radius: 0.375rem !important;
    padding: 0.5rem !important;
    border: 1px solid var(--dark-300) !important;
    background-color: var(--dark-100) !important;
    color: var(--accent-300) !important;
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
    width: 100% !important;
    height: 42px !important;
    transition: all 0.2s !important;
}

.dataTables_wrapper .dataTables_info {
    padding-top: 0;
}

.dtr-control {
    text-align: center !important;
}

/* Células do cabeçalho e corpo */
table.dataTable thead th,
table.dataTable tbody td {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

/* Esconder labels */
.dataTables_filter label,
.dataTables_length label {
    font-size: 0;
    width: 100%;
    display: flex;
}

.dt-footer {
    display: flex;
    flex: 1;
    width: 100%;
    position: relative;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    height: 100%;
    margin: 1.5rem auto;
    align-items: center;
    flex-flow: column-reverse;
}

.dt-info {
    color: #6b7280;
    font-size: 0.875rem;
    padding: 0.5rem 0;
}

.dark .dt-info {
    color: #a1a1aa;
}

.dt-pagination {
    flex-grow: 1;
    text-align: center;
    margin-bottom: 0.5rem;
}

/* Estilos para DataTable */

/* Estilos base para os filtros */
.datatable-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: -1px;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    width: 100%;
}

.datatable-filters .search-filter {
    flex: 1;
    min-width: 200px;
}

.datatable-filters .filter-group {
    flex: 1;
    min-width: 150px;
}

/* Estilos para os inputs e selects */
.datatable-filters select, 
.datatable-filters input[type="search"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #fff;
    color: #1f2937;
}

/* Estilos para dark mode */
.dark .datatable-filters select, 
.dark .datatable-filters input[type="search"] {
    background-color: #18181b;
    border-color: #3f3f46;
    color: #f4f4f5;
}

/* Botão limpar filtros - Estado inativo (cinza) */
.dt-clear-filters {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 12px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    background-color: #f3f4f6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    border: none;
}

/* Botão limpar filtros - Estado ativo (vermelho) */
.dt-clear-filters.active {
    color: #ef4444;
    background-color: #fee2e2;
}

.dt-clear-filters:hover {
    background-color: #e5e7eb;
}

.dt-clear-filters.active:hover {
    background-color: #fecaca;
}

.dark .dt-clear-filters {
    color: #9ca3af;
    background-color: rgba(75, 85, 99, 0.2);
}

.dark .dt-clear-filters.active {
    color: #f87171;
    background-color: rgba(239, 68, 68, 0.1);
}

.dark .dt-clear-filters:hover {
    background-color: rgba(75, 85, 99, 0.3);
}

.dark .dt-clear-filters.active:hover {
    background-color: rgba(239, 68, 68, 0.2);
}

/* Estilos para a tabela */
table.dataTable {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

/* Estilo para células truncadas */
.truncate {
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

/* Responsividade */
@media screen and (max-width: 1024px) {
    .datatable-filters {
        gap: 8px;
    }
    
    .datatable-filters .filter-group,
    .datatable-filters .search-filter {
        flex: 1 0 calc(50% - 8px); /* 2 filtros por linha com gap de 8px */
        min-width: calc(50% - 8px);
    }
    
    /* Botão de limpar sempre ocupa linha inteira */
    .datatable-filters .filter-group:last-child {
        flex: 1 0 100%;
    }
}

@media screen and (max-width: 640px) {
    .datatable-filters .filter-group,
    .datatable-filters .search-filter {
        flex: 1 0 100%;
        min-width: 100%;
    }
    
    .truncate {
        max-width: 200px;
    }
    
    .dtr-data .truncate {
        max-width: 100%;
        white-space: normal;
    }
}

/* Estilos para paginação */
.dataTables_paginate {
    display: flex;
    justify-content: center;
    margin-top: 0;
    padding: 0 !important;
    font-size: 0.875rem;
    width: 100%;
}

.dataTables_paginate .paginate_button {
    display: inline-flex!important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
    padding: 0 0.5rem !important;
    margin: 0 0.125rem !important;
    border-radius: 0.375rem !important;
    /* border: 1px solid #e5e7eb !important;
    background-color: #ffffff !important;
    color: #6b7280 !important; */
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    user-select: none !important;
    line-height: normal !important;
}

.dataTables_paginate .paginate_button:hover {
    background-color: #f3f4f6;
    color: #1f2937;
    border-color: #d1d5db;
}

.dataTables_paginate .paginate_button.current {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
    font-weight: 500;
}

.dataTables_paginate .paginate_button.current:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.dataTables_paginate .paginate_button.disabled,
.dataTables_paginate .paginate_button.disabled:hover {
    color: #d1d5db;
    background-color: #ffffff;
    border-color: #e5e7eb;
    cursor: not-allowed;
    opacity: 0.7;
}

.dataTables_paginate .ellipsis {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    padding: 0 0.5rem;
}

/* Estilos para a paginação no dark mode */
.dark .dataTables_paginate .paginate_button {
    background-color: #27272a;
    color: #a1a1aa;
    border-color: #3f3f46;
}

.dark .dataTables_paginate .paginate_button:hover {
    background-color: #3f3f46;
    color: #e4e4e7;
    border-color: #52525b;
}

.dark .dataTables_paginate .paginate_button.current {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.dark .dataTables_paginate .paginate_button.current:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.dark .dataTables_paginate .paginate_button.disabled,
.dark .dataTables_paginate .paginate_button.disabled:hover {
    color: #52525b;
    background-color: #27272a;
    border-color: #3f3f46;
}

/* Responsividade para paginação */
@media screen and (max-width: 640px) {
    .dataTables_paginate {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dataTables_paginate .paginate_button {
        min-width: 2rem;
        height: 2rem;
        margin-bottom: 0.5rem;
    }
}

/* Remover os pseudoelementos */
.dataTables_paginate .previous:before,
.dataTables_paginate .next:before {
    content: none !important;
}

/* Layout específico para desktop */
@media (min-width: 768px) {
    .dt-footer {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        max-width: 100%;
        padding: 0.75rem 1.5rem;
    }
    
    .dt-pagination {
        margin-bottom: 0;
        display: flex;
        justify-content: flex-end;
        width: auto;
    }
    
    .dt-info {
        text-align: left;
        color: #9ca3af;
        font-size: 1rem;
        padding: 0;
        margin-right: auto;
    }
    
    .dataTables_paginate {
        justify-content: flex-end;
        width: auto;
        margin-left: auto;
        padding: 0;
    }
    
    /* Estilo minimalista para a paginação no desktop */
    .dataTables_paginate .paginate_button {
        border: none;
        background-color: transparent;
        color: #9ca3af;
        min-width: 1.75rem;
        height: 1.75rem;
        padding: 0;
        margin: 0 0.125rem;
        font-size: 0.875rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .dataTables_paginate .paginate_button.current {
        background-color: transparent;
        color: #111827;
        font-weight: 500;
    }
    
    .dataTables_paginate .paginate_button:hover {
        background-color: transparent;
        color: #4b5563;
    }
    
    .dataTables_paginate .paginate_button.previous,
    .dataTables_paginate .paginate_button.next {
        font-size: 0.875rem;
        color: #9ca3af;
    }
    
    .dataTables_paginate .paginate_button.disabled {
        opacity: 0.4;
        color: #d1d5db;
    }
    
    .dataTables_paginate .ellipsis {
        color: #9ca3af;
    }
    
    .dark .dataTables_paginate .paginate_button {
        background-color: transparent;
        color: #71717a;
    }
    
    .dark .dataTables_paginate .paginate_button.current {
        color: #e4e4e7;
        background-color: transparent;
    }
    
    .dark .dataTables_paginate .paginate_button:hover {
        color: #a1a1aa;
    }
    
    /* Estilo para a informação "Mostrando X até Y de Z registros" */
    .dataTables_info {
        font-size: 1rem;
        color: #9ca3af;
        font-weight: 400;
        white-space: nowrap;
    }
    
    .dark .dataTables_info {
        color: #71717a;
    }
}

/* Tooltip de imagem para thumbnails */
.image-tooltip {
    position: fixed;
    z-index: 9999;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 8px;
    max-width: 300px;
    max-height: 300px;
    display: none;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

.dark .image-tooltip {
    background: #18181b;
    border-color: #3f3f46;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.image-tooltip img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    object-fit: cover;
}

.image-tooltip .tooltip-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-top: 6px;
    text-align: center;
}

.dark .image-tooltip .tooltip-title {
    color: #d1d5db;
}

/* Hover effect para thumbnails */
.thumbnail-hover {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.thumbnail-hover:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}