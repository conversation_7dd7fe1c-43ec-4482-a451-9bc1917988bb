import { MediaManagerCore } from './MediaManagerCore.js';
import { MediaRenderer } from './MediaRenderer.js';
import { SidebarManager } from './SidebarManager.js';
import { MediaManagerControls } from './MediaManagerControls.js';

export class MediaManagerStandalone {
    constructor(container, options = {}) {
        this.container = container;
        
        // Detectar target do DOM se não especificado
        const target = options.target || this.container.dataset.target || 'gallery';
        
        this.core = new MediaManagerCore({
            target: target,
            selectable: true,
            ...options
        });
        
        this.selectionControls = null;
        this.sidebarManager = null;
        this.controls = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        if (this.isInitialized || !this.container) return;
        
        // Proteção contra inicialização duplicada
        if (this.container.dataset.initialized === 'true') {
            return;
        }
        this.container.dataset.initialized = 'true';
        this.isInitialized = true;

        // Configurar controles de seleção se necessário
        const target = this.container.dataset.target || 'gallery';
        if (target === 'gallery' || this.container.dataset.multiple === 'true') {
            this.createSelectionControls();
        }

        // Configurar eventos
        this.bindEvents();
        
        // Inicializar sidebar manager
        this.initializeSidebar();
        
        // Inicializar controles de paginação e ordenação
        this.initializeControls();
        
        // Carregar filtros e mídia inicial
        await this.loadCategoryFilters();
        await this.refresh();
    }

    // ==================== EVENTOS ====================

    bindEvents() {
        this.bindUploadEvents();
        this.bindSearchEvents();
        this.bindSelectionControlsEvents();
        this.bindOrphanedButton();
    }

    bindUploadEvents() {
        // Buscar input de upload
        let uploadInput = this.container.parentNode.querySelector('.media-upload-input');
        if (!uploadInput) {
            uploadInput = document.querySelector('.media-upload-input');
        }

        if (uploadInput) {
            // Remover listeners anteriores
            if (this._uploadHandler) {
                uploadInput.removeEventListener('change', this._uploadHandler);
            }

            this._uploadHandler = async (e) => {
                if (e.target.files && e.target.files.length > 0) {
                    await this.handleUpload(e.target.files);
                    e.target.value = ''; // Limpar input
                }
            };

            uploadInput.addEventListener('change', this._uploadHandler);
        }
    }

    bindSearchEvents() {
        const searchInput = this.container.parentNode.querySelector('#mediaSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.resetOrphanedButton();
                    this.loadMedia(1, { search: e.target.value });
                }, 300);
            });
        }
    }

    bindSelectionControlsEvents() {
        if (!this.selectionControls) return;

        const selectPageBtn = this.selectionControls.querySelector('.select-page-btn');
        const selectAllBtn = this.selectionControls.querySelector('.select-all-btn');
        const clearSelectionBtn = this.selectionControls.querySelector('.clear-selection-btn');
        const deleteSelectedBtn = this.selectionControls.querySelector('.delete-selected-btn');

        if (selectPageBtn) {
            selectPageBtn.replaceWith(selectPageBtn.cloneNode(true));
            this.selectionControls.querySelector('.select-page-btn').addEventListener('click', () => {
                this.toggleSelectAll();
            });
        }

        if (selectAllBtn) {
            selectAllBtn.replaceWith(selectAllBtn.cloneNode(true));
            this.selectionControls.querySelector('.select-all-btn').addEventListener('click', async () => {
                await this.selectAllItems();
            });
        }

        if (clearSelectionBtn) {
            clearSelectionBtn.replaceWith(clearSelectionBtn.cloneNode(true));
            this.selectionControls.querySelector('.clear-selection-btn').addEventListener('click', () => {
                this.core.clearSelection();
                this.updateUI();
            });
        }

        if (deleteSelectedBtn) {
            deleteSelectedBtn.replaceWith(deleteSelectedBtn.cloneNode(true));
            this.selectionControls.querySelector('.delete-selected-btn').addEventListener('click', () => {
                this.deleteSelected();
            });
        }
    }

    bindOrphanedButton() {
        setTimeout(() => {
            const cleanOrphanedBtn = document.querySelector('#cleanOrphanedBtn');
            
            if (cleanOrphanedBtn && !cleanOrphanedBtn.dataset.bound) {
                cleanOrphanedBtn.dataset.bound = 'true';
                cleanOrphanedBtn.dataset.state = 'search';
                
                cleanOrphanedBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    
                    const currentState = cleanOrphanedBtn.dataset.state || 'search';
                    
                    if (currentState === 'search') {
                        await this.searchOrphanedMedia();
                        cleanOrphanedBtn.dataset.state = 'clean';
                    } else {
                        await this.cleanOrphanedMedia();
                        cleanOrphanedBtn.dataset.state = 'search';
                        this.resetOrphanedButton();
                    }
                });
                
                this.resetOrphanedButton();
            }
        }, 100);
    }

    // ==================== CONTROLES DE SELEÇÃO ====================

    createSelectionControls() {
        const existingControls = this.container.parentNode.querySelector('.selection-controls');
        if (existingControls) {
            this.selectionControls = existingControls;
            return;
        }

        const options = {
            isModal: false,
            target: this.core.options.target
        };

        this.selectionControls = MediaRenderer.renderSelectionControls(
            this.container, 
            this.core.getSelectedCount(),
            options
        );
    }

    toggleSelectAll() {
        const currentPageItems = Array.from(this.container.querySelectorAll('.media-item')).map(item => ({
            id: parseInt(item.dataset.id),
            ...this.core.allMediaItems.get(parseInt(item.dataset.id))
        }));

        this.core.toggleSelectAll(currentPageItems);
        this.updateUI();
    }

    async selectAllItems() {
        try {
            await this.core.selectAllItems();
            this.updateUI();
        } catch (error) {
            console.error('Erro ao selecionar todos os itens:', error);
        }
    }

    // ==================== CARREGAMENTO ====================

    async loadMedia(page = 1, filters = {}) {
        try {
            MediaRenderer.showLoading(this.container);
            const data = await this.core.loadMedia(page, filters);
            this.renderMedia(data.items, data.pagination);
        } catch (error) {
            MediaRenderer.showError(this.container);
        }
    }

    async loadCategoryFilters() {
        try {
            const categories = await this.core.loadMediaTypes();
            const sidebar = document.querySelector('.w-64');
            if (sidebar) {
                MediaRenderer.renderCategoryFilters(sidebar, categories, (filters) => {
                    this.resetOrphanedButton();
                    this.loadMedia(1, filters);
                });
            }
        } catch (error) {
            console.error('Erro ao carregar filtros:', error);
        }
    }

    // ==================== RENDERIZAÇÃO ====================

    renderMedia(items, pagination) {
        // Armazenar informações de paginação
        this.lastPagination = pagination;
        
        const selectedIds = new Set(this.core.selectedMedia.keys());
        
        MediaRenderer.renderMediaGrid(this.container, items, selectedIds);
        
        // Adicionar eventos aos itens
        this.container.querySelectorAll('.media-item').forEach(item => {
            const mediaId = parseInt(item.dataset.id);
            const media = this.core.allMediaItems.get(mediaId);
            
            if (media) {
                // Evento de seleção
                item.addEventListener('click', (e) => {
                    if (e.target.closest('.media-actions')) return;
                    this.core.toggleItemSelection(mediaId, media);
                    this.updateUI();
                });

                // Eventos dos botões de ação
                item.querySelector('.view-btn')?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    MediaRenderer.openPreview(media, this.core.getFileIcon.bind(this.core));
                });

                item.querySelector('.delete-btn')?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.deleteMedia(media);
                });
            }
        });

        // Renderizar paginação
        if (pagination) {
            MediaRenderer.renderPagination(this.container, pagination, (page) => {
                this.loadMedia(page, this.core.currentFilters);
            });
        }

        this.updateUI();
    }

    updateUI() {
        // Atualizar seleção visual
        this.container.querySelectorAll('.media-item').forEach(item => {
            const mediaId = parseInt(item.dataset.id);
            const isSelected = this.core.isSelected(mediaId);
            
            if (isSelected) {
                item.classList.add('selected', 'border-blue-500', 'bg-blue-50');
            } else {
                item.classList.remove('selected', 'border-blue-500', 'bg-blue-50');
            }
        });

        // Atualizar controles de seleção com informações de paginação
        if (this.selectionControls) {
            const pageItems = this.container.querySelectorAll('.media-item').length;
            const options = {
                totalItems: this.lastPagination?.total || pageItems,
                pageItems: pageItems,
                isModal: false,
                target: this.core.options.target
            };
            MediaRenderer.updateSelectionControls(this.selectionControls, this.core.getSelectedCount(), options);
        }

        // Atualizar indicadores globais
        this.updateGlobalIndicators();
    }

    updateGlobalIndicators() {
        const selectedItems = this.core.getSelectedArray();
        
        // Atualizar contadores
        document.querySelectorAll('[data-media-count]').forEach(counter => {
            counter.textContent = selectedItems.length;
        });

        // Atualizar botões dependentes de seleção
        document.querySelectorAll('[data-requires-selection]').forEach(button => {
            button.disabled = selectedItems.length === 0;
        });
    }

    // ==================== AÇÕES ====================

    async handleUpload(files) {
        const uploadBtn = document.querySelector('.media-upload-btn');
        const uploadIcon = uploadBtn?.querySelector('i');
        const uploadSpan = uploadBtn?.querySelector('span');

        if (uploadBtn) {
            uploadBtn.disabled = true;
            if (uploadIcon) uploadIcon.className = 'fas fa-spinner fa-spin';
            if (uploadSpan) uploadSpan.textContent = 'Enviando...';
        }

        try {
            await this.core.uploadFiles(files);
            await this.refresh();
            await this.loadCategoryFilters();
        } catch (error) {
            console.error('Erro no upload:', error);
        } finally {
            if (uploadBtn) {
                uploadBtn.disabled = false;
                if (uploadIcon) uploadIcon.className = 'fas fa-upload';
                if (uploadSpan) uploadSpan.textContent = 'Upload';
            }
        }
    }

    async deleteSelected() {
        try {
            const success = await this.core.deleteSelected();
            if (success) {
                await this.refresh();
                await this.loadCategoryFilters();
            }
        } catch (error) {
            console.error('Erro ao excluir selecionados:', error);
        }
    }

    async deleteMedia(media) {
        try {
            const success = await this.core.deleteMedia(media);
            if (success) {
                await this.refresh();
                await this.loadCategoryFilters();
            }
        } catch (error) {
            console.error('Erro ao excluir mídia:', error);
        }
    }

    // ==================== MÍDIAS ÓRFÃS ====================

    async searchOrphanedMedia() {
        const cleanOrphanedBtn = document.querySelector('#cleanOrphanedBtn');
        const btnTextElement = document.querySelector('#cleanOrphanedBtnText');
        const btnIconElement = document.querySelector('#cleanOrphanedBtnIcon');

        try {
            if (cleanOrphanedBtn) cleanOrphanedBtn.disabled = true;
            if (btnTextElement) btnTextElement.textContent = 'Buscando...';
            if (btnIconElement) btnIconElement.className = 'fas fa-spinner fa-spin mr-2';

            const data = await this.core.searchOrphanedMedia();
            this.renderMedia(data.items, data.pagination);

            const orphanedCount = data.pagination.total || 0;

            if (btnTextElement) {
                btnTextElement.textContent = orphanedCount > 0 
                    ? `Limpar Mídias Órfãs (${orphanedCount})`
                    : 'Nenhuma mídia órfã encontrada';
            }
            if (btnIconElement) {
                btnIconElement.className = orphanedCount > 0 
                    ? 'fas fa-broom mr-2'
                    : 'fas fa-check mr-2';
            }

            if (cleanOrphanedBtn) {
                cleanOrphanedBtn.disabled = orphanedCount === 0;
            }

        } catch (error) {
            this.resetOrphanedButton();
        }
    }

    async cleanOrphanedMedia() {
        const cleanOrphanedBtn = document.querySelector('#cleanOrphanedBtn');
        const btnTextElement = document.querySelector('#cleanOrphanedBtnText');
        const btnIconElement = document.querySelector('#cleanOrphanedBtnIcon');

        try {
            if (cleanOrphanedBtn) cleanOrphanedBtn.disabled = true;
            if (btnTextElement) btnTextElement.textContent = 'Limpando...';
            if (btnIconElement) btnIconElement.className = 'fas fa-spinner fa-spin mr-2';

            const success = await this.core.cleanOrphanedMedia();
            if (success) {
                await this.refresh();
                await this.loadCategoryFilters();
            }
        } catch (error) {
            console.error('Erro ao limpar mídias órfãs:', error);
        }
    }

    resetOrphanedButton() {
        const cleanOrphanedBtn = document.querySelector('#cleanOrphanedBtn');
        const btnTextElement = document.querySelector('#cleanOrphanedBtnText');
        const btnIconElement = document.querySelector('#cleanOrphanedBtnIcon');

        if (cleanOrphanedBtn) {
            cleanOrphanedBtn.disabled = false;
            cleanOrphanedBtn.dataset.state = 'search';
        }
        if (btnTextElement) btnTextElement.textContent = 'Buscar Mídias Órfãs';
        if (btnIconElement) btnIconElement.className = 'fas fa-search mr-2';

        // Limpar filtros se estivermos visualizando órfãs
        if (this.core.currentFilters.orphaned === 'true') {
            this.loadMedia(1, {});
        }
    }

    // ==================== UTILITÁRIOS ====================

    async refresh() {
        await this.loadMedia(this.core.currentPage, this.core.currentFilters);
    }

    getSelected() {
        return this.core.getSelectedArray();
    }

    clearSelection() {
        this.core.clearSelection();
        this.updateUI();
    }

    // ==================== SIDEBAR ====================
    
    initializeSidebar() {
        this.sidebarManager = new SidebarManager(this.container.parentNode || document);
    }

    // ==================== CONTROLES ====================
    
    initializeControls() {
        this.controls = new MediaManagerControls(this.core);
    }

    destroy() {
        // Limpar controles de seleção
        MediaRenderer.hideSelectionControls();
        
        // Limpar sidebar manager
        if (this.sidebarManager) {
            this.sidebarManager.destroy();
        }
        
        // Limpar controles
        if (this.controls) {
            this.controls = null;
        }
        
        this.core.destroy();
        this.isInitialized = false;
    }
} 