@props(['course'])

<a href="{{ is_object($course) ? route('student.course.show', $course->slug) : '#' }}" class="block group">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg overflow-hidden transition-transform transform hover:scale-105">
        <!-- Thumbnail -->
        @if(is_object($course) && $course->hasMedia('thumbnail'))
            @php
                $media = $course->getMedia('thumbnail')->first();
                $thumbnailUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=400&fit=crop&fm=webp';
            @endphp
            <div class="relative aspect-video">
                <img src="{{ $thumbnailUrl }}" alt="{{ is_object($course) ? $course->title : $course['title'] }}" class="w-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
        @elseif(is_array($course) && isset($course['image']))
            <div class="relative aspect-video">
                <img src="{{ $course['image'] }}" alt="{{ $course['title'] }}" class="w-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
        @else
            <div class="relative aspect-video">
                <img src="https://placehold.co/400x500/dc2626/ffffff?text={{ urlencode(is_object($course) ? $course->title : $course['title']) }}"
                     alt="{{ is_object($course) ? $course->title : $course['title'] }}"
                     class="w-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
        @endif

        <!-- Conteúdo -->
        <div class="p-4">
            <h3 class="font-semibold text-lg mb-2 text-zinc-900 dark:text-zinc-100">
                {{ is_object($course) ? $course->title : $course['title'] }}
            </h3>
            <p class="text-sm text-zinc-500 dark:text-zinc-400 mb-4">
                {{ is_object($course) ? ($course->short_description ?? '') : '' }}
            </p>
            
            <!-- Informações adicionais -->
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center text-zinc-500 dark:text-zinc-400">
                    <i class="fas fa-clock mr-2"></i>
                    <span>
                        @if(is_object($course))
                            {{ $course->duration_minutes ?? 0 }} min
                        @else
                            {{ $course['duration'] ?? '0 min' }}
                        @endif
                    </span>
                </div>
                <div class="flex items-center text-zinc-500 dark:text-zinc-400">
                    <i class="fas fa-book mr-2"></i>
                    <span>
                        @if(is_object($course))
                            {{ $course->modules->count() }} módulos
                        @else
                            {{ $course['modules_count'] ?? 0 }} módulos
                        @endif
                    </span>
                </div>
            </div>
        </div>
    </div>
</a>