@props(['test', 'course', 'module', 'user' => null])

@php
    $user = $user ?? auth()->user();
    $canTake = $user ? $user->canTakeTest($test) : false;
    $lastAttempt = $user ? $user->attempts()
        ->where('test_id', $test->id)
        ->latest('finished_at')
        ->first() : null;
    $hasPassed = $lastAttempt && $lastAttempt->passed;
@endphp

<div class="bg-white border border-gray-200 rounded-lg p-4 mb-4">
    <!-- Quiz Header -->
    <div class="flex items-start justify-between mb-3">
        <div class="flex-1">
            <h4 class="font-semibold text-gray-900 mb-1">{{ $test->name }}</h4>
            <div class="flex items-center space-x-2 mb-2">
                @if($test->isQuiz())
                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                        Quiz de Treino
                    </span>
                @else
                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                        Teste Final
                    </span>
                @endif
            </div>
        </div>
        
        <!-- Status Icon -->
        <div class="flex-shrink-0 ml-3">
            @if($hasPassed)
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-check text-white text-sm"></i>
                </div>
            @elseif($lastAttempt)
                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-clock text-white text-sm"></i>
                </div>
            @else
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <i class="fas fa-play text-gray-600 text-sm"></i>
                </div>
            @endif
        </div>
    </div>
    
    <!-- Quiz Info -->
    <div class="text-sm text-gray-600 space-y-1 mb-3">
        <p>
            <i class="fas fa-question-circle mr-1"></i>
            {{ is_array($test->questions) ? count($test->questions) : 0 }} questões
        </p>
        @if($test->time_limit)
            <p>
                <i class="fas fa-clock mr-1"></i>
                {{ $test->time_limit }} min
            </p>
        @endif
        @if($test->isFinalTest())
            <p>
                <i class="fas fa-target mr-1"></i>
                Nota mínima: {{ $test->passing_score }}%
            </p>
        @endif
    </div>

    <!-- Last Attempt Status -->
    @if($lastAttempt)
        <div class="mb-3 p-3 rounded-lg {{ $hasPassed ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200' }}">
            <div class="flex items-center justify-between text-sm">
                <span class="font-medium {{ $hasPassed ? 'text-green-800' : 'text-gray-800' }}">
                    @if($hasPassed)
                        <i class="fas fa-check-circle mr-1"></i>
                        Aprovado
                    @else
                        <i class="fas fa-times-circle mr-1"></i>
                        Última tentativa
                    @endif
                </span>
                <span class="{{ $hasPassed ? 'text-green-600' : 'text-gray-600' }}">
                    {{ $lastAttempt->score }}%
                </span>
            </div>
            <div class="text-xs {{ $hasPassed ? 'text-green-600' : 'text-gray-500' }} mt-1">
                {{ $lastAttempt->completed_at ? $lastAttempt->completed_at->format('d/m/Y H:i') : 'Em andamento' }}
            </div>
        </div>
    @endif

    <!-- Action Buttons -->
    <div class="space-y-2">
        @if($hasPassed)
            <!-- Already Passed -->
            <div class="text-center mb-2">
                <span class="text-green-600 text-sm font-medium">
                    <i class="fas fa-trophy mr-1"></i>
                    Concluído!
                </span>
            </div>
            
            @if($test->isQuiz())
                <button class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-redo mr-1"></i>
                    Refazer Quiz
                </button>
            @endif
            
        @elseif($canTake)
            <!-- Can Take Test -->
            @if($test->isQuiz())
                <button class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-play mr-1"></i>
                    {{ $lastAttempt ? 'Iniciar Quiz' : 'Tentar Novamente' }}
                </button>
            @else
                <button class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-graduation-cap mr-1"></i>
                    {{ $lastAttempt ? 'Iniciar Teste' : 'Tentar Novamente' }}
                </button>
            @endif
            
        @else
            <!-- Cannot Take (Cooldown) -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-2">
                <div class="text-yellow-800 text-sm font-medium mb-1">
                    <i class="fas fa-clock mr-1"></i>
                    Tempo de espera ativo
                </div>
                <div class="text-yellow-600 text-xs">
                    Disponível em: {{ $user ? $user->getTestCooldownRemaining($test) : 'N/A' }}
                </div>
            </div>
        @endif
        
        <!-- Secondary Actions -->
        @if($lastAttempt)
            <div class="flex space-x-2">
                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-2 rounded-lg text-xs font-medium transition-colors">
                    <i class="fas fa-eye mr-1"></i>
                    Resultados
                </button>
                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-2 rounded-lg text-xs font-medium transition-colors">
                    <i class="fas fa-history mr-1"></i>
                    Histórico
                </button>
            </div>
        @endif
    </div>
</div>
