document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const sidebar = document.querySelector('.sidebar');
    
    // Verificar se o sidebar existe antes de continuar
    if (!sidebar) return;
    
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarToggleIcon = sidebarToggle ? sidebarToggle.querySelector('i') : null;
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileOverlay = document.getElementById('mobile-overlay');
    const closeMenuButton = document.getElementById('close-menu-button');
    
    // Desktop menu items
    const menuItems = sidebar.querySelectorAll('.has-submenu');
    
    // Mobile menu items
    const mobileMenuItems = mobileSidebar ? mobileSidebar.querySelectorAll('.has-submenu') : [];
    
    // Logo elements
    const logoFull = sidebar.querySelector('.logo-full');
    const logoMini = sidebar.querySelector('.logo-mini');
    
    // Estado do sidebar
    let isSidebarOpen = localStorage.getItem('sidebarOpen') !== 'false'; // Padrão: aberto

    // ========== DESKTOP BEHAVIOR ==========
    
    // Função para atualizar o estado do sidebar (desktop)
    function updateSidebarState() {
        // Salvar estado no localStorage
        localStorage.setItem('sidebarOpen', isSidebarOpen);
        
        // Atualizar classes CSS
        if (isSidebarOpen) {
            sidebar.classList.remove('w-16');
            sidebar.classList.add('w-64');
            sidebar.classList.remove('sidebar-collapsed');
            
            // Mostrar textos dos menus
            sidebar.querySelectorAll('.menu-text').forEach(text => {
                text.classList.remove('hidden');
            });

            // Mostrar as setas dos submenus
            sidebar.querySelectorAll('.menu-arrow').forEach(arrow => {
                arrow.classList.remove('hidden');
            });
            
            // Esconder hovers cards se visíveis
            document.querySelectorAll('.hover-card').forEach(card => {
                card.classList.add('hidden');
            });
            
            // Mostrar logo completo
            if (logoFull) logoFull.classList.remove('hidden');
            if (logoMini) logoMini.classList.add('hidden');
            
            // Mudar o ícone para indicar que vai recolher (seta para esquerda)
            if (sidebarToggleIcon) {
                sidebarToggleIcon.classList.remove('fa-chevron-right');
                sidebarToggleIcon.classList.add('fa-chevron-left');
            }
        } else {
            sidebar.classList.remove('w-64');
            sidebar.classList.add('w-16');
            sidebar.classList.add('sidebar-collapsed');
            
            // Esconder textos dos menus
            sidebar.querySelectorAll('.menu-text').forEach(text => {
                text.classList.add('hidden');
            });

            // Esconder as setas dos submenus
            sidebar.querySelectorAll('.menu-arrow').forEach(arrow => {
                arrow.classList.add('hidden');
            });
            
            // Fechar todos os submenus
            sidebar.querySelectorAll('.submenu').forEach(submenu => {
                submenu.classList.add('hidden');
            });
            
            // Resetar setas dos submenus
            sidebar.querySelectorAll('.menu-arrow').forEach(arrow => {
                arrow.classList.remove('active');
            });
            
            // Mostrar logo mini (apenas o "T")
            if (logoFull) logoFull.classList.add('hidden');
            if (logoMini) logoMini.classList.remove('hidden');
            
            // Mudar o ícone para indicar que vai expandir (seta para direita)
            if (sidebarToggleIcon) {
                sidebarToggleIcon.classList.remove('fa-chevron-left');
                sidebarToggleIcon.classList.add('fa-chevron-right');
            }
        }
    }

    // Inicializar estado do sidebar para desktop
    updateSidebarState();

    // Adicionar event listener ao botão de toggle para desktop
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            isSidebarOpen = !isSidebarOpen;
            updateSidebarState();
        });
    }
    
    // ========== MOBILE BEHAVIOR ==========
    
    // Funções para controlar o menu mobile (completo on/off)
    function openMobileMenu() {
        if (mobileOverlay) mobileOverlay.classList.remove('hidden');
        if (mobileSidebar) mobileSidebar.classList.remove('hidden');
    }
    
    function closeMobileMenu() {
        if (mobileOverlay) mobileOverlay.classList.add('hidden');
        if (mobileSidebar) mobileSidebar.classList.add('hidden');
    }
    
    // Event listeners para controles móveis
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', openMobileMenu);
    }
    
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', closeMobileMenu);
    }
    
    if (closeMenuButton) {
        closeMenuButton.addEventListener('click', closeMobileMenu);
    }

    // ========== SUBMENU HANDLING (BOTH DESKTOP & MOBILE) ==========
    
    // Função para gerenciar o clique nos submenus
    function handleSubmenuClick(e) {
        e.preventDefault();
        
        // Obter elementos e verificar se é mobile ou não
        const isMobile = this.closest('#mobile-sidebar') !== null;
        
        // No mobile, sempre podemos abrir submenus, no desktop apenas se o sidebar estiver expandido
        if (!isMobile && !isSidebarOpen) return;
        
        const submenu = this.nextElementSibling;
        const arrow = this.querySelector('.menu-arrow');
        
        // Fechar outros submenus no mesmo contexto (mobile ou desktop)
        const parent = isMobile ? mobileSidebar : sidebar;
        parent.querySelectorAll('.submenu').forEach(otherSubmenu => {
            if (otherSubmenu !== submenu) {
                otherSubmenu.classList.add('hidden');
                const otherArrow = otherSubmenu.previousElementSibling.querySelector('.menu-arrow');
                if (otherArrow) {
                    otherArrow.classList.remove('active');
                }
            }
        });
        
        // Toggle do submenu atual
        submenu.classList.toggle('hidden');
        if (arrow) arrow.classList.toggle('active');
    }

    // Gerenciar cliques nos itens com submenu - desktop
    menuItems.forEach(item => {
        item.addEventListener('click', handleSubmenuClick);
    });
    
    // Gerenciar cliques nos itens com submenu - mobile
    mobileMenuItems.forEach(item => {
        item.addEventListener('click', handleSubmenuClick);
    });

    // ========== HOVER CARDS FOR COLLAPSED SIDEBAR ==========
    
    // Implementar hover cards para sidebar colapsado
    sidebar.querySelectorAll('.sidebar-item').forEach(item => {
        // Criar hover cards - só visíveis quando sidebar colapsado
        if (item.classList.contains('has-submenu-parent')) {
            const hoverCard = document.createElement('div');
            hoverCard.className = 'hover-card fixed left-16 border border-[#2c2c2c] rounded-md shadow-lg z-50 hidden';
            
            // Pegar o label do menu e itens do submenu
            const menuText = item.querySelector('.menu-text');
            if (!menuText) return;
            
            const menuLabel = menuText.textContent.trim();
            const submenu = item.querySelector('.submenu');
            if (!submenu) return;
            
            const submenuItems = Array.from(submenu.querySelectorAll('a'));
            
            // Construir o conteúdo do hover card
            let hoverCardContent = `
                <div class="p-2 border-b border-[#2c2c2c]">
                    <h4 class="text-sm font-medium text-gray-300">${menuLabel}</h4>
                </div>
                <div class="py-1 max-h-[300px] overflow-auto">
            `;
            
            submenuItems.forEach(subItem => {
                const href = subItem.getAttribute('href');
                const label = subItem.textContent.trim();
                hoverCardContent += `
                    <a href="${href}" class="block px-3 py-2 text-sm text-gray-300 hover:bg-[#2c2c2c]">
                        ${label}
                    </a>
                `;
            });
            
            hoverCardContent += '</div>';
            hoverCard.innerHTML = hoverCardContent;
            document.body.appendChild(hoverCard);
            
            // Mostrar/esconder hover card ao passar o mouse
            const menuIcon = item.querySelector('.has-submenu');
            if (!menuIcon) return;
            
            menuIcon.addEventListener('mouseenter', function() {
                if (!isSidebarOpen) {
                    const rect = menuIcon.getBoundingClientRect();
                    hoverCard.style.top = `${rect.top}px`;
                    hoverCard.classList.remove('hidden');
                }
            });
            
            menuIcon.addEventListener('mouseleave', function() {
                setTimeout(() => {
                    if (!hoverCard.matches(':hover')) {
                        hoverCard.classList.add('hidden');
                    }
                }, 100);
            });
            
            hoverCard.addEventListener('mouseleave', function() {
                hoverCard.classList.add('hidden');
            });
        }
    });

    // ========== WINDOW RESIZE HANDLING ==========
    
    // Lidar com redimensionamento da janela
    window.addEventListener('resize', function() {
        const isMobile = window.innerWidth < 768;
        
        if (isMobile) {
            // Sempre fechar o sidebar em dispositivos móveis no redimensionamento
            closeMobileMenu();
        }
    });
}); 