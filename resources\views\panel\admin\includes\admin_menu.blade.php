<ul class="space-y-1">
    <!-- Dashboard -->
    <li class="sidebar-item {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
        <a href="{{ route('admin.dashboard') }}" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <i class="fas fa-home w-5 text-center sidebar-item-icon"></i>
            <span class="menu-text">Dashboard</span>
    </a>
    </li>


    <!-- Categorias -->
    <li class="sidebar-item {{ request()->routeIs('admin.categories*') ? 'active' : '' }}">
        <a href="{{ route('admin.categories.index') }}" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <i class="fas fa-tags w-5 text-center sidebar-item-icon"></i>
            <span class="menu-text">Categorias</span>
    </a>
    </li>


    <!-- Cursos -->
    <li class="sidebar-item has-submenu-parent {{ request()->routeIs('admin.courses*') ? 'active' : '' }}">
        <a href="#" class="has-submenu flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <div class="flex items-center gap-3">
                <i class="fas fa-book w-5 text-center sidebar-item-icon"></i>
                <span class="menu-text">Cursos</span>
            </div>
            <i class="fas fa-chevron-down menu-arrow text-xs"></i>
        </a>
        <div class="submenu hidden pl-12 mt-1 space-y-0.5">
            <a href="{{ route('admin.courses.index') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Todos os Cursos
            </a>
            <a href="{{ route('admin.courses.create') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Novo Curso
            </a>
        </div>
    </li>

    <!-- Módulos -->
    <li class="sidebar-item has-submenu-parent {{ request()->routeIs('admin.modulos*') ? 'active' : '' }}">
        <a href="#" class="has-submenu flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <div class="flex items-center gap-3">
                <i class="fas fa-layer-group w-5 text-center sidebar-item-icon"></i>
                <span class="menu-text">Módulos</span>
            </div>
            <i class="fas fa-chevron-down menu-arrow text-xs"></i>
        </a>
        <div class="submenu hidden pl-12 mt-1 space-y-0.5">
            <a href="{{ route('admin.modules.index') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Todos os Módulos
            </a>
            <a href="{{ route('admin.modules.create') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Criar Módulo
            </a>
        </div>
    </li>
 
    <!-- Alunos -->
    <li class="sidebar-item {{ request()->routeIs('admin.students*') ? 'active' : '' }}">
        <a href="{{ route('admin.students.index') }}" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <i class="fas fa-users w-5 text-center sidebar-item-icon"></i>
            <span class="menu-text">Alunos</span>
    </a>
    </li>

    <!-- Matrículas -->
    <li class="sidebar-item {{ request()->routeIs('admin.enrollments*') ? 'active' : '' }}">
        <a href="{{ route('admin.enrollments.index') }}" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <i class="fas fa-user-graduate w-5 text-center sidebar-item-icon"></i>
            <span class="menu-text">Matrículas</span>
    </a>
    </li>

    <!-- Testes -->
    <li class="sidebar-item has-submenu-parent {{ request()->routeIs('admin.tests*') ? 'active' : '' }}">
        <a href="#" class="has-submenu flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <div class="flex items-center gap-3">
                <i class="fas fa-clipboard-check w-5 text-center sidebar-item-icon"></i>
                <span class="menu-text">Testes</span>
            </div>
            <i class="fas fa-chevron-down menu-arrow text-xs"></i>
        </a>
        <div class="submenu hidden pl-12 mt-1 space-y-0.5">
            <a href="{{ route('admin.tests.index') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Todos os Testes
            </a>
            <a href="{{ route('admin.tests.create') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Novo Teste
            </a>
        </div>
    </li>

    <!-- Questões -->
    <li class="sidebar-item has-submenu-parent {{ request()->routeIs('admin.questions*') ? 'active' : '' }}">
        <a href="#" class="has-submenu flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <div class="flex items-center gap-3">
                <i class="fas fa-file-alt w-5 text-center sidebar-item-icon"></i>
                <span class="menu-text">Banco de Questões</span>
            </div>
            <i class="fas fa-chevron-down menu-arrow text-xs"></i>
        </a>
        <div class="submenu hidden pl-12 mt-1 space-y-0.5">
            <a href="{{ route('admin.questions.index') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Todas as Questões
            </a>
            <a href="{{ route('admin.questions.create') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Nova Questão
            </a>
            <a href="{{ route('admin.questions.import') }}" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Importar CSV
            </a>
        </div>
    </li>

    <!-- Gerenciador de Mídia -->
    <li class="sidebar-item {{ request()->routeIs('admin.media*') ? 'active' : '' }}">
        <a href="{{ route('admin.media.index') }}" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <i class="fas fa-photo-video w-5 text-center sidebar-item-icon"></i>
            <span class="menu-text">Gerenciador de Mídia</span>
        </a>
    </li>

    <!-- Relatórios -->
    <li class="sidebar-item has-submenu-parent {{ request()->routeIs('admin.relatorios*') ? 'active' : '' }}">
        <a href="#" class="has-submenu flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
            <div class="flex items-center gap-3">
                <i class="fas fa-chart-bar w-5 text-center sidebar-item-icon"></i>
                <span class="menu-text">Relatórios</span>
            </div>
            <i class="fas fa-chevron-down menu-arrow text-xs"></i>
        </a>
        <div class="submenu hidden pl-12 mt-1 space-y-0.5">
            <a href="#" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Desempenho Alunos
            </a>
            <a href="#" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Desempenho Cursos
            </a>
            <a href="#" class="block py-2 text-sm text-gray-300 hover:text-white hover:bg-[#2c2c2c] px-3 rounded-md menu-text">
                Financeiro
            </a>
        </div>
    </li>

    <!-- Teachers -->
    @auth
        @if(Auth::user()->role === 'super_admin')
            <li class="sidebar-item {{ request()->routeIs('admin.teachers*') ? 'active' : '' }}">
                <a href="{{ route('admin.teachers.index') }}" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
                    <i class="fas fa-chalkboard-teacher w-5 text-center sidebar-item-icon"></i>
                    <span class="menu-text">Teachers</span>
                </a>
            </li>
            <li class="sidebar-item {{ request()->routeIs('admin.users*') ? 'active' : '' }}">
                <a href="#" class="flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-md text-gray-300 hover:bg-[#2c2c2c] hover:text-white">
                    <i class="fas fa-user-shield w-5 text-center sidebar-item-icon"></i>
                    <span class="menu-text">Administradores</span>
                </a>
            </li>
        @endif
    @endauth
</ul>
  