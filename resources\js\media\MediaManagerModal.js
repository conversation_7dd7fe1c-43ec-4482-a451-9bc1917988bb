import { MediaManagerCore } from './MediaManagerCore.js';
import { MediaRenderer } from './MediaRenderer.js';
import { MediaAPI } from './MediaAPI.js';
import { SidebarManager } from './SidebarManager.js';
import { MediaManagerControls } from './MediaManagerControls.js';
import { notify } from '../notifications.js';

export class MediaManagerModal {
    constructor(options = {}) {
        this.options = {
            multiple: false,
            target: null, // Será detectado automaticamente
            onSelect: null,
            onClose: null,
            ...options
        };
        
        // Detectar target automaticamente se não especificado
        if (!this.options.target) {
            this.options.target = this.options.multiple ? 'gallery' : 'thumb';
        }
        
        this.core = new MediaManagerCore({
            target: this.options.target,
            selectable: true,
            onSelect: this.options.onSelect,
            onSelectionChange: (items) => this.updateSelectionControls()
        });
        
        this.modal = null;
        this.modalContainer = null;
        this.selectionControls = null;
        this.sidebarManager = null;
        this.controls = null;
        this.isDestroyed = false;
    }

    // ==================== MODAL ====================

    async open() {
        if (this.isDestroyed) return;
        
        await this.createModal();
        this.setupModal();
        await this.loadCategoryFilters();
        
        // Carregar mídias pré-selecionadas DEPOIS de carregar todas as mídias
        if (Array.isArray(this.options.preSelectedIds) && this.options.preSelectedIds.length > 0) {
            console.log('MediaManagerModal.open() - Pré-selecionadas encontradas:', this.options.preSelectedIds);
            
            // Primeiro carregar todas as mídias para popular o cache
            await this.loadMedia(1, {});
            
            console.log('MediaManagerModal.open() - Cache de mídias após loadMedia:', this.core.allMediaItems.size);
            
            // Agora adicionar as mídias pré-selecionadas ao selectedMedia
            let foundCount = 0;
            this.options.preSelectedIds.forEach(id => {
                const media = this.core.allMediaItems.get(id);
                if (media) {
                    this.core.selectedMedia.set(id, media);
                    foundCount++;
                    console.log('MediaManagerModal.open() - Mídia pré-selecionada encontrada:', id, media.filename);
                } else {
                    console.warn('MediaManagerModal.open() - Mídia pré-selecionada NÃO encontrada:', id);
                }
            });
            
            console.log('MediaManagerModal.open() - Mídias pré-selecionadas carregadas:', foundCount, 'de', this.options.preSelectedIds.length);
            
            // Atualizar a UI para mostrar as seleções
            this.updateUI();
        } else {
            // Se não há pré-selecionadas, apenas carregar normalmente
            await this.loadMedia(1, {});
        }
    }

    async createModal() {
        try {
            const modalHtml = await MediaAPI.getModalHtml();
            
            const modalElement = document.createElement('div');
            modalElement.innerHTML = modalHtml;
            
            document.body.appendChild(modalElement.firstElementChild);
            
            // Aguardar DOM ser atualizado
            await new Promise(resolve => setTimeout(resolve, 10));
            
            this.modal = document.getElementById('mediaModal');
            if (!this.modal) {
                throw new Error('Modal não encontrado no DOM');
            }
            
            this.modalContainer = this.modal.querySelector('#modalMediaGrid');
            if (!this.modalContainer) {
                throw new Error('Container do modal não encontrado');
            }
            
        } catch (error) {
            throw error;
        }
    }

    createFallbackModal() {
        const fallbackModal = document.createElement('div');
        fallbackModal.id = 'mediaModal';
        fallbackModal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center';
        
        fallbackModal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] w-full mx-4 flex flex-col">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Seletor de Mídia${this.options.multiple ? ' (Múltipla Seleção)' : ''}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="flex-1 overflow-hidden">
                    <div class="mb-4 p-3 border-b border-gray-200">
                        <label class="media-upload-btn px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors inline-flex items-center gap-2 cursor-pointer">
                            <i class="fas fa-upload"></i>
                            <span>Upload</span>
                            <input type="file" class="media-upload-input hidden" accept="image/*,application/pdf,audio/mpeg,video/mp4" multiple>
                        </label>
                    </div>
                    <div id="modalMediaGrid" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                        <!-- Mídias serão carregadas aqui -->
                    </div>
                </div>
                <div class="mt-4 flex justify-end gap-2">
                    <button class="cancel-btn px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                        Cancelar
                    </button>
                    <button class="confirm-selection-btn px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Selecionar
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(fallbackModal);
        
        this.modal = fallbackModal;
        this.modalContainer = fallbackModal.querySelector('#modalMediaGrid');
        
        this.setupModal();
        this.refresh();
    }

    setupModal() {
        if (!this.modal) return;

        // Botões do modal
        const confirmBtn = this.modal.querySelector('.confirm-selection-btn, .confirm-media-selection');
        const cancelBtn = this.modal.querySelector('.cancel-btn, .close-modal, .cancel-media-selection');
        
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.confirmSelection());
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.close());
        }
        
        // Controles de seleção (sempre criar, pois aparecem com 1+ selecionado)
        this.createSelectionControls();
        
        // Eventos
        this.bindModalEvents();
        
        // Inicializar sidebar manager para o modal
        this.initializeSidebar();
        
        // Inicializar controles de paginação e ordenação
        this.initializeControls();
        
        // Fechar com ESC
        const escapeHandler = (e) => {
            if (e.key === 'Escape' && this.modal) {
                this.close();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Fechar clicando fora
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal || e.target.classList.contains('media-modal-overlay')) {
                this.close();
            }
        });
    }

    bindModalEvents() {
        if (!this.modal) return;

        // Upload
        const uploadInput = this.modal.querySelector('.media-upload-input');
        if (uploadInput) {
            uploadInput.addEventListener('change', async (e) => {
                if (e.target.files && e.target.files.length > 0) {
                    await this.handleUpload(e.target.files);
                    e.target.value = '';
                }
            });
        }

        // Busca
        const searchInput = this.modal.querySelector('#mediaSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.resetOrphanedButton();
                    this.loadMedia(1, { search: e.target.value });
                }, 300);
            });
        }

        // Botão de limpeza de órfãs
        this.bindOrphanedButton();
    }

    bindOrphanedButton() {
        setTimeout(() => {
            const cleanOrphanedBtn = this.modal.querySelector('#cleanOrphanedBtn');
            
            if (cleanOrphanedBtn && !cleanOrphanedBtn.dataset.bound) {
                cleanOrphanedBtn.dataset.bound = 'true';
                cleanOrphanedBtn.dataset.state = 'search';
                
                cleanOrphanedBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    
                    const currentState = cleanOrphanedBtn.dataset.state || 'search';
                    
                    if (currentState === 'search') {
                        await this.searchOrphanedMedia();
                        cleanOrphanedBtn.dataset.state = 'clean';
                    } else {
                        await this.cleanOrphanedMedia();
                        cleanOrphanedBtn.dataset.state = 'search';
                        this.resetOrphanedButton();
                    }
                });
                
                this.resetOrphanedButton();
            }
        }, 100);
    }

    // ==================== CONTROLES DE SELEÇÃO ====================

    createSelectionControls() {
        const existingControls = this.modal.querySelector('.selection-controls');
        if (existingControls) {
            this.selectionControls = existingControls;
            this.bindSelectionControlsEvents();
            return;
        }

        const options = {
            isModal: true,
            target: this.options.target
        };

        this.selectionControls = MediaRenderer.renderSelectionControls(
            this.modalContainer, 
            this.core.getSelectedCount(),
            options
        );
        this.bindSelectionControlsEvents();
    }

    bindSelectionControlsEvents() {
        if (!this.selectionControls) return;

        const selectPageBtn = this.selectionControls.querySelector('.select-page-btn');
        const selectAllBtn = this.selectionControls.querySelector('.select-all-btn');
        const clearSelectionBtn = this.selectionControls.querySelector('.clear-selection-btn');
        const deleteSelectedBtn = this.selectionControls.querySelector('.delete-selected-btn');
        const confirmSelectionBtn = this.selectionControls.querySelector('.confirm-selection-btn');

        if (selectPageBtn) {
            selectPageBtn.replaceWith(selectPageBtn.cloneNode(true));
            this.selectionControls.querySelector('.select-page-btn').addEventListener('click', () => {
                this.toggleSelectAll();
            });
        }

        if (selectAllBtn) {
            selectAllBtn.replaceWith(selectAllBtn.cloneNode(true));
            this.selectionControls.querySelector('.select-all-btn').addEventListener('click', async () => {
                await this.selectAllItems();
            });
        }

        if (clearSelectionBtn) {
            clearSelectionBtn.replaceWith(clearSelectionBtn.cloneNode(true));
            this.selectionControls.querySelector('.clear-selection-btn').addEventListener('click', () => {
                this.core.clearSelection();
                this.updateUI();
            });
        }

        if (deleteSelectedBtn) {
            deleteSelectedBtn.replaceWith(deleteSelectedBtn.cloneNode(true));
            this.selectionControls.querySelector('.delete-selected-btn').addEventListener('click', () => {
                this.deleteSelected();
            });
        }

        if (confirmSelectionBtn) {
            confirmSelectionBtn.replaceWith(confirmSelectionBtn.cloneNode(true));
            this.selectionControls.querySelector('.confirm-selection-btn').addEventListener('click', () => {
                this.confirmSelection();
            });
        }
    }

    toggleSelectAll() {
        const currentPageItems = Array.from(this.modalContainer.querySelectorAll('.media-item')).map(item => ({
            id: parseInt(item.dataset.id),
            ...this.core.allMediaItems.get(parseInt(item.dataset.id))
        }));

        this.core.toggleSelectAll(currentPageItems);
        this.updateUI();
    }

    async selectAllItems() {
        try {
            await this.core.selectAllItems();
            this.updateUI();
        } catch (error) {
            console.error('Erro ao selecionar todos os itens:', error);
        }
    }

    updateSelectionControls() {
        if (this.selectionControls) {
            const pageItems = this.modalContainer.querySelectorAll('.media-item').length;
            const options = {
                totalItems: this.lastPagination?.total || pageItems,
                pageItems: pageItems,
                isModal: true,
                target: this.options.target
            };
            MediaRenderer.updateSelectionControls(this.selectionControls, this.core.getSelectedCount(), options);
        }
    }

    // ==================== CARREGAMENTO ====================

    async loadMedia(page = 1, filters = {}) {
        try {
            MediaRenderer.showLoading(this.modalContainer);
            const data = await this.core.loadMedia(page, filters);
            this.renderMedia(data.items, data.pagination);
        } catch (error) {
            MediaRenderer.showError(this.modalContainer);
        }
    }

    async loadCategoryFilters() {
        try {
            const categories = await this.core.loadMediaTypes();
            const sidebar = this.modal.querySelector('.w-64');
            if (sidebar) {
                MediaRenderer.renderCategoryFilters(sidebar, categories, (filters) => {
                    this.resetOrphanedButton();
                    this.loadMedia(1, filters);
                });
            }
        } catch (error) {
            console.error('Erro ao carregar filtros:', error);
        }
    }

    // ==================== RENDERIZAÇÃO ====================

    renderMedia(items, pagination) {
        // Armazenar informações de paginação
        this.lastPagination = pagination;
        
        const selectedIds = new Set(this.core.selectedMedia.keys());
        
        MediaRenderer.renderMediaGrid(this.modalContainer, items, selectedIds);
        
        // Adicionar eventos aos itens
        this.modalContainer.querySelectorAll('.media-item').forEach(item => {
            const mediaId = parseInt(item.dataset.id);
            const media = this.core.allMediaItems.get(mediaId);
            
            if (media) {
                // Evento de seleção
                item.addEventListener('click', (e) => {
                    if (e.target.closest('.media-actions')) return;
                    this.core.toggleItemSelection(mediaId, media);
                    this.updateUI();
                });

                // Eventos dos botões de ação
                item.querySelector('.view-btn')?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    MediaRenderer.openPreview(media, this.core.getFileIcon.bind(this.core));
                });

                item.querySelector('.delete-btn')?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.deleteMedia(media);
                });
            }
        });

        // Renderizar paginação
        if (pagination) {
            MediaRenderer.renderPagination(this.modalContainer, pagination, (page) => {
                this.loadMedia(page, this.core.currentFilters);
            });
        }

        this.updateUI();
    }

    updateUI() {
        // Atualizar seleção visual
        this.modalContainer.querySelectorAll('.media-item').forEach(item => {
            const mediaId = parseInt(item.dataset.id);
            const isSelected = this.core.isSelected(mediaId);
            
            if (isSelected) {
                item.classList.add('selected', 'border-blue-500', 'bg-blue-50');
            } else {
                item.classList.remove('selected', 'border-blue-500', 'bg-blue-50');
            }
        });

        // Atualizar controles de seleção
        this.updateSelectionControls();
    }

    // ==================== AÇÕES ====================

    async handleUpload(files) {
        const uploadBtn = this.modal.querySelector('.media-upload-btn');
        const uploadIcon = uploadBtn?.querySelector('i');
        const uploadSpan = uploadBtn?.querySelector('span');

        if (uploadBtn) {
            uploadBtn.disabled = true;
            if (uploadIcon) uploadIcon.className = 'fas fa-spinner fa-spin';
            if (uploadSpan) uploadSpan.textContent = 'Enviando...';
        }

        try {
            await this.core.uploadFiles(files);
            await this.refresh();
            await this.loadCategoryFilters();
        } catch (error) {
            console.error('Erro no upload:', error);
        } finally {
            if (uploadBtn) {
                uploadBtn.disabled = false;
                if (uploadIcon) uploadIcon.className = 'fas fa-upload';
                if (uploadSpan) uploadSpan.textContent = 'Upload';
            }
        }
    }

    async deleteSelected() {
        try {
            const success = await this.core.deleteSelected();
            if (success) {
                await this.refresh();
                await this.loadCategoryFilters();
            }
        } catch (error) {
            console.error('Erro ao excluir selecionados:', error);
        }
    }

    async deleteMedia(media) {
        try {
            const success = await this.core.deleteMedia(media);
            if (success) {
                await this.refresh();
                await this.loadCategoryFilters();
            }
        } catch (error) {
            console.error('Erro ao excluir mídia:', error);
        }
    }

    // ==================== MÍDIAS ÓRFÃS ====================

    async searchOrphanedMedia() {
        const cleanOrphanedBtn = this.modal.querySelector('#cleanOrphanedBtn');
        const btnTextElement = this.modal.querySelector('#cleanOrphanedBtnText');
        const btnIconElement = this.modal.querySelector('#cleanOrphanedBtnIcon');

        try {
            if (cleanOrphanedBtn) cleanOrphanedBtn.disabled = true;
            if (btnTextElement) btnTextElement.textContent = 'Buscando...';
            if (btnIconElement) btnIconElement.className = 'fas fa-spinner fa-spin mr-2';

            const data = await this.core.searchOrphanedMedia();
            this.renderMedia(data.items, data.pagination);

            const orphanedCount = data.pagination.total || 0;

            if (btnTextElement) {
                btnTextElement.textContent = orphanedCount > 0 
                    ? `Limpar Mídias Órfãs (${orphanedCount})`
                    : 'Nenhuma mídia órfã encontrada';
            }
            if (btnIconElement) {
                btnIconElement.className = orphanedCount > 0 
                    ? 'fas fa-broom mr-2'
                    : 'fas fa-check mr-2';
            }

            if (cleanOrphanedBtn) {
                cleanOrphanedBtn.disabled = orphanedCount === 0;
            }

        } catch (error) {
            this.resetOrphanedButton();
        }
    }

    async cleanOrphanedMedia() {
        const cleanOrphanedBtn = this.modal.querySelector('#cleanOrphanedBtn');
        const btnTextElement = this.modal.querySelector('#cleanOrphanedBtnText');
        const btnIconElement = this.modal.querySelector('#cleanOrphanedBtnIcon');

        try {
            if (cleanOrphanedBtn) cleanOrphanedBtn.disabled = true;
            if (btnTextElement) btnTextElement.textContent = 'Limpando...';
            if (btnIconElement) btnIconElement.className = 'fas fa-spinner fa-spin mr-2';

            const success = await this.core.cleanOrphanedMedia();
            if (success) {
                await this.refresh();
                await this.loadCategoryFilters();
            }
        } catch (error) {
            console.error('Erro ao limpar mídias órfãs:', error);
        }
    }

    resetOrphanedButton() {
        const cleanOrphanedBtn = this.modal.querySelector('#cleanOrphanedBtn');
        const btnTextElement = this.modal.querySelector('#cleanOrphanedBtnText');
        const btnIconElement = this.modal.querySelector('#cleanOrphanedBtnIcon');

        if (cleanOrphanedBtn) {
            cleanOrphanedBtn.disabled = false;
            cleanOrphanedBtn.dataset.state = 'search';
        }
        if (btnTextElement) btnTextElement.textContent = 'Buscar Mídias Órfãs';
        if (btnIconElement) btnIconElement.className = 'fas fa-search mr-2';

        // Limpar filtros se estivermos visualizando órfãs
        if (this.core.currentFilters.orphaned === 'true') {
            this.loadMedia(1, {});
        }
    }

    // ==================== SIDEBAR ====================
    
    initializeSidebar() {
        if (this.modal) {
            this.sidebarManager = new SidebarManager(this.modal);
        }
    }

    // ==================== CONTROLES ====================
    
    initializeControls() {
        this.controls = new MediaManagerControls(this.core);
    }

    // ==================== CONFIRMAÇÃO E FECHAMENTO ====================

    confirmSelection() {
        if (this.core.getSelectedCount() === 0) return;

        const selectedArray = this.core.getSelectedArray();
        const result = this.options.multiple ? selectedArray : selectedArray[0];

        if (this.options.onSelect) {
            this.options.onSelect(result);
        }

        this.close();
    }

    close() {
        if (this.modal) {
            // Limpar controles de seleção
            MediaRenderer.hideSelectionControls();
            
            // Limpar sidebar manager
            if (this.sidebarManager) {
                this.sidebarManager.destroy();
                this.sidebarManager = null;
            }
            
            // Limpar controles
            if (this.controls) {
                this.controls = null;
            }
            
            this.modal.remove();
            this.modal = null;
            this.modalContainer = null;
            this.selectionControls = null;
            
            if (this.options.onClose) {
                this.options.onClose();
            }
        }
    }

    // ==================== UTILITÁRIOS ====================

    async refresh() {
        await this.loadMedia(this.core.currentPage, this.core.currentFilters);
    }

    destroy() {
        this.isDestroyed = true;
        this.close();
        this.core.destroy();
    }
} 