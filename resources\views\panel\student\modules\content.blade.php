<x-layouts.student title="{{ $content->title }} - {{ $module->title }} - {{ $course->title }}">
    <!-- Breadcrumb -->
    <x-student.breadcrumb :course="$course" :module="$module" :content="$content" />

    <div class="min-h-screen">
        <div class="container mx-auto py-6 px-6">
            <!-- Main Content Grid -->
            <div class="grid grid-cols-12 gap-6">
                <!-- Left Sidebar - Content Navigation (4 columns) -->
                <div class="col-span-4">
                    <x-student.module.content-sidebar
                        :module="$module"
                        :course="$course"
                        :currentContent="$content"
                        :progress="$progress ?? collect()"
                        :progressPercentage="$progressPercentage ?? 0" />
                </div>

                <!-- Main Content Area (8 columns) -->
                <div class="col-span-8">
                    <!-- Content Header -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div>
                                        <h1 class="text-xl font-bold text-dark dark:text-gray-400 mb-3">
                                             @switch($content->content_type)
                                                @case('video')
                                                    <i class="fas fa-video text-dark dark:text-gray-400"></i>
                                                    @break
                                                @case('pdf')
                                                    <i class="fas fa-file-pdf text-dark dark:text-gray-400"></i>
                                                    @break
                                                @case('link')
                                                    <i class="fas fa-external-link-alt text-dark dark:text-gray-400"></i>
                                                    @break
                                                @case('word')
                                                    <i class="fas fa-file-word text-dark dark:text-gray-400"></i>
                                                    @break
                                                @default
                                                    <i class="fas fa-file-text text-dark dark:text-gray-400"></i>
                                            @endswitch

                                            {{ $content->title }}</h1>
                                        <span class="text-sm text-gray-500">Material {{ $currentIndex + 1 }} de {{ $totalContents }}</span>
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                                            {{ ucfirst($content->content_type) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <button id="completion-btn"
                                            onclick="toggleCompletion()"
                                            class="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 {{ $isCompleted ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700' }}">
                                        <i class="fas {{ $isCompleted ? 'fa-check' : 'fa-circle' }} mr-2"></i>
                                        <span id="completion-text">{{ $isCompleted ? 'Concluído' : 'Marcar como Concluído' }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Body -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                        @if($content->content_type === 'video')
                            <!-- Video Content -->
                            <div class="p-6">
                                <div class="aspect-video">
                                    <div class="w-full h-full [&>iframe]:w-full [&>iframe]:h-full [&>iframe]:rounded-lg">
                                        
                                        @if(str_contains($content->content, '<iframe'))
                                        
                                        <div class="video-wrapper">
                                            {!! $content->content !!}
                                            
                                            <div class="marca-dagua-central"><img src="https://quizz.sytes.net/images/Logo_Trends-QUIZ_branco-1024x361.png" width="200"/></div>
                                        </div>

                                        @else

                                            <iframe src="{{ $content->content }}"
                                                    frameborder="0"
                                                    allowfullscreen
                                                    class="w-full h-full rounded-lg">
                                            </iframe>

                                            
                                        @endif

                                        
                                    </div>
                                </div>
                            </div>
                        

                        @elseif($content->content_type === 'media')
                            <!-- Other Content Types -->
                            <div class="p-6">
                                <div class="prose max-w-none">
                                    {!! $content->description !!}
                                </div>
                            </div>

                        @else

                            <!-- Other Content Types -->
                            <div class="p-6">
                                @php
                                    // Detectar se o conteúdo é um PDF (por tipo ou por URL)
                                    $isPdf = $content->content_type === 'pdf' ||
                                             (is_string($content->content) && str_ends_with(strtolower($content->content), '.pdf'));
                                @endphp

                                @if($isPdf)
                                    <!-- Visualizador de PDF Nativo -->
                                    <div class="pdf-viewer-container">
                                        <!-- Header do PDF -->
                                        <div class="bg-gray-50 dark:bg-gray-800 rounded-t-lg p-4 border-b border-gray-200 dark:border-gray-700">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-file-pdf text-red-600 dark:text-red-400 text-lg"></i>
                                                </div>
                                                <div>
                                                    <h4 class="font-semibold text-gray-900 dark:text-white">{{ $content->title }}</h4>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400">Documento PDF • Visualização integrada</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Visualizador PDF -->
                                        <div class="pdf-iframe-container bg-white dark:bg-gray-800 rounded-b-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                                            <iframe src="{{ $content->content }}#toolbar=1&navpanes=0&scrollbar=1&page=1&view=FitH&zoom=page-width"
                                                    class="w-full border-0"
                                                    style="height: 85vh; min-height: 700px;"
                                                    title="Visualizador de PDF - {{ $content->title }}"
                                                    loading="lazy"
                                                    id="pdf-viewer">
                                                <!-- Fallback para navegadores que não suportam PDF -->
                                                <div class="p-8 text-center bg-gray-50 dark:bg-gray-800">
                                                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                                                        <i class="fas fa-file-pdf text-red-600 dark:text-red-400 text-2xl"></i>
                                                    </div>
                                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">PDF não pode ser exibido</h3>
                                                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                                                        Seu navegador não suporta a visualização de PDFs incorporados.<br>
                                                        O documento está disponível, mas precisa ser visualizado externamente.
                                                    </p>
                                                    <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                                        <p class="text-sm text-blue-800 dark:text-blue-200">
                                                            <i class="fas fa-info-circle mr-2"></i>
                                                            <strong>Dica:</strong> Atualize seu navegador ou use Chrome/Firefox para melhor compatibilidade.
                                                        </p>
                                                    </div>
                                                </div>
                                            </iframe>
                                        </div>

                                        <!-- Controles e informações -->
                                        <div class="mt-4 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                                            <div class="flex items-center justify-between text-sm">
                                                <div class="flex items-center gap-4 text-gray-600 dark:text-gray-400">
                                                    <span class="flex items-center gap-1">
                                                        <i class="fas fa-search text-xs"></i>
                                                        Use Ctrl+F para pesquisar
                                                    </span>
                                                    <span class="flex items-center gap-1">
                                                        <i class="fas fa-mouse text-xs"></i>
                                                        Scroll para navegar
                                                    </span>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <button onclick="togglePdfFullscreen()"
                                                            class="inline-flex items-center gap-1 px-3 py-1.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-md hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
                                                        <i class="fas fa-expand"></i>
                                                        Tela Cheia
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- JavaScript para funcionalidades do PDF -->
                                    <script>
                                        function togglePdfFullscreen() {
                                            const iframe = document.getElementById('pdf-viewer');
                                            const container = iframe.closest('.pdf-viewer-container');

                                            if (!document.fullscreenElement) {
                                                // Entrar em tela cheia
                                                if (container.requestFullscreen) {
                                                    container.requestFullscreen();
                                                } else if (container.webkitRequestFullscreen) {
                                                    container.webkitRequestFullscreen();
                                                } else if (container.msRequestFullscreen) {
                                                    container.msRequestFullscreen();
                                                }

                                                // Ajustar altura do iframe em tela cheia
                                                iframe.style.height = '100vh';
                                            } else {
                                                // Sair da tela cheia
                                                if (document.exitFullscreen) {
                                                    document.exitFullscreen();
                                                } else if (document.webkitExitFullscreen) {
                                                    document.webkitExitFullscreen();
                                                } else if (document.msExitFullscreen) {
                                                    document.msExitFullscreen();
                                                }

                                                // Restaurar altura normal
                                                iframe.style.height = '85vh';
                                            }
                                        }

                                        // Detectar mudanças de tela cheia
                                        document.addEventListener('fullscreenchange', function() {
                                            const iframe = document.getElementById('pdf-viewer');
                                            const button = document.querySelector('[onclick="togglePdfFullscreen()"]');

                                            if (document.fullscreenElement) {
                                                iframe.style.height = '100vh';
                                                if (button) {
                                                    button.innerHTML = '<i class="fas fa-compress"></i> Sair da Tela Cheia';
                                                }
                                            } else {
                                                iframe.style.height = '85vh';
                                                if (button) {
                                                    button.innerHTML = '<i class="fas fa-expand"></i> Tela Cheia';
                                                }
                                            }
                                        });

                                        // Melhorar carregamento do PDF
                                        document.addEventListener('DOMContentLoaded', function() {
                                            const iframe = document.getElementById('pdf-viewer');
                                            if (iframe) {
                                                iframe.addEventListener('load', function() {
                                                    console.log('PDF carregado com sucesso');
                                                });

                                                iframe.addEventListener('error', function() {
                                                    console.warn('Erro ao carregar PDF');
                                                });
                                            }
                                        });
                                    </script>
                                @else
                                    <!-- Conteúdo HTML padrão -->
                                    <div class="prose max-w-none dark:prose-invert">
                                        {!! $content->content !!}
                                    </div>
                                @endif
                            </div>

                        @endif
                    </div>

                    <!-- Navigation Footer -->
                    <div  class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <!-- Previous Button -->
                                <div>
                                    @if($previousContent)
                                        <a href="{{ route('student.module.content', [$course->slug, $module->slug, $previousContent->id]) }}"
                                           class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                                            <i class="fas fa-chevron-left mr-2"></i>
                                            Anterior
                                        </a>
                                    @endif
                                </div>

                                <!-- Progress Dots -->
                                <div class="flex items-center space-x-2">
                                    @foreach($module->contents->sortBy('order') as $index => $moduleContent)
                                        <div class="w-3 h-3 rounded-full {{ $moduleContent->id === $content->id ? 'bg-red-600' : ($progress->has($moduleContent->id) && $progress[$moduleContent->id]->status === 'completed' ? 'bg-green-500' : 'bg-gray-300') }}"></div>
                                    @endforeach
                                </div>

                                <!-- Next Button -->
                                <div>
                                    @if($nextContent)
                                        <a href="{{ route('student.module.content', [$course->slug, $module->slug, $nextContent->id]) }}"
                                           class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                            Próximo
                                            <i class="fas fa-chevron-right ml-2"></i>
                                        </a>
                                    @else
                                        <a href="{{ route('student.module.show', [$course->slug, $module->slug]) }}"
                                           class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                                            Finalizar Módulo
                                            <i class="fas fa-check ml-2"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript para funcionalidades interativas -->
    <script>
    let isCompleted = {{ $isCompleted ? 'true' : 'false' }};
    const contentId = {{ $content->id }};
    const courseSlug = '{{ $course->slug }}';
    const moduleSlug = '{{ $module->slug }}';

    function toggleCompletion() {
        const btn = document.getElementById('completion-btn');
        const text = document.getElementById('completion-text');

        // Desabilitar botão temporariamente
        btn.disabled = true;

        const url = isCompleted
            ? `{{ route('student.module.content.incomplete', [$course->slug, $module->slug, $content->id]) }}`
            : `{{ route('student.module.content.complete', [$course->slug, $module->slug, $content->id]) }}`;

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                isCompleted = data.completed;

                // Atualizar botão
                if (isCompleted) {
                    btn.className = 'px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 bg-green-600 hover:bg-green-700 text-white';
                    text.innerHTML = '<i class="fas fa-check mr-2"></i>Concluído';
                } else {
                    btn.className = 'px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 bg-gray-200 hover:bg-gray-300 text-gray-700';
                    text.innerHTML = '<i class="fas fa-circle mr-2"></i>Marcar como Concluído';
                }

                // Atualizar progresso na sidebar
                updateSidebarProgress(data);

                // Verificar se deve redirecionar para página de conclusão
                if (data.all_contents_completed && data.redirect_url) {
                    setTimeout(() => {
                        window.location.href = data.redirect_url;
                    }, 1500); // Aguardar 1.5s para mostrar o feedback visual
                }
            }
        })
        .catch(error => {
            console.error('Erro:', error);
        })
        .finally(() => {
            btn.disabled = false;
        });
    }

    function updateSidebarProgress(data) {
        // Atualizar contador
        const counter = document.getElementById('progress-counter');
        if (counter) {
            counter.textContent = `${data.completed_count}/${data.total_contents}`;
        }

        // Atualizar barra de progresso
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            progressBar.style.width = `${data.progress_percentage}%`;

            // Atualizar cor da barra dinamicamente
            const isAlmostComplete = data.total_contents > 0 && data.completed_count === (data.total_contents - 1);
            const isComplete = data.progress_percentage >= 100;

            // Remover classes de cor existentes
            progressBar.classList.remove('bg-gray-400', 'bg-blue-600', 'bg-green-600');

            // Aplicar nova cor
            if (isComplete) {
                progressBar.classList.add('bg-green-600');
            } else if (isAlmostComplete) {
                progressBar.classList.add('bg-blue-600');
            } else {
                progressBar.classList.add('bg-gray-400');
            }
        }

        // Atualizar item na sidebar
        const contentItem = document.querySelector(`[data-content-id="${contentId}"]`);
        if (contentItem) {
            const icon = contentItem.querySelector('i');
            const statusText = contentItem.querySelector('.text-xs');

            if (isCompleted) {
                icon.className = 'fas fa-check-circle text-green-600 text-lg mt-1';
                if (statusText && !statusText.textContent.includes('Concluído')) {
                    statusText.innerHTML += ' • <span class="text-green-600 font-medium">Concluído</span>';
                }
            } else {
                // Restaurar ícone original baseado no tipo
                const contentType = '{{ $content->content_type }}';
                let iconClass = 'fas ';
                switch(contentType) {
                    case 'video': iconClass += 'fa-video'; break;
                    case 'pdf': iconClass += 'fa-file-pdf'; break;
                    case 'link': iconClass += 'fa-external-link-alt'; break;
                    case 'word': iconClass += 'fa-file-word'; break;
                    default: iconClass += 'fa-file-text';
                }
                icon.className = iconClass + ' text-blue-600 text-lg mt-1';

                if (statusText) {
                    statusText.innerHTML = statusText.textContent.replace(' • Concluído', '');
                }
            }
        }

        // Atualizar dots de progresso
        updateProgressDots();
    }

    function updateProgressDots() {
        const dots = document.querySelectorAll('.w-3.h-3.rounded-full');
        // Recarregar a página para atualizar os dots corretamente
        // Em uma implementação mais avançada, poderíamos atualizar via AJAX
    }

    function navigateToContent(contentId) {
        // Buscar o elemento clicado para obter a URL correta
        const contentItem = document.querySelector(`[data-content-id="${contentId}"]`);
        if (contentItem && contentItem.dataset.href) {
            window.location.href = contentItem.dataset.href;
        } else {
            // Fallback: usar as variáveis do blade
            const courseSlug = '{{ $course->slug }}';
            const moduleSlug = '{{ $module->slug }}';
            window.location.href = `/student/${courseSlug}/${moduleSlug}/conteudo/${contentId}`;
        }
    }







    /*############################ SEGURANÇA ##########################*/

      // Desabilita atalho Ctrl+S, Ctrl+U, Ctrl+Shift+I, F12 etc.
        document.addEventListener('keydown', function(e) {
            if (
            (e.ctrlKey && ['s','u','c'].includes(e.key.toLowerCase())) ||
            e.key === 'F12' ||
            (e.ctrlKey && e.shiftKey && ['i','j'].includes(e.key.toLowerCase()))
            ) {
            e.preventDefault();
            return false;
            }
        });

        // Impede seleção de texto e clique com botão direito no documento inteiro
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('selectstart', e => e.preventDefault());

     /*#############################################################*/

    // Funcionalidades do PDF
    function toggleFullscreen() {
        const pdfContainer = document.querySelector('.pdf-iframe-container');
        const iframe = pdfContainer?.querySelector('iframe');
        const button = event.target.closest('button');
        const icon = button?.querySelector('i');

        if (!pdfContainer || !iframe || !button) return;

        if (!document.fullscreenElement) {
            // Entrar em tela cheia
            pdfContainer.requestFullscreen().then(() => {
                pdfContainer.style.height = '100vh';
                iframe.style.height = '100vh';
                icon.className = 'fas fa-compress';
                button.innerHTML = '<i class="fas fa-compress"></i> Sair da Tela Cheia';
            }).catch(err => {
                console.log('Erro ao entrar em tela cheia:', err);
            });
        } else {
            // Sair da tela cheia
            document.exitFullscreen().then(() => {
                pdfContainer.style.height = '';
                iframe.style.height = '80vh';
                icon.className = 'fas fa-expand';
                button.innerHTML = '<i class="fas fa-expand"></i> Tela Cheia';
            });
        }
    }

    // Listener para mudanças de fullscreen (ESC, etc.)
    document.addEventListener('fullscreenchange', function() {
        const pdfContainer = document.querySelector('.pdf-iframe-container');
        const iframe = pdfContainer?.querySelector('iframe');
        const button = document.querySelector('button[onclick="toggleFullscreen()"]');

        if (!document.fullscreenElement && pdfContainer && iframe && button) {
            // Saiu da tela cheia
            pdfContainer.style.height = '';
            iframe.style.height = '80vh';
            button.innerHTML = '<i class="fas fa-expand"></i> Tela Cheia';
        }
    });

    // Marcar conteúdo PDF como visualizado quando carregar
    document.addEventListener('DOMContentLoaded', function() {
        const iframe = document.querySelector('.pdf-iframe-container iframe');
        if (iframe) {
            iframe.addEventListener('load', function() {
                // Aguardar um pouco para garantir que o PDF foi carregado
                setTimeout(() => {
                    if (typeof markContentAsCompleted === 'function') {
                        markContentAsCompleted();
                    }
                }, 3000); // 3 segundos para dar tempo do PDF carregar
            });
        }
    });

    </script>

    <style>
        /* Garantir que iframes dentro de containers aspect-video se ajustem corretamente */
        .aspect-video iframe {
            width: 100% !important;
            height: 100% !important;
            border-radius: 0.5rem;
        }

        .marca-dagua {
        position: absolute;
        bottom: 40px;
        right: 10px;
        background-color: rgba(0,0,0,0.5);
        color: white;
        font-weight: bold;
        padding: 5px 10px;
        font-size: 14px;
        z-index: 2;
        pointer-events: none;
        }


        .marca-dagua-central {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0.6; /* transparente */
        pointer-events: none; /* não bloqueia clique */
        z-index: 10;
        }

        .video-wrapper {
        position: relative;
        width: 100%;
        //max-width: 800px;
        aspect-ratio: 16 / 9;
        }

        /* Estilos para o visualizador de PDF */
        .pdf-viewer-container {
            margin: 0;
        }

        .pdf-iframe-container {
            transition: all 0.3s ease;
        }

        .pdf-iframe-container:fullscreen {
            background: #1f2937;
            padding: 0;
        }

        .pdf-iframe-container:fullscreen iframe {
            border-radius: 0;
            height: 100vh !important;
        }

        /* Melhorar aparência do iframe PDF */
        .pdf-iframe-container iframe {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* Responsividade para dispositivos móveis */
        @media (max-width: 768px) {
            .pdf-iframe-container iframe {
                height: 60vh !important;
                min-height: 400px;
            }

            .pdf-viewer-container .flex {
                flex-direction: column;
                gap: 0.5rem;
            }

            .pdf-viewer-container .flex .flex {
                flex-direction: row;
                gap: 0.5rem;
            }
        }
    </style>
</x-layouts.student>
