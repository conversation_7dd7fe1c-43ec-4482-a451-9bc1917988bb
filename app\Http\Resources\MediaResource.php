<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class MediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'filename' => $this->filename,
            'original_filename' => $this->filename . '.' . $this->extension,
            'extension' => $this->extension,
            'mime_type' => $this->mime_type,
            'size' => $this->size,
            'size_human' => $this->formatBytes($this->size),
            'url' => $this->getOptimizedUrl(),
            'original_url' => $this->getOriginalUrl(),
            'exists' => $this->fileExists(),
            'created_at' => $this->created_at->format('d/m/Y H:i:s'),
            'created_at_diff' => $this->created_at->diffForHumans(),
            'is_image' => str_starts_with($this->mime_type, 'image/'),
            'thumbnail_url' => $this->getThumbnailUrl(),
            'download_url' => $this->getDownloadUrl(),
        ];
    }

    /**
     * Gera URL otimizada para exibição
     */
    private function getOptimizedUrl(int $width = 250, int $height = 250): string
    {
        $path = $this->getDiskPath();
        $baseUrl = route('media.serve', ['path' => $path]);
        
        if (str_starts_with($this->mime_type, 'image/')) {
            $params = [
                'w' => $width,
                'h' => $height,
                'fit' => 'crop',
                'fm' => 'webp'
            ];
            
            return $baseUrl . '?' . http_build_query($params);
        }
        
        return $baseUrl;
    }

    /**
     * URL original sem otimizações
     */
    private function getOriginalUrl(): string
    {
        return route('media.serve', ['path' => $this->getDiskPath()]);
    }

    /**
     * URL para thumbnail pequeno
     */
    private function getThumbnailUrl(): string
    {
        if (!str_starts_with($this->mime_type, 'image/')) {
            return $this->getFileTypeIcon();
        }

        $path = $this->getDiskPath();
        $baseUrl = route('media.serve', ['path' => $path]);
        
        $params = [
            'w' => 250,
            'h' => 250,
            'fit' => 'crop',
            'fm' => 'webp'
        ];
        
        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * URL para download
     */
    private function getDownloadUrl(): string
    {
        return route('media.serve', [
            'path' => $this->getDiskPath(),
            'download' => 1
        ]);
    }

    /**
     * Verifica se o arquivo existe fisicamente
     */
    private function fileExists(): bool
    {
        $path = $this->getDiskPath();
        $fullPath = storage_path('app/public/' . $path);
        return file_exists($fullPath);
    }

    /**
     * Formata bytes em formato legível
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Ícone para tipos de arquivo não-imagem
     */
    private function getFileTypeIcon(): string
    {
        $icons = [
            'application/pdf' => 'fas fa-file-pdf text-red-500',
            'application/msword' => 'fas fa-file-word text-blue-500',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'fas fa-file-word text-blue-500',
            'application/vnd.ms-excel' => 'fas fa-file-excel text-green-500',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'fas fa-file-excel text-green-500',
            'application/zip' => 'fas fa-file-archive text-yellow-500',
            'application/x-rar-compressed' => 'fas fa-file-archive text-yellow-500',
            'video/mp4' => 'fas fa-file-video text-purple-500',
            'video/avi' => 'fas fa-file-video text-purple-500',
            'audio/mp3' => 'fas fa-file-audio text-pink-500',
            'audio/wav' => 'fas fa-file-audio text-pink-500',
        ];

        return $icons[$this->mime_type] ?? 'fas fa-file text-gray-500';
    }
} 