<!-- <PERSON><PERSON> da direita - Preview da questão -->
<div class="lg:col-span-1">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg sticky">
        <div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
            <div class="flex items-center gap-2">
                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                <h2 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">Preview da Questão</h2>
            </div>
            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                Como aparecerá para o aluno
            </p>
        </div>

        <div class="p-6">
            <div class="space-y-4">
                <!-- Imagem da questão -->
                <div id="preview-question-image" class="{{ isset($question) && $question->hasMedia('question_image') ? '' : 'hidden' }}">
                    @if (isset($question) && $question->hasMedia('question_image'))
                        @php
                            $media = $question->firstMedia('question_image');
                            $imageUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=400&h=300&fit=crop&fm=webp';
                        @endphp
                        <img src="{{ $imageUrl }}" alt="Imagem da questão" class="w-full h-48 object-cover rounded-lg mb-4">
                    @endif
                </div>

                <!-- Texto da questão -->
                <div>
                    <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Questão</h4>
                    <div id="preview-question-text" class="text-sm text-zinc-600 dark:text-zinc-400">
                        {{ isset($question) ? $question->question : 'Digite a questão aqui...' }}
                    </div>
                </div>

                <!-- Tipo de questão -->
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-zinc-700 dark:text-zinc-300">Tipo:</span>
                    <span id="preview-question-type" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-question-circle mr-1"></i>
                        {{ isset($question) ? ucfirst($question->question_type) : 'Escolha Única' }}
                    </span>
                </div>

                <!-- Alternativas da questão -->
                <div id="preview-answers-section" class="{{ isset($question) && $question->question_type === 'essay' ? 'hidden' : '' }}">
                    <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Alternativas</h4>
                    <div id="preview-answers-container" class="space-y-3">
                        @if (isset($question) && $question->answers->count() > 0)
                            @foreach ($question->answers->sortBy('answer_number') as $index => $answer)
                                <div class="preview-answer-item">
                                    <label class="flex items-start gap-3 p-3 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-700 cursor-pointer transition-colors">
                                        <input type="{{ $question->question_type === 'multiple' ? 'checkbox' : 'radio' }}" 
                                               disabled 
                                               class="mt-1 text-trends-primary" 
                                               name="preview-answer"
                                               {{ $answer->correct ? 'checked' : '' }}>
                                        <div class="flex-1">
                                            <span class="preview-answer-text">
                                                @php
                                                    $format = $question->answer_format ?? 'alpha';
                                                    $marker = '';
                                                    switch($format) {
                                                        case 'numeric':
                                                            $marker = ($index + 1) . ') ';
                                                            break;
                                                        case 'alpha':
                                                            $marker = chr(65 + $index) . ') ';
                                                            break;
                                                        case 'roman':
                                                            $romans = ['i', 'ii', 'iii', 'iv', 'v', 'vi', 'vii', 'viii', 'ix', 'x'];
                                                            $marker = ($romans[$index] ?? ($index + 1)) . '. ';
                                                            break;
                                                        default:
                                                            $marker = '';
                                                    }
                                                @endphp
                                                {{ $marker }}{{ $answer->answer }}
                                            </span>
                                            @if ($answer->hasMedia('answer_image'))
                                                @php
                                                    $media = $answer->firstMedia('answer_image');
                                                    $imageUrl = route('media.serve', ['path' => $media->getDiskPath()]) . '?w=300&h=200&fit=crop&fm=webp';
                                                @endphp
                                                <img src="{{ $imageUrl }}"
                                                     alt="Imagem da alternativa"
                                                     class="mt-2 max-w-full rounded-md max-h-32">
                                                @if ($answer->caption)
                                                    <p class="text-xs text-gray-600 mt-1 italic">{{ $answer->caption }}</p>
                                                @endif
                                            @endif
                                        </div>
                                    </label>
                                </div>
                            @endforeach
                        @else
                            <p class="text-zinc-500">Adicione alternativas para ver o preview</p>
                        @endif
                    </div>
                </div>

                <!-- Campo de resposta dissertativa -->
                <div id="preview-essay-container" class="{{ isset($question) && $question->question_type === 'essay' ? '' : 'hidden' }}">
                    <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Resposta Dissertativa</h4>
                    <textarea disabled
                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-zinc-50 dark:bg-zinc-800 text-zinc-400 dark:text-zinc-500"
                        rows="4" placeholder="Espaço para resposta dissertativa do aluno..."></textarea>
                </div>

                <!-- Categorias -->
                <div>
                    <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Categorias</h4>
                    <div id="preview-categories" class="flex flex-wrap gap-2">
                        @if (isset($question) && $question->categories->count() > 0)
                            @foreach ($question->categories as $category)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-zinc-100 text-zinc-800 dark:bg-zinc-700 dark:text-zinc-200">
                                    {{ $category->title }}
                                </span>
                            @endforeach
                        @else
                            <span class="text-xs text-zinc-500 dark:text-zinc-400">Nenhuma categoria selecionada</span>
                        @endif
                    </div>
                </div>

                <!-- Status -->
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-zinc-700 dark:text-zinc-300">Status:</span>
                    <span id="preview-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ isset($question) && $question->free ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800' }}">
                        <i class="fas {{ isset($question) && $question->free ? 'fa-unlock' : 'fa-lock' }} mr-1"></i>
                        {{ isset($question) && $question->free ? 'Gratuita' : 'Paga' }}
                    </span>
                </div>

                <!-- Informações adicionais -->
                <div class="pt-4 border-t border-zinc-200 dark:border-zinc-700">
                    <div class="space-y-2">
                        <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                            <i class="fas fa-calendar text-xs"></i>
                            <span class="text-xs">Criada: {{ isset($question) ? $question->created_at->format('d/m/Y') : 'N/A' }}</span>
                        </div>
                        @if(isset($question) && $question->updated_at != $question->created_at)
                        <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                            <i class="fas fa-edit text-xs"></i>
                            <span class="text-xs">Atualizada: {{ $question->updated_at->format('d/m/Y') }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== ELEMENTOS DO PREVIEW =====
    const previewQuestionImage = document.getElementById('preview-question-image');
    const previewQuestionText = document.getElementById('preview-question-text');
    const previewQuestionType = document.getElementById('preview-question-type');
    const previewAnswersSection = document.getElementById('preview-answers-section');
    const previewAnswersContainer = document.getElementById('preview-answers-container');
    const previewEssayContainer = document.getElementById('preview-essay-container');
    const previewCategories = document.getElementById('preview-categories');
    const previewStatus = document.getElementById('preview-status');

    // ===== ELEMENTOS DO FORMULÁRIO =====
    const questionInput = document.getElementById('question');
    const questionImageInput = document.getElementById('question_image');
    const questionTypeSelect = document.getElementById('question_type');
    const answerFormatSelect = document.getElementById('answer_format');
    const categoriesSelect = document.getElementById('categories_select');
    const freeCheckbox = document.querySelector('input[name="free"]');

    // ===== EVENTOS DE PREVIEW EM TEMPO REAL =====
    
    // Preview do texto da questão em tempo real
    if (questionInput && previewQuestionText) {
        questionInput.addEventListener('input', function() {
            previewQuestionText.innerHTML = this.value || '<span class="text-zinc-400 italic">Digite a questão para visualizar o preview...</span>';
        });
    }

    // Preview da imagem da questão APENAS quando mídia é selecionada
    if (questionImageInput) {
        questionImageInput.addEventListener('mediaSelected', (e) => {
            const media = e.detail.media;
            updateQuestionImagePreview(media);
        });
    }

    // Preview do tipo de questão em tempo real
    if (questionTypeSelect && previewQuestionType) {
        questionTypeSelect.addEventListener('change', function() {
            const type = this.value;
            const typeLabels = {
                'single': 'Escolha Única',
                'multiple': 'Múltipla Escolha',
                'essay': 'Dissertativa'
            };
            
            previewQuestionType.innerHTML = `<i class="fas fa-question-circle mr-1"></i>${typeLabels[type] || 'Escolha Única'}`;
            
            // Mostrar/ocultar seções baseado no tipo
            if (type === 'essay') {
                previewAnswersSection.classList.add('hidden');
                previewEssayContainer.classList.remove('hidden');
            } else {
                previewAnswersSection.classList.remove('hidden');
                previewEssayContainer.classList.add('hidden');
            }
            
            // Atualizar preview das alternativas
            // Usar a função do questions.js se disponível
            if (window.updatePreview) {
                window.updatePreview();
            }
        });
    }

    // Preview do formato das alternativas em tempo real
    if (answerFormatSelect) {
        answerFormatSelect.addEventListener('change', function() {
            // Usar a função do questions.js se disponível
            if (window.updatePreview) {
                window.updatePreview();
            }
        });
    }

    // Preview do status em tempo real
    if (freeCheckbox && previewStatus) {
        freeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                previewStatus.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
                previewStatus.innerHTML = '<i class="fas fa-unlock mr-1"></i>Gratuita';
            } else {
                previewStatus.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800';
                previewStatus.innerHTML = '<i class="fas fa-lock mr-1"></i>Paga';
            }
        });
    }

    // ===== FUNÇÕES DE PREVIEW =====
    
    /**
     * Atualiza o preview da imagem da questão APENAS quando uma nova mídia é selecionada
     */
    function updateQuestionImagePreview(media) {
        if (!previewQuestionImage) return;

        if (media && media.url) {
            const img = previewQuestionImage.querySelector('img');
            if (img) {
                img.src = media.url;
                img.alt = media.filename || 'Imagem da questão';
            } else {
                previewQuestionImage.innerHTML = `<img src="${media.url}" alt="${media.filename || 'Imagem da questão'}" class="w-full h-48 object-cover rounded-lg mb-4">`;
            }
            previewQuestionImage.classList.remove('hidden');
        } else {
            previewQuestionImage.classList.add('hidden');
        }
    }



    // ===== INICIALIZAÇÃO DO PREVIEW =====
    
    // Disparar eventos iniciais para sincronizar o preview com os valores atuais
    // IMPORTANTE: NÃO disparar eventos para imagem, apenas para texto e outros campos
    if (questionInput) questionInput.dispatchEvent(new Event('input'));
    if (questionTypeSelect) questionTypeSelect.dispatchEvent(new Event('change'));
    if (freeCheckbox) freeCheckbox.dispatchEvent(new Event('change'));
    
    // Para alternativas, usar a função do questions.js se disponível
    if (window.updatePreview) {
        window.updatePreview();
    }
});
</script>
@endpush