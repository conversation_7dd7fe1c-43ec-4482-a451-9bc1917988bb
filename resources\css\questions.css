.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-accent);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--bg-input);
    color: var(--text-primary);
    transition: all 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-600);
    box-shadow: 0 0 0 2px var(--primary-200);
}

.form-textarea {
    min-height: 150px;
    resize: vertical;
}

/* Choices.js Customization - Estilos já incluídos no choices-custom.css */

.media-preview {
    margin-top: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.5rem;
    display: inline-block;
}

/* Estilos para botão de upload de mídia */
.media-upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    min-width: 36px;
    height: 36px;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.15s;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dark .media-upload-btn {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
}

.media-upload-btn:hover {
    background-color: #e5e7eb;
    transform: translateY(-1px);
}

.dark .media-upload-btn:hover {
    background-color: #374151;
}

.media-upload-btn:active {
    transform: translateY(0);
}

.media-upload-btn svg,
.media-upload-btn i {
    /* width: 1.25rem;
    height: 1.25rem; */
    margin-right: 0;
}

.media-upload-btn span + i,
.media-upload-btn span + svg {
    margin-left: 0.25rem;
    margin-right: 0;
}

.media-upload-btn i + span,
.media-upload-btn svg + span {
    margin-left: 0.25rem;
}

/* Botão de upload de questão específico */
button[data-target-input="question_image"] {
    padding: 0.5rem 1rem;
    height: auto;
    width: auto;
    font-weight: 500;
}

/* Botão para remover imagem */
.image-remove-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: rgba(239, 68, 68, 0.9);
    color: white;
    border-radius: 9999px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.image-remove-btn:hover {
    background-color: rgb(239, 68, 68);
    transform: scale(1.05);
}

.image-container {
    position: relative;
}

/* Estilos para alternativas */
.answer-item {
    padding-bottom: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.dark .answer-item {
    border-bottom-color: #374151;
}

.answer-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* Estilo para a borda colorida de explicação */
.border-trends-primary {
    border-color: rgb(var(--color-primary-500)); 
}

/* Preview estilo */
.preview-container {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    background-color: #fff;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .preview-container {
    background-color: #1f2937;
    border-color: #374151;
}

.question-option {
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.dark .question-option {
    border-color: #374151;
}

.question-option:hover {
    background-color: #f9fafb;
}

.dark .question-option:hover {
    background-color: #111827;
}

.question-option.selected {
    border-color: #2563eb;
    background-color: rgba(37, 99, 235, 0.05);
}

.dark .question-option.selected {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.question-option-image {
    max-width: 80px;
    max-height: 80px;
    border-radius: 0.25rem;
}

.sticky-preview {
    position: sticky;
    top: 0rem;
}

/* Estilos para os prefixos das alternativas no preview */
.question-option .text-trends-primary {
    display: inline-block;
    min-width: 25px;
    color: rgb(var(--color-primary-500));
}

/* Estilo para prefixo antes de imagem */
.question-option div:not(.flex-shrink-0) > .text-trends-primary + img {
    margin-top: 0.5rem;
    display: block;
}

/* Estilo para o indicador de formato */
#format-indicator {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px dashed rgba(107, 114, 128, 0.2);
}

.dark #format-indicator {
    border-bottom-color: rgba(107, 114, 128, 0.3);
}

#format-name {
    color: rgb(var(--color-primary-500));
}

/* Estilos para o gerenciador de mídia */
.media-item {
    transition: all 0.2s;
    border-color: #e2e8f0;
    border-width: 1px;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.media-item:hover {
    transform: translateY(-2px);
}

.media-item.border-trends-primary {
    border-width: 2px;
    border-color: rgb(var(--color-primary-500));
}

.media-item.ring-2 {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.media-item:hover .media-checkbox {
    visibility: visible !important;
}

.media-checkbox .fa-check {
    color: rgb(var(--color-primary-500));
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
}

.media-item > div:first-child {
    flex: 0 0 auto;
}

.media-item > div:last-child {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.media-details-btn, 
.media-delete-btn {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.media-details-btn:hover, 
.media-delete-btn:hover {
    transform: scale(1.1);
}

.media-item:hover .media-details-btn,
.media-item:hover .media-delete-btn {
    opacity: 1;
}

#multi-selection-controls {
    transition: all 0.3s;
}

/* ====================================================
   QUILL EDITOR CUSTOMIZATION
   ==================================================== */

/* Container do editor Quill */
.ql-container {
    font-size: 14px;
    min-height: 80px !important;
    max-height: 300px !important;
    overflow-y: auto;
}

/* Área de edição do Quill */
.ql-editor {
    min-height: 80px !important;
    max-height: 200px !important;
    padding: 12px 15px;
    line-height: 1.42;
    overflow-y: auto;
}

/* Toolbar do Quill */
.ql-toolbar {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    background: #f8f9fa;
}

/* Container principal do Quill */
.ql-container {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 6px 6px;
    background: white;
}

/* Tema escuro para o Quill */
.dark .ql-toolbar {
    background: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
}

.dark .ql-container {
    background: #1f2937;
    border-color: #4b5563;
    color: #f3f4f6;
}

.dark .ql-editor {
    color: #f3f4f6;
}

/* Evitar que o editor cresça demais */
.editor {
    max-height: 250px;
}

/* Placeholder do editor */
.ql-editor.ql-blank::before {
    color: #9ca3af;
    font-style: italic;
}

.dark .ql-editor.ql-blank::before {
    color: #6b7280;
}

/* ====================================================
   EXISTING CSS BELOW
   ==================================================== */ 