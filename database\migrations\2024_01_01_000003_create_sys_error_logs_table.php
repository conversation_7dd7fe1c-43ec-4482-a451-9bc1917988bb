<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_error_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->string('level'); // error, warning, info, debug
            $table->string('message');
            $table->json('context')->nullable(); // Dados adicionais do erro
            $table->string('file')->nullable(); // Arquivo onde ocorreu o erro
            $table->integer('line')->nullable(); // Linha do erro
            $table->text('stack_trace')->nullable(); // Stack trace completo
            $table->string('user_agent')->nullable(); // User agent do usuário
            $table->string('ip_address', 45)->nullable(); // IP do usuário
            $table->unsignedBigInteger('user_id')->nullable(); // Usuário logado (se houver)
            $table->string('url')->nullable(); // URL onde ocorreu o erro
            $table->string('method', 10)->nullable(); // GET, POST, etc.
            $table->json('request_data')->nullable(); // Dados da requisição
            $table->timestamp('occurred_at')->useCurrent(); // Quando ocorreu
            $table->timestamps();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');
            
            // Índices para performance
            $table->index(['company_id', 'level']); // Multi-tenancy + nível
            $table->index(['level', 'occurred_at']); // Nível + data
            $table->index(['user_id', 'occurred_at']); // Usuário + data
            $table->index(['ip_address']); // IP
            $table->index(['occurred_at']); // Data
            $table->index(['company_id', 'occurred_at']); // Multi-tenancy + data
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_error_logs');
    }
};
