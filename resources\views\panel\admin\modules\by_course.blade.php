@extends('layouts.panel')

@section('title', 'Módulos: ' . $course->title)
@section('page_title', 'Módulos: ' . $course->title)

@section('content')
<div class="container-fluid">
    <div class="d-flex align-items-center mb-4">
        <a href="{{ route('admin.modules.index') }}" class="btn btn-circle btn-sm btn-secondary mr-2">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="h3 mb-0 text-gray-800">{{ $course->title }}</h1>
    </div>
    
    <div class="mb-4">
        <h6 class="font-weight-bold text-secondary">Gerencie os módulos deste curso</h6>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">M<PERSON><PERSON>los</h6>
            <a href="{{ route('admin.modules.create', ['courseId' => $course->id]) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Adicionar Módulo
            </a>
        </div>
        <div class="card-body">
            @if($course->modules->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th>Título</th>
                            <th>Descrição</th>
                            <th>Conteúdos</th>
                            <th>Ordem</th>
                            <th width="15%">Ações</th>
                        </tr>
                    </thead>
                    <tbody id="modules-list">
                        @foreach($course->modules->sortBy('order') as $module)
                        <tr data-id="{{ $module->id }}">
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $module->title }}</td>
                            <td>{{ Str::limit($module->description, 80) }}</td>
                            <td>
                                <span class="badge badge-info">{{ $module->contents->count() }} itens</span>
                            </td>
                            <td>{{ $module->order }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('admin.modules.contents.index', ['moduleId' => $module->id]) }}" class="btn btn-sm btn-info" title="Conteúdos">
                                        <i class="fas fa-list"></i>
                                    </a>
                                    <a href="{{ route('admin.modules.edit', $module->id) }}" class="btn btn-sm btn-primary" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger delete-module" data-id="{{ $module->id }}" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <div class="mt-3">
                <button id="save-order" class="btn btn-success d-none">Salvar Ordem</button>
            </div>
            @else
            <div class="text-center py-5">
                <p class="text-muted mb-3">Nenhum módulo cadastrado para este curso.</p>
                <a href="{{ route('admin.modules.create', ['courseId' => $course->id]) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Adicionar o primeiro módulo
                </a>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Exclusão</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">Tem certeza que deseja excluir este módulo? Esta ação não pode ser desfeita.</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancelar</button>
                <form id="delete-form" action="" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Excluir</button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
    $(document).ready(function() {
        // Sortable para reordenar módulos
        var modulesList = document.getElementById('modules-list');
        if (modulesList) {
            var sortable = new Sortable(modulesList, {
                animation: 150,
                onEnd: function() {
                    $('#save-order').removeClass('d-none');
                }
            });
        }
        
        // Salvar ordem dos módulos
        $('#save-order').on('click', function() {
            var modules = [];
            $('#modules-list tr').each(function(index) {
                modules.push({
                    id: $(this).data('id'),
                    order: index + 1
                });
            });
            
            $.ajax({
                url: "{{ route('admin.modules.reorder') }}",
                method: 'POST',
                data: {
                    _token: "{{ csrf_token() }}",
                    modules: modules
                },
                success: function(response) {
                    $('#save-order').addClass('d-none');
                    alert('Ordem dos módulos atualizada com sucesso!');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    alert('Ocorreu um erro ao atualizar a ordem dos módulos.');
                }
            });
        });
        
        // Modal de confirmação de exclusão
        $('.delete-module').on('click', function() {
            var moduleId = $(this).data('id');
            var deleteUrl = "{{ route('admin.modules.delete', ':id') }}".replace(':id', moduleId);
            $('#delete-form').attr('action', deleteUrl);
            $('#deleteModal').modal('show');
        });
    });
</script>
@endpush
@endsection 