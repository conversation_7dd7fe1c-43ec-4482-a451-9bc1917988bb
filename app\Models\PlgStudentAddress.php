<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlgStudentAddress extends Model
{
    use SoftDeletes;

    protected $table = 'plg_students_address';

    protected $fillable = [
        'company_id',
        'student_id',
        'country',
        'cep',
        'street',
        'number',
        'neighborhood',
        'city',
        'state',
        'complement',
        'is_primary',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    public function student()
    {
        return $this->belongsTo(PlgStudent::class, 'student_id');
    }
}
