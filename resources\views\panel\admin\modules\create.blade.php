@extends('layouts.panel')

@section('title', 'Novo Módulo')
@section('page_title', 'Módulos')

@push('styles')
    @vite(['resources/css/quill-editor.css'])
@endpush

@section('content')
    <div class="animate-fade-in">
        <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-t-lg shadow-lg">
            @include('panel.admin.includes.page-header', [
                'title' => 'Novo Módulo',
                'description' => 'Preencha os dados abaixo para criar um novo módulo.',
                'actions' => [
                    [
                        'route' => route('admin.modules.index'),
                        'text' => 'Voltar',
                        'icon' => 'fas fa-arrow-left',
                        'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                        'hover_class' => 'bg-zinc-50 dark:bg-zinc-700',
                    ],
                ],
            ])

            @include('panel.admin.includes.alerts')

            <form action="{{ route('admin.modules.store') }}" method="POST" class="space-y-6">
                @csrf

                <div class="px-6">

                    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
                        <!-- Título -->
                        <div class="md:col-span-2">
                            <label for="title" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                Título do Módulo <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="title" id="title" value="{{ old('title') }}"
                                class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('title') border-red-500 @enderror"
                                placeholder="Digite o título do módulo" required>
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Curso -->
                        <div class="md:col-span-2">
                            <label for="course_id" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                Curso <span class="text-red-500">*</span>
                            </label>
                            <select name="course_id" id="course_id"
                                class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('course_id') border-red-500 @enderror"
                                required>
                                <option value="">Selecione um curso</option>
                                @foreach ($courses as $course)
                                    <option value="{{ $course->id }}"
                                        {{ old('course_id') == $course->id ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('course_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>


                        <!-- Preço -->
                        <div class="md:col-span-1">
                            <label for="price" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                Preço do Módulo <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="price" id="price" placeholder="R$ 0,00" value="{{ old('price') }}"
                                class="money w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('price') border-red-500 @enderror"
                                placeholder="Digite o título do módulo">
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Duração do Acesso -->
                        <div class="md:col-span-1">
                            <label for="duration_months" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                Duração do Acesso
                            </label>
                            <select name="duration_months" id="duration_months"
                                class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('duration_months') border-red-500 @enderror">
                                <option value="">Vitalício</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ old('duration_months') == $i ? 'selected' : '' }}>
                                        {{ $i }} {{ $i == 1 ? 'mês' : 'meses' }}
                                    </option>
                                @endfor
                            </select>
                            @error('duration_months')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                                Deixe vazio para acesso vitalício
                            </p>
                        </div>


                    </div>






                  <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">

                    <!-- Ordem -->
                    <div class="md:col-span-1 mb-0">
                        <label for="order" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Ordem
                        </label>
                        <input type="number" name="order" id="order" value="{{ old('order', 0) }}"
                            class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200 @error('order') border-red-500 @enderror"
                            min="0">
                        @error('order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

   
                    <!-- Grástis ou Não -->
                     <div class="md:col-span-2 mt-6">
                        <label class="flex items-center gap-2">
                            <input type="checkbox" name="is_free" value="1" class="sr-only peer" {{ old('is_free', isset($module) ? $module->is_free : false) ? 'checked' : '' }}>
                            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary"></div>
                            <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Módulo Grátis</span>
                        </label>
                    </div>

                    <!-- Status -->
                     <div class="md:col-span-2 mt-6">
                        <label class="flex items-center gap-2">
                            <input type="checkbox" name="active" value="1" class="sr-only peer" {{ old('active', true) ? 'checked' : '' }}>
                            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary"></div>
                            <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Módulo ativo</span>
                        </label>
                    </div>

                </div> 




                    <!-- Descrição -->
                    <div class="mb-6 mt-12">
                        <label for="description" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                            Descrição <span class="text-red-500">*</span>
                        </label>
                        <div id="description-editor" class="editor" data-input="description"
                            data-placeholder="Digite uma descrição para o módulo..."></div>
                        <input type="hidden" name="description" id="description"
                            value="{{ old('description') }}" required>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Imagem de Capa -->
               <!-- Imagem de Capa -->
                    <div class="media-selector">

                    <div class="flex justify-between items-center mb-2">
                   
                               <button type="button" 
                                class="open-media-modal w-full px-4 py-8 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg hover:border-trends-primary dark:hover:border-trends-primary transition-colors group"
                               data-mediable-type="App\Models\PlgModule"
                                        data-tag="thumbnail"
                                        data-input-id="module_thumbnail"
                                        data-preview-id="thumbnail-preview" 
                                        data-multiple="false">
                                <div class="text-center">
                                    <i class="fas fa-image text-2xl text-zinc-400 dark:text-zinc-600 group-hover:text-trends-primary transition-colors"></i>
                                    <p class="mt-2 text-sm text-zinc-500 dark:text-zinc-400 group-hover:text-trends-primary transition-colors">
                                        Clique para selecionar uma imagem para a capa do Módulo
                                    </p>
                                </div>
                            </button>

                         </div>
                            <input type="hidden" name="thumbnail" class="media-input" data-preview-id="thumbnail-preview" id="module_thumbnail" value="{{ old('thumbnail') }}">


                            <div class="media-preview hidden" id="thumbnail-preview">
                                <!-- Preview será inserido aqui pelo JavaScript -->
                            </div>
                            <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendad para capa: 1080x1350px</p>
                        </div>
                    

                    
                     


                    @error('active')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Botões -->
                @include('panel.admin.includes.page-footer', [
                    'cancelRoute' => route('admin.modules.index'),
                    'cancelText' => 'Cancelar',
                    'submitText' => 'Salvar Módulo'
                ])
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    @vite(['resources/js/quill-editor.js'])
@endpush