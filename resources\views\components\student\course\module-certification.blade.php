@props(['assessment', 'course', 'module'])

@if($assessment)
    @php
    // Verificar se o aluno já fez o teste
    $lastAttempt = auth('students')->user()->attempts()
        ->where('test_id', $assessment->id)
        ->where('status', 'completed')
        ->latest('finished_at')
        ->first();
    
    $isPassed = $lastAttempt && $lastAttempt->score_percentage >= $assessment->passing_score;
    @endphp

    <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
        <div class="flex items-start justify-between mb-4">
            <div class="flex items-center">
                <div class="flex-shrink-0 mr-3">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 dark:text-white text-lg">Teste Final do Módulo</h4>
                    <x-ui.badge variant="beginner" class="mt-1">Certificação</x-ui.badge>
                </div>
            </div>
            
            @if($isPassed)
                <x-ui.badge variant="completed">Aprovado</x-ui.badge>
            @endif
        </div>

        <h5 class="font-medium text-gray-900 dark:text-white mb-2">{{ $assessment->name }}</h5>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Teste seus conhecimentos sobre os fundamentos avançados do {{ $module->title }}.
        </p>

        <!-- Informações do Teste -->
        <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="text-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">Tempo</div>
                <div class="font-medium text-gray-900 dark:text-white">
                    {{ $assessment->time_limit_minutes ? $assessment->time_limit_minutes . ' min' : 'Ilimitado' }}
                </div>
            </div>
            <div class="text-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">Nota mínima</div>
                <div class="font-medium text-gray-900 dark:text-white">{{ $assessment->passing_score }}%</div>
            </div>
            <div class="text-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">Questões</div>
                <div class="font-medium text-gray-900 dark:text-white">
                    @if(isset($assessment->questions))
                        {{ $assessment->questions->count() }}
                    @else
                        N/A
                    @endif
                </div>
            </div>
        </div>

        @if($lastAttempt)
            <!-- Resultado da Última Tentativa -->
            <div class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Última tentativa:</span>
                    <span class="font-medium {{ $isPassed ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $lastAttempt->score_percentage }}%
                    </span>
                </div>
                @if($lastAttempt->finished_at)
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {{ $lastAttempt->finished_at->format('d/m/Y H:i') }}
                    </div>
                @endif
            </div>
        @endif

        <!-- Botão de Ação -->
        <div class="flex justify-center">
            @if($module->slug && $assessment->slug)
                @if($isPassed)
                    <!-- Teste já aprovado - opção de refazer -->
                    <a href="{{ route('student.test.start', [$course->slug, $module->slug, $assessment->slug]) }}"
                       class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refazer Teste
                    </a>
                @else
                    <!-- Primeira tentativa ou não aprovado -->
                    <a href="{{ route('student.test.start', [$course->slug, $module->slug, $assessment->slug]) }}"
                       class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                        </svg>
                        {{ $lastAttempt ? 'Tentar Novamente' : 'Iniciar Teste Final' }}
                    </a>
                @endif
            @else
                <button disabled 
                        class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-gray-400 text-white text-sm font-medium rounded-lg cursor-not-allowed">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    Em Configuração
                </button>
            @endif
        </div>
    </div>
@endif
