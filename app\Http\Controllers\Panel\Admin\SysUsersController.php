<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\SysUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;

class SysUsersController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:super_admin'])->only([
            'indexTeachers', 'createTeacher', 'storeTeacher', 'editTeacher', 
            'updateTeacher', 'destroyTeacher', 'showTeacher'
        ]);
    }

    public function index()
    {
        $users = SysUser::paginate(10);
        return view('panel.admin.users.index', compact('users'));
    }

    public function create()
    {
        return view('panel.admin.users.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:sys_users',
            'password' => 'required|min:6',
            'role' => 'required|in:super_admin,admin,teacher,student'
        ]);

        SysUser::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
            'company_id' => Auth::user()->company_id
        ]);

        return redirect()->route('admin.users.index')->with('success', 'User created successfully');
    }

    public function show(SysUser $user)
    {
        return view('panel.admin.users.show', compact('user'));
    }

    public function edit(SysUser $user)
    {
        return view('panel.admin.users.edit', compact('user'));
    }

    public function update(Request $request, SysUser $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:sys_users,email,'.$user->id,
            'role' => 'required|in:super_admin,admin,teacher,student'
        ]);

        $user->update($validated);

        return redirect()->route('admin.users.index')->with('success', 'User updated successfully');
    }

    public function destroy(SysUser $user)
    {
        $user->delete();
        return response()->json(['success' => true]);
    }

    // Listar teachers
    public function indexTeachers() {
        return view('panel.admin.teachers.index', [
            'columns' => $this->getColumns(),
            'id' => 'teachers-table',
            'ajaxUrl' => route('admin.teachers.data'),
            'filters' => [],
            'clearFiltersBtn' => true
        ]);
    }



    public function getDataTeachers()
    {
        $query = SysUser::select('sys_users.*')->where('role', 'teacher')->orderBy('id', 'desc');


        return DataTables::of($query)
            ->editColumn('status', fn($teacher) => view('panel.admin.teachers.partials.status', compact('teacher'))->render())
            ->editColumn('featured', fn($teacher) => view('panel.admin.teachers.partials.featured', compact('teacher'))->render())
            ->addColumn('actions', function($teacher) {
                return view('panel.admin.includes.table-actions', [
                    'actions' => [
                        [
                            'type' => 'edit',
                            'route' => route('admin.teachers.edit', $teacher->id),
                            'title' => 'Editar Cadastro'
                        ],
                        [
                            'type' => 'show',
                            'route' => route('admin.teachers.show', $teacher->id),
                            'title' => 'Visualizar Cadastro'
                        ],
                        [
                            'type' => 'delete',
                            'route' => route('admin.teachers.destroy', $teacher->id),
                            'title' => 'Excluir Cadastro',
                            'confirm_message' => 'Tem certeza que deseja excluir este Professor?'
                        ]
                    ]
                ])->render();
            })
            ->filterColumn('name', fn($query, $keyword) => $query->whereHas('name', fn($q) => $q->where('name', 'like', "%{$keyword}%")))
            ->filterColumn('email', fn($query, $keyword) => $query->whereHas('email', fn($q) => $q->where('email', 'like', "%{$keyword}%")))
            ->filterColumn('status', fn($query, $keyword) => $keyword ? $query->where('status', $keyword) : null)
            ->rawColumns(['actions', 'status', 'featured'])
            ->make(true);
    }



    // Formulário de cadastro
    public function createTeacher() {
        return view('panel.admin.teachers.create');
    }

    // Salvar novo teacher
    public function storeTeacher(Request $request) {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:sys_users',
            'password' => 'required|min:6',
        ]);
        
        SysUser::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => 'teacher',
            'company_id' => Auth::user()->company_id
        ]);
        
        return redirect()->route('admin.teachers.index')->with('success', 'Teacher created successfully');
    }

    // Visualizar teacher
    public function showTeacher(SysUser $teacher) {
        return view('panel.admin.teachers.show', compact('teacher'));
    }

    // Editar teacher
    public function editTeacher(SysUser $teacher) {
        return view('panel.admin.teachers.edit', compact('teacher'));
    }

    // Atualizar teacher
    public function updateTeacher(Request $request, SysUser $teacher) {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:sys_users,email,'.$teacher->id,
        ]);
        $teacher->update($validated);
        return redirect()->route('admin.teachers.index')->with('success', 'Teacher updated successfully');
    }

    // Deletar teacher
    public function destroyTeacher(SysUser $teacher) {
        $teacher->delete();
        return response()->json(['success' => true]);
    }




    private function getColumns()
    {
        return [
            ['data' => 'id', 'name' => 'sys_users.id', 'label' => 'ID'],
            ['data' => 'name', 'name' => 'sys_users.name', 'label' => 'Nome'],
            ['data' => 'email', 'name' => 'sys_users.email', 'label' => 'Email'],
            ['data' => 'status', 'name' => 'sys_users.active', 'label' => 'Status'],
            ['data' => 'actions', 'name' => 'actions', 'label' => 'Ações', 'orderable' => false, 'searchable' => false],
        ];
    }






} 