@props(['title' => 'Quiz - ' . config('app.name')])

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full bg-black">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ $title }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css'])
    
    <style>
        /* Layout minimalista para quiz */
        body {
            font-family: 'Figtree', sans-serif;
            /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; */
        }
        
        .quiz-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
        }
        
        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        /* .answer-option {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
         */
        .answer-option:hover {
            border-color: #3b82f6;
            background: #eff6ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2);
        }
        
        .answer-option.selected {
            border-color: #3b82f6;
            background: #dbeafe;
        }
        
        .answer-option.correct {
            border-color: #10b981;
            background: #d1fae5;
        }
        
        .answer-option.incorrect {
            border-color: #ef4444;
            background: #fee2e2;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        
        .btn-secondary {
            background: #6b7280;
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
        }
        
        /* Animações */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .quiz-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .question-card {
                border-radius: 10px;
            }
        }
        
        /* Prevenção de seleção de texto */
        .no-select {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Notificações */
        #quiz-notification {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
    
    @stack('styles')
</head>

<body class="antialiased bg-black h-full">
    <!-- Header minimalista -->
    <header class="bg-white/10 backdrop-blur-md border-b border-white/20 w-full fixed">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <h1 class="text-white font-bold text-xl">{{ config('app.name') }}</h1>
                    <span class="text-white/70 text-sm">Quiz</span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="text-white/90 text-sm">
                        <i class="fas fa-user mr-2"></i>
                        {{ Auth::guard('students')->user()->name }}
                    </div>
                    
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-white/70 hover:text-white text-sm transition-colors">
                            <i class="fas fa-sign-out-alt mr-1"></i>
                            Sair
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <!-- Conteúdo principal -->
    <main class="flex-1 py-8 h-full justify-center items-center">
        {{ $slot }}
    </main>

    <!-- Scripts -->
    @vite(['resources/js/app.js'])
    
    <script>
        // Prevenção de trapaça
        // document.addEventListener('contextmenu', function(e) {
        //     e.preventDefault();
        // });
               
        // Sistema otimizado de detecção de mudança de aba
        // let focusLost = false;

        // Função global para ser chamada quando o foco é perdido
        // window.onFocusLost = function() {
        //     // Esta função será sobrescrita pela página do quiz se necessário
        //     console.log('Mudança de aba detectada');
        // };

        // Detectar mudança de aba (método único e eficiente)
        // document.addEventListener('visibilitychange', function() {
        //     if (document.hidden && !focusLost) {
        //         focusLost = true;
        //         if (typeof window.onFocusLost === 'function') {
        //             window.onFocusLost('tab_change');
        //         }
        //     } else if (!document.hidden && focusLost) {
        //         focusLost = false;
        //         // Notificar que o foco voltou
        //         if (typeof window.onFocusRegained === 'function') {
        //             window.onFocusRegained();
        //         }
        //     }
        // });

        // Detectar tentativas de abrir DevTools (F12, Ctrl+Shift+I, etc.)
        document.addEventListener('keydown', function(e) {
            // Bloquear F12, Ctrl+Shift+I, Ctrl+U, etc.
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C')) {
                e.preventDefault();
            }

            // F12
            if (e.key === 'F12') {
                e.preventDefault();
                if (typeof window.onSecurityViolation === 'function') {
                    window.onSecurityViolation('devtools_attempt');
                }
                return false;
            }

            // Ctrl+Shift+I (DevTools)
            if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                e.preventDefault();
                if (typeof window.onSecurityViolation === 'function') {
                    window.onSecurityViolation('devtools_attempt');
                }
                return false;
            }

            // Ctrl+U (View Source)
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                if (typeof window.onSecurityViolation === 'function') {
                    window.onSecurityViolation('view_source_attempt');
                }
                return false;
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>