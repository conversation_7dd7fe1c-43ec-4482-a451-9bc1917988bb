{{-- Styles --}}
@push('styles')
    @vite(['resources/css/datatable-custom.css', 'resources/js/datatable-custom.js'])
@endpush

{{-- Filtros do DataTable --}}
<div class="datatable-filters">
    {{-- <PERSON>ai<PERSON> de busca --}}
    <div class="search-filter">
        <input type="search" id="dt-search-{{ $id }}" placeholder="Buscar registros" class="w-full">
    </div>

    {{-- Filtros adicionais --}}
    @if (isset($filters) && count($filters) > 0)
        @foreach ($filters as $filter)
            <div class="filter-group">
                <select class="dt-filter" data-column="{{ $filter['column'] }}">
                    <option value="">{{ $filter['label'] }}</option>
                    @foreach ($filter['options'] as $option)
                        <option value="{{ $option['value'] }}">{{ $option['label'] }}</option>
                    @endforeach
                </select>
            </div>
        @endforeach

        {{-- Botão para limpar filtros - sempre visível, mas inicialmente inativo --}}
        @if ($clearFiltersBtn ?? false)
            <div class="filter-group">
                <button type="button" class="dt-clear-filters">
                    <i class="fas fa-times"></i>
                    Limpar Filtros
                </button>
            </div>
        @endif
    @endif
</div>

{{-- Tabela --}}
<table id="{{ $id }}"
    class="display nowrap bg-white dark:bg-zinc-900 w-full table-auto border-t border-b border-zinc-200 dark:border-zinc-6800"
    style="width: 100%;">
    <thead>
        <tr>
            @foreach ($columns as $column)
                <th>{{ $column['label'] }}</th>
            @endforeach
        </tr>
    </thead>
</table>

@push('scripts')
    <script>
        // Aguardar jQuery estar disponível
        function initDataTable() {
            if (typeof window.$ === 'undefined') {
                setTimeout(initDataTable, 100);
                return;
            }

            window.$(function() {

            // Descobre o índice da coluna "image_url" e "thumbnail_url"
            const imageColIndex = @json($columns).findIndex(col => col.data === 'image_url');
            const thumbnailColIndex = @json($columns).findIndex(col => col.data === 'thumbnail_url');

            // Inicializa o DataTable
            const table = $('#{{ $id }}').DataTable({
                processing: true,
                serverSide: true,
                responsive: true,
                ajax: '{{ $ajaxUrl }}',
                columns: @json($columns),
                language: {
                    url: '/js/datatables/i18n/pt-BR.json'
                },
                dom: 't<"dt-footer"<"dt-info"i><"dt-pagination"p>>',
                pagingType: 'simple_numbers',
                order: [
                    [0, 'desc']
                ],
                oLanguage: {
                    oPaginate: {
                        sNext: '<i class="fas fa-chevron-right"></i>',
                        sPrevious: '<i class="fas fa-chevron-left"></i>'
                    },
                    sInfo: "Mostrando de _START_ até _END_ de _TOTAL_ registros",
                    sInfoEmpty: "Mostrando 0 até 0 de 0 registros",
                    sInfoFiltered: ""
                },
                columnDefs: [
                    // Configurações básicas de responsividade que funcionam para qualquer tabela
                    {
                        responsivePriority: 1,
                        targets: 0
                    }, // Primeira coluna (geralmente ID)
                    {
                        responsivePriority: 2,
                        targets: 1, // Título
                        render: function(data, type, row) {
                            if (type === 'display' && typeof data === 'object' && data !== null) {
                                const thumb = data.thumbnail
                                    ? `<img src="${data.thumbnail}" class="w-10 h-10 rounded-full object-cover cursor-pointer hover:opacity-80 transition-opacity" data-tooltip="${data.text}" data-tooltip-image="${data.thumbnail}" />`
                                    : `<div class="w-10 h-10 bg-trends-primary/10 rounded-full flex items-center justify-center"><i class="fas fa-image text-red-400"></i></div>`;

                                return `
                                    <div class="flex items-center gap-3">
                                        <div class="flex-shrink-0">${thumb}</div>
                                        <div class="flex-grow font-medium text-zinc-900 dark:text-zinc-100">${data.text ?? '-'}</div>
                                    </div>`;
                            }

                            return typeof data === 'string' ? data : '-';
                        }
                    },
                    {
                        responsivePriority: 3,
                        targets: -1
                    }, // Última coluna (geralmente ações)

                    // Renderização customizada para a coluna de imagem
                    ...(imageColIndex !== -1 ? [{
                        targets: imageColIndex,
                        render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return `<img src="${data}" style="object-fit: cover; border-radius: 6px;" />`;
                            }
                            return '';
                        },
                        orderable: false,
                        searchable: false
                    }] : []),

                    // Renderização customizada para a coluna de thumbnail
                    ...(thumbnailColIndex !== -1 ? [{
                        targets: thumbnailColIndex,
                        render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return `<img src="${data}" style="object-fit: cover; border-radius: 6px;" />`;
                            }
                            return '';
                        },
                        orderable: false,
                        searchable: false
                    }] : []),

                    // Tratamento genérico para textos em qualquer coluna
                    {
                        targets: '_all',
                        render: function(data, type, row, meta) {

                            if (meta.col === imageColIndex || meta.col === thumbnailColIndex) return data;

                            if (type === 'display' && data && typeof data === 'string') {
                                // Remove HTML e caracteres problemáticos
                                let cleanText = $('<div>').html(data).text();
                                cleanText = cleanText.replace(/["']\s*>/g, '');

                                // Aplica truncamento apenas se não for um elemento HTML já renderizado
                                if (!data.includes('<') && !data.includes('>')) {
                                    return '<span class="truncate" title="' + cleanText + '">' +
                                        cleanText + '</span>';
                                }
                            }
                            return data;
                        }
                    }
                ],
                drawCallback: function() {
                    // Verifica se há filtros ativos e atualiza a classe do botão
                    updateClearFiltersButton();
                }
            });

            // Função para atualizar o estado do botão de limpar filtros
            function updateClearFiltersButton() {
                const hasActiveFilters = $('.dt-filter').toArray().some(el => el.value !== '') ||
                    $('#dt-search-{{ $id }}').val().trim() !== '';

                // Atualiza a classe do botão conforme os filtros
                if (hasActiveFilters) {
                    $('.dt-clear-filters').addClass('active');
                } else {
                    $('.dt-clear-filters').removeClass('active');
                }
            }

            // Função debounce utilitária
            function debounce(func, wait) {
                let timeout;
                return function(...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(this, args), wait);
                };
            }

            // Busca com debounce
            $('#dt-search-{{ $id }}').off('keyup').on('input', debounce(function() {
                table.search(this.value).draw();
                updateClearFiltersButton();
            }, 400));

            // Setup dos filtros
            $(document).on('change', '.dt-filter', function() {
                const column = $(this).data('column');
                const value = this.value;

                // Busca a coluna correta baseada no nome
                const columnIndex = table.columns().indexes().toArray().find(index => {
                    const columnDef = table.settings()[0].aoColumns[index];
                    return columnDef.name === column || columnDef.data === column;
                });

                if (columnIndex !== undefined) {
                    table.column(columnIndex).search(value).draw();
                }

                // Atualiza o estado do botão de limpar filtros
                updateClearFiltersButton();
            });

            // Botão para limpar filtros
            $(document).on('click', '.dt-clear-filters', function() {
                // Só executa a ação se tiver filtros aplicados (classe active)
                if ($(this).hasClass('active')) {
                    $('.dt-filter').val('');
                    $('#dt-search-{{ $id }}').val('');
                    table.search('').columns().search('').draw();
                    updateClearFiltersButton();
                }
            });

            // Função global para mostrar tooltip de imagem (simples)
            window.showImageTooltip = function(element, event = null) {
                const tooltip = document.getElementById('image-tooltip');
                const tooltipImg = tooltip.querySelector('img');
                const tooltipTitle = tooltip.querySelector('.tooltip-title');
                
                const imageUrl = element.getAttribute('data-tooltip-image');
                const title = element.getAttribute('data-tooltip');
                
                if (imageUrl && title) {
                    tooltipImg.src = imageUrl;
                    tooltipTitle.textContent = title;
                    tooltip.style.display = 'block';
                    tooltip.style.opacity = '1';

                    // Posição simplificada: centralizado abaixo da thumb
                    const rect = element.getBoundingClientRect();
                    const scrollY = window.scrollY || window.pageYOffset;
                    const scrollX = window.scrollX || window.pageXOffset;
                    const tooltipRect = tooltip.getBoundingClientRect();

                    let left = rect.left + scrollX + (rect.width / 2) - (tooltipRect.width / 2);
                    let top = rect.bottom + scrollY + 6; // 6px abaixo da thumb

                    // Ajustar se sair da tela
                    if (left < 10) left = 10;
                    if (left + tooltipRect.width > window.innerWidth - 10) {
                        left = window.innerWidth - tooltipRect.width - 10;
                    }
                    if (top + tooltipRect.height > window.innerHeight + scrollY - 10) {
                        top = rect.top + scrollY - tooltipRect.height - 6;
                    }

                    tooltip.style.left = left + 'px';
                    tooltip.style.top = top + 'px';
                }
            };

            // Função global para esconder tooltip de imagem
            window.hideImageTooltip = function() {
                const tooltip = document.getElementById('image-tooltip');
                if (tooltip) {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        tooltip.style.display = 'none';
                    }, 200);
                }
            };

            // Esconder tooltip quando clicar fora
            document.addEventListener('click', function(e) {
                const tooltip = document.getElementById('image-tooltip');
                if (tooltip && !tooltip.contains(e.target) && !e.target.hasAttribute('data-tooltip-image')) {
                    hideImageTooltip();
                }
            });

            // Esconder tooltip ao sair da área da thumb
            $(document).on('mouseleave', '.flex-shrink-0', function(e) {
                hideImageTooltip();
            });

            // Exibir tooltip ao passar mouse na área da thumb
            $(document).on('mouseenter', '.flex-shrink-0', function(e) {
                const img = $(this).find('img[data-tooltip-image]')[0];
                if (img) {
                    showImageTooltip(img, e);
                }
            });

            // Exibir tooltip ao clicar/tocar na thumb (mobile/tablet)
            $(document).on('click touchstart', 'img[data-tooltip-image]', function(e) {
                e.stopPropagation();
                showImageTooltip(this, e.originalEvent && e.originalEvent.touches ? e.originalEvent.touches[0] : e);
            });
            });
        }

        // Inicializar DataTable
        initDataTable();
    </script>
@endpush

<!-- Tooltip de imagem -->
<div id="image-tooltip" class="image-tooltip">
    <img src="" alt="Tooltip Image">
    <div class="tooltip-title"></div>
</div>