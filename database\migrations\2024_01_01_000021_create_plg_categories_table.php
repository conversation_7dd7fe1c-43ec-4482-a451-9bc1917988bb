<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id')->nullable(); // Quem criou a categoria
            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#3B82F6'); // Cor em hexadecimal
            $table->string('icon')->nullable(); // Ícone da categoria
            $table->boolean('active')->default(true);
            $table->integer('order')->default(0); // Ordem de exibição
            $table->timestamps();
            $table->softDeletes();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');
            
            // Índices para performance
            $table->index('slug');
            $table->index(['company_id', 'active']); // Multi-tenancy + ativo
            $table->index(['active', 'order']); // Ativo + ordem
            $table->index(['company_id', 'order']); // Multi-tenancy + ordem
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_categories');
    }
};
