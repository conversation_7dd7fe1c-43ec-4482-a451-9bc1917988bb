@props(['progress' => 0])

<div {{ $attributes->merge(['class' => 'w-12 h-12 rounded-full bg-white/10 flex items-center justify-center text-xs relative']) }}>
    <svg class="w-full h-full absolute top-0 left-0 -rotate-90" viewBox="0 0 36 36">
        <circle 
            stroke-dasharray="100" 
            stroke-dashoffset="{{ 100 - $progress }}" 
            stroke-linecap="round" 
            stroke-width="3" 
            stroke="currentColor" 
            fill="transparent" 
            r="16" 
            cx="18" 
            cy="18" 
            class="text-netflix"
        />
    </svg>
    <span class="text-white font-medium z-10">{{ $progress }}%</span>
</div> 