<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PlgTest;
use Illuminate\Support\Str;

class UpdateTestSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tests:update-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Atualiza os slugs dos testes existentes e ativa testes inativos';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Verificando e corrigindo testes...');

        // Listar todos os testes
        $allTests = PlgTest::with('module')->get();

        $this->info("📊 Total de testes encontrados: {$allTests->count()}");

        foreach ($allTests as $test) {
            $this->info("🔍 Teste ID {$test->id}: {$test->title}");
            $this->info("   - Módulo: {$test->module->title}");
            $this->info("   - Ativo: " . ($test->active ? 'SIM' : 'NÃO'));
            $this->info("   - Slug: " . ($test->slug ?: 'VAZIO'));

            $needsUpdate = false;
            $updates = [];

            // Verificar slug
            if (empty($test->slug)) {
                $updates['slug'] = Str::slug($test->title);
                $needsUpdate = true;
                $this->info("   ⚠️  Slug vazio - será atualizado para: {$updates['slug']}");
            }

            // Verificar se está ativo
            if (!$test->active) {
                $updates['active'] = true;
                $needsUpdate = true;
                $this->info("   ⚠️  Teste inativo - será ativado");
            }

            if ($needsUpdate) {
                $test->update($updates);
                $this->info("   ✅ Teste atualizado!");
            } else {
                $this->info("   ✅ Teste OK!");
            }

            $this->info("");
        }

        $this->info('🎉 Verificação concluída!');

        return 0;
    }
}
