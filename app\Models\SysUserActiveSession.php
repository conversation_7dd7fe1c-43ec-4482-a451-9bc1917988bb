<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SysUserActiveSession extends Model
{
    use HasFactory;

    protected $table = 'sys_user_active_sessions';

    protected $fillable = [
        'company_id',
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'device_type',
        'browser',
        'platform',
        'location',
        'login_at',
        'last_activity',
        'is_active',
        'logout_at',
        'logout_reason',
    ];

    protected $casts = [
        'login_at' => 'datetime',
        'last_activity' => 'datetime',
        'logout_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Relacionamento com a empresa
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(SysCompany::class, 'company_id');
    }

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(SysUser::class, 'user_id');
    }

    /**
     * Verificar se a sessão está ativa
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Marcar sessão como inativa
     */
    public function deactivate(string $reason = 'manual'): bool
    {
        return $this->update([
            'is_active' => false,
            'logout_at' => now(),
            'logout_reason' => $reason,
        ]);
    }

    /**
     * Atualizar última atividade
     */
    public function updateActivity(): bool
    {
        return $this->update([
            'last_activity' => now(),
        ]);
    }

    /**
     * Verificar se a sessão expirou
     */
    public function isExpired(int $timeoutMinutes = 120): bool
    {
        return $this->last_activity->diffInMinutes(now()) > $timeoutMinutes;
    }

    /**
     * Obter informações do dispositivo formatadas
     */
    public function getDeviceInfoAttribute(): string
    {
        $parts = array_filter([
            $this->browser,
            $this->platform,
            $this->device_type,
        ]);

        return implode(' / ', $parts) ?: 'Dispositivo desconhecido';
    }

    /**
     * Scope para sessões ativas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para sessões por usuário
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope para sessões por empresa
     */
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope para sessões expiradas
     */
    public function scopeExpired($query, int $timeoutMinutes = 120)
    {
        return $query->where('last_activity', '<', now()->subMinutes($timeoutMinutes))
                    ->where('is_active', true);
    }
}
