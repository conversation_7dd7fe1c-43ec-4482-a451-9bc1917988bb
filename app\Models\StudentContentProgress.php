<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentContentProgress extends Model
{
    use HasFactory;

    protected $table = 'plg_students_content_progress';

    protected $fillable = [
        'company_id',
        'student_id',
        'content_id',
        'status',
        'progress_percentage',
        'time_spent',
        'started_at',
        'completed_at',
        'last_accessed_at',
        'progress_data',
    ];

    protected $casts = [
        'progress_percentage' => 'integer',
        'time_spent' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'progress_data' => 'array',
    ];

    /**
     * Relacionamento com o estudante.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(PlgStudent::class, 'student_id');
    }

    /**
     * Relacionamento com o conteúdo.
     */
    public function content(): BelongsTo
    {
        return $this->belongsTo(PlgModuleContent::class, 'content_id');
    }



    /**
     * Marcar conteúdo como concluído.
     */
    public static function markAsCompleted($studentId, $contentId, $timeSpent = 0)
    {
        return static::updateOrCreate(
            [
                'student_id' => $studentId,
                'content_id' => $contentId,
            ],
            [
                'company_id' => 1,
                'status' => 'completed',
                'progress_percentage' => 100,
                'time_spent' => $timeSpent,
                'completed_at' => now(),
                'last_accessed_at' => now(),
            ]
        );
    }

    /**
     * Marcar conteúdo como não concluído.
     */
    public static function markAsIncomplete($studentId, $contentId)
    {
        return static::updateOrCreate(
            [
                'student_id' => $studentId,
                'content_id' => $contentId,
            ],
            [
                'company_id' => 1,
                'status' => 'in_progress',
                'progress_percentage' => 0,
                'completed_at' => null,
                'last_accessed_at' => now(),
            ]
        );
    }

    /**
     * Verificar se um conteúdo está concluído.
     */
    public static function isCompleted($studentId, $contentId): bool
    {
        return static::where('student_id', $studentId)
            ->where('content_id', $contentId)
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Obter progresso do módulo para um estudante.
     */
    public static function getModuleProgress($studentId, $moduleId)
    {
        // Buscar conteúdos do módulo e depois o progresso
        $contentIds = \App\Models\PlgModuleContent::where('module_id', $moduleId)->pluck('id');

        return static::where('student_id', $studentId)
            ->whereIn('content_id', $contentIds)
            ->get()
            ->keyBy('content_id');
    }

    /**
     * Calcular percentual de progresso do módulo.
     */
    public static function calculateModuleProgress($studentId, $moduleId, $totalContents = null)
    {
        // Buscar conteúdos do módulo
        $contentIds = \App\Models\PlgModuleContent::where('module_id', $moduleId)->pluck('id');

        if ($totalContents === null) {
            $totalContents = $contentIds->count();
        }

        $completedCount = static::where('student_id', $studentId)
            ->whereIn('content_id', $contentIds)
            ->where('status', 'completed')
            ->count();

        return $totalContents > 0 ? round(($completedCount / $totalContents) * 100) : 0;
    }
}
