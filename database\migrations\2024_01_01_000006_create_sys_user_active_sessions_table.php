<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_user_active_sessions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id');
            $table->string('session_id')->unique();
            $table->string('ip_address', 45);
            $table->text('user_agent');
            $table->string('device_type')->nullable(); // mobile, desktop, tablet
            $table->string('browser')->nullable(); // Chrome, Firefox, Safari
            $table->string('platform')->nullable(); // Windows, macOS, Linux, Android, iOS
            $table->string('location')->nullable(); // Cidade/País se disponível
            $table->timestamp('login_at');
            $table->timestamp('last_activity');
            $table->boolean('is_active')->default(true);
            $table->timestamp('logout_at')->nullable();
            $table->enum('logout_reason', ['manual', 'timeout', 'forced', 'security'])->nullable();
            $table->timestamps();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('cascade');
            
            // Índices para performance
            $table->index(['company_id', 'user_id']); // Multi-tenancy + usuário
            $table->index(['user_id', 'is_active']); // Usuário + ativo
            $table->index(['session_id']); // Sessão
            $table->index(['last_activity']); // Última atividade
            $table->index(['ip_address']); // IP
            $table->index(['company_id', 'is_active']); // Multi-tenancy + ativo
            $table->index(['login_at', 'logout_at']); // Período de sessão
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_user_active_sessions');
    }
};
