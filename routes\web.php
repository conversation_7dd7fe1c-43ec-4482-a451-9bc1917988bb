<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

use App\Http\Controllers\Auth\StudentAuthController;
use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Panel\Student\DashboardController as StudentDashboardController;
use App\Http\Controllers\Panel\Student\QuizController as StudentQuizController;

use App\Http\Controllers\Panel\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Panel\Admin\SysUsersController;
use App\Http\Controllers\Panel\Admin\PlgStudentsController;
use App\Http\Controllers\Panel\Admin\PlgEnrollmentsController;
use App\Http\Controllers\Panel\Admin\PlgCategoriesController;
use App\Http\Controllers\Panel\Admin\PlgCoursesController;
use App\Http\Controllers\Panel\Admin\PlgModuleController;
use App\Http\Controllers\Panel\Admin\PlgModulesContentController;
use App\Http\Controllers\Panel\Admin\PlgQuestionsController;
use App\Http\Controllers\Panel\Admin\PlgTestController;
use App\Http\Controllers\Panel\Admin\TestReportsController;
use App\Http\Controllers\Panel\Admin\MediaController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Rota protegida para servir mídias com autenticação
Route::get('/media/{path}', [MediaController::class, 'serve'])
    ->where('path', '.*')
    ->name('media.serve');

// Página inicial
Route::get('/', function () {
    // Verificar se o usuário já está autenticado
    if (Auth::check()) {
        return redirect()->route('admin.dashboard');
    } elseif (Auth::guard('students')->check()) {
        return redirect()->route('student.dashboard');
    }

    // Se não estiver autenticado, mostrar página de escolha
    return view('welcome');
})->name('welcome');


// ====================================================
// ROTAS DE AUTENTICAÇÃO PARA ESTUDANTES (/login)
// ====================================================
Route::middleware('guest')->group(function () {
    Route::get('/login', [StudentAuthController::class, 'showLoginForm'])->name('student.login');
    Route::post('/login', [StudentAuthController::class, 'login'])->name('student.login.post');
    Route::get('/register', [StudentAuthController::class, 'showRegistrationForm'])->name('student.register');
    Route::post('/register', [StudentAuthController::class, 'register'])->name('student.register.post');
    Route::get('/forgot-password', [StudentAuthController::class, 'showForgotForm'])->name('student.password.request');
    Route::post('/forgot-password', [StudentAuthController::class, 'forgotPassword'])->name('student.password.email');
});

// ====================================================
// ROTAS DE AUTENTICAÇÃO PARA ADMINISTRADORES (/panel)
// ====================================================
Route::prefix('panel')->group(function () {
    // Rotas públicas do panel (não autenticadas)
    Route::middleware('guest')->group(function () {
        Route::get('/', [AdminAuthController::class, 'showLoginForm'])->name('admin.login');
        Route::post('/', [AdminAuthController::class, 'login'])->name('admin.login.post');
        Route::get('/forgot-password', [AdminAuthController::class, 'showForgotForm'])->name('admin.password.request');
        Route::post('/forgot-password', [AdminAuthController::class, 'forgotPassword'])->name('admin.password.email');
    });

    // Logout compartilhado
    Route::post('/logout', function(Request $request) {
        if (Auth::guard('students')->check()) {
            return app(StudentAuthController::class)->logout($request);
        }
        return app(AdminAuthController::class)->logout($request);
    })->name('logout');

    // Rotas autenticadas do panel (admin/teacher)
    Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
            Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');

            // System Users - apenas super_admin
            Route::resource('users', SysUsersController::class)->middleware('role:super_admin');

            
            
            // Matrículas dentro do ADMIN
            Route::prefix('enrollments')->name('enrollments.')->group(function () {
                Route::get('/', [PlgEnrollmentsController::class, 'index'])->name('index');
                Route::get('/data', [PlgEnrollmentsController::class, 'getData'])->name('data');
                Route::get('/create', [PlgEnrollmentsController::class, 'create'])->name('create');
                Route::get('/modules-by-course/{courseId}', [PlgEnrollmentsController::class, 'getModulesByCourse'])->name('modules-by-course');
                Route::post('/', [PlgEnrollmentsController::class, 'store'])->name('store');
                Route::get('/{enrollment}/edit', [PlgEnrollmentsController::class, 'edit'])->name('edit');
                Route::put('/{enrollment}', [PlgEnrollmentsController::class, 'update'])->name('update');
                Route::get('/{enrollment}', [PlgEnrollmentsController::class, 'show'])->name('show');
                Route::delete('/{enrollment}', [PlgEnrollmentsController::class, 'destroy'])->name('destroy');
                Route::post('/{enrollment}/approve', [PlgEnrollmentsController::class, 'approve'])->name('approve');
                Route::post('/{enrollment}/reject', [PlgEnrollmentsController::class, 'reject'])->name('reject');
            });
            
            
            
            // Students dentro do ADMIN
            Route::prefix('students')->name('students.')->group(function () {
                Route::get('/', [PlgStudentsController::class, 'index'])->name('index');
                Route::get('/data', [PlgStudentsController::class, 'getData'])->name('data');
                Route::get('/create', [PlgStudentsController::class, 'create'])->name('create');
                Route::post('/', [PlgStudentsController::class, 'store'])->name('store');
                Route::get('/{student}/edit', [PlgStudentsController::class, 'edit'])->name('edit');
                Route::put('/{student}', [PlgStudentsController::class, 'update'])->name('update');
                Route::get('/{student}', [PlgStudentsController::class, 'show'])->name('show');
                Route::delete('/{student}', [PlgStudentsController::class, 'destroy'])->name('destroy');
            });

            // Teachers - apenas super_admin
            Route::prefix('teachers')->name('teachers.')->middleware('role:super_admin')->group(function () {
                Route::get('/', [SysUsersController::class, 'indexTeachers'])->name('index');
                Route::get('/data', [SysUsersController::class, 'getDataTeachers'])->name('data');
                Route::get('/create', [SysUsersController::class, 'createTeacher'])->name('create');
                Route::post('/', [SysUsersController::class, 'storeTeacher'])->name('store');
                Route::get('/{teacher}/edit', [SysUsersController::class, 'editTeacher'])->name('edit');
                Route::put('/{teacher}', [SysUsersController::class, 'updateTeacher'])->name('update');
                Route::get('/{teacher}', [SysUsersController::class, 'showTeacher'])->name('show');
                Route::delete('/{teacher}', [SysUsersController::class, 'destroyTeacher'])->name('destroy');
            });

            // Categories
            Route::prefix('categories')->name('categories.')->group(function () {
                Route::get('/', [PlgCategoriesController::class, 'index'])->name('index');
                Route::get('/data', [PlgCategoriesController::class, 'getData'])->name('data');
                Route::get('/create', [PlgCategoriesController::class, 'create'])->name('create');
                Route::post('/', [PlgCategoriesController::class, 'store'])->name('store');
                Route::get('/{category}/edit', [PlgCategoriesController::class, 'edit'])->name('edit');
                Route::put('/{category}', [PlgCategoriesController::class, 'update'])->name('update');
                Route::get('/{category}', [PlgCategoriesController::class, 'show'])->name('show');
                Route::delete('/{category}', [PlgCategoriesController::class, 'destroy'])->name('destroy');
            });

            // Courses - validação simples feita nos controllers
            Route::prefix('courses')->name('courses.')->group(function () {
                Route::get('/', [PlgCoursesController::class, 'index'])->name('index');
                Route::get('/data', [PlgCoursesController::class, 'getData'])->name('data');
                Route::get('/create', [PlgCoursesController::class, 'create'])->name('create');
                Route::post('/', [PlgCoursesController::class, 'store'])->name('store');
                Route::get('/{course}/edit', [PlgCoursesController::class, 'edit'])->name('edit');
                Route::put('/{course}', [PlgCoursesController::class, 'update'])->name('update');
                Route::get('/{course}', [PlgCoursesController::class, 'show'])->name('show');
                Route::delete('/{course}', [PlgCoursesController::class, 'destroy'])->name('destroy');
                Route::post('/{course}/duplicate', [PlgCoursesController::class, 'duplicate'])->name('duplicate');
            });

            // Modules
            Route::prefix('modules')->name('modules.')->group(function () {
                Route::get('/', [PlgModuleController::class, 'index'])->name('index');
                Route::get('/data', [PlgModuleController::class, 'getData'])->name('data');
                Route::get('/course/{courseId}', [PlgModuleController::class, 'byCourse'])->name('by-course');
                Route::get('/create', [PlgModuleController::class, 'create'])->name('create');
                Route::post('/', [PlgModuleController::class, 'store'])->name('store');
                Route::get('/{module}', [PlgModuleController::class, 'show'])->name('show');
                Route::get('/{module}/edit', [PlgModuleController::class, 'edit'])->name('edit');
                Route::put('/{module}', [PlgModuleController::class, 'update'])->name('update');
                Route::delete('/{module}', [PlgModuleController::class, 'destroy'])->name('destroy');
                Route::post('/reorder', [PlgModuleController::class, 'reorder'])->name('reorder');
                Route::post('/{module}/duplicate', [PlgModuleController::class, 'duplicate'])->name('duplicate');

                // Module Contents
                Route::prefix('{moduleId}/contents')->name('contents.')->group(function () {
                    Route::get('/', [PlgModulesContentController::class, 'index'])->name('index');
                    Route::get('/data', [PlgModulesContentController::class, 'getData'])->name('data');
                    Route::get('/create', [PlgModulesContentController::class, 'create'])->name('create');
                    Route::post('/', [PlgModulesContentController::class, 'store'])->name('store');
                    Route::get('/{content}', [PlgModulesContentController::class, 'show'])->name('show');
                    Route::get('/{content}/edit', [PlgModulesContentController::class, 'edit'])->name('edit');
                    Route::put('/{content}', [PlgModulesContentController::class, 'update'])->name('update');
                    Route::delete('/{content}', [PlgModulesContentController::class, 'destroy'])->name('destroy');
                    Route::post('/{content}/duplicate', [PlgModulesContentController::class, 'duplicate'])->name('duplicate');
                });
            });

            // Questions routes
            Route::prefix('questions')->name('questions.')->group(function () {
                Route::get('/', [PlgQuestionsController::class, 'index'])->name('index');
                Route::get('/data', [PlgQuestionsController::class, 'getData'])->name('data');
                Route::get('/create', [PlgQuestionsController::class, 'create'])->name('create');
                Route::post('/', [PlgQuestionsController::class, 'store'])->name('store');
                Route::get('/import', [PlgQuestionsController::class, 'import'])->name('import');
                Route::post('/import-csv', [PlgQuestionsController::class, 'importCsv'])->name('importCsv');
                Route::post('/preview-csv', [PlgQuestionsController::class, 'previewCsv'])->name('previewCsv');
                Route::get('/{question}', [PlgQuestionsController::class, 'show'])->name('show');
                Route::get('/{question}/edit', [PlgQuestionsController::class, 'edit'])->name('edit');
                Route::put('/{question}', [PlgQuestionsController::class, 'update'])->name('update');
                Route::delete('/{question}', [PlgQuestionsController::class, 'destroy'])->name('destroy');
            });
            
            // Tests routes
            Route::prefix('tests')->name('tests.')->group(function () {
                Route::get('/', [PlgTestController::class, 'index'])->name('index');
                Route::get('/data', [PlgTestController::class, 'getData'])->name('data');
                Route::get('/create', [PlgTestController::class, 'create'])->name('create');
                Route::get('/get-filtered-questions', [PlgTestController::class, 'getFilteredQuestions'])->name('questions');
                Route::post('/', [PlgTestController::class, 'store'])->name('store');
                Route::get('/{test}/edit', [PlgTestController::class, 'edit'])->name('edit');
                Route::put('/{test}', [PlgTestController::class, 'update'])->name('update');
                Route::get('/{test}', [PlgTestController::class, 'show'])->name('show');
                Route::delete('/{test}', [PlgTestController::class, 'destroy'])->name('destroy');
                Route::post('/{test}/duplicate', [PlgTestController::class, 'duplicate'])->name('duplicate');
            });

            // Relatórios de Testes
            Route::prefix('reports/tests')->name('reports.tests.')->group(function () {
                Route::get('/', [TestReportsController::class, 'index'])->name('index');
                Route::get('/dashboard-data', [TestReportsController::class, 'dashboardData'])->name('dashboard-data');
                Route::get('/{test}', [TestReportsController::class, 'show'])->name('show');
                Route::get('/{test}/export', [TestReportsController::class, 'exportCsv'])->name('export');
                Route::get('/{test}/student/{student}', [TestReportsController::class, 'studentAttempts'])->name('student-attempts');
            });

            // Media routes
            Route::prefix('media')->name('media.')->group(function () {
                Route::get('/', [MediaController::class, 'index'])->name('index');
                Route::get('/load-more', [MediaController::class, 'loadMore'])->name('load-more');
                Route::get('/types', [MediaController::class, 'getMediaTypes'])->name('types');
                Route::get('/stats', [MediaController::class, 'stats'])->name('stats');
                Route::post('/', [MediaController::class, 'store'])->name('store');
                Route::get('/{media}/info', [MediaController::class, 'info'])->name('info');
                Route::delete('/{media}', [MediaController::class, 'destroy'])->name('destroy');
                Route::put('/{media}', [MediaController::class, 'update'])->name('update');
                Route::post('/{media}/replace', [MediaController::class, 'replace'])->name('replace');
                Route::post('/delete-multiple', [MediaController::class, 'deleteMultiple'])->name('delete-multiple');
                Route::post('/clean-orphaned', [MediaController::class, 'cleanOrphaned'])->name('clean-orphaned');
                Route::get('/modal', function () {
                    return view('panel.admin.media.partials.modal');
                })->name('modal');
            });
        });

    // Rotas de estudante
    Route::prefix('student')->name('student.')->middleware('auth:students')->group(function () {
        Route::get('/', [StudentDashboardController::class, 'index'])->name('dashboard');
        Route::get('/courses', [StudentDashboardController::class, 'courses'])->name('courses');
        Route::get('/quizzes', [StudentDashboardController::class, 'quizzes'])->name('quizzes');
        Route::get('/quiz/{id}', [StudentDashboardController::class, 'takeQuiz'])->name('take-quiz');
        Route::get('/simulated', [StudentDashboardController::class, 'simulated'])->name('simulated');
        Route::get('/profile', [StudentDashboardController::class, 'profile'])->name('profile');

        // Auto-matrícula em módulos gratuitos
        Route::get('/enroll-free-module/{module}', [StudentDashboardController::class, 'enrollInFreeModule'])->name('enroll-free-module');

        // Rotas para cursos (sem prefixo 'curso')
        Route::get('/{courseSlug}', [StudentDashboardController::class, 'show'])->name('course.show');

        // Rotas para módulos específicos (com verificação de acesso)
        Route::middleware('module.access')->group(function () {
            // Nova rota para detalhes do módulo (hub)
            Route::get('/{courseSlug}/{moduleSlug}', [StudentDashboardController::class, 'showModuleDetails'])->name('module.details');

            // Rota para materiais do módulo (antiga showModule)
            Route::get('/{courseSlug}/{moduleSlug}/materiais', [StudentDashboardController::class, 'showModule'])->name('module.show');

            // Rota para quizzes do módulo
            Route::get('/{courseSlug}/{moduleSlug}/quizzes', [StudentDashboardController::class, 'showModuleQuizzes'])->name('module.quizzes');

            Route::get('/{courseSlug}/{moduleSlug}/conclusao', [StudentDashboardController::class, 'showModuleConclusion'])->name('module.conclusion');
            Route::get('/{courseSlug}/{moduleSlug}/conteudo/{contentId}', [StudentDashboardController::class, 'showModuleContent'])->name('module.content');

            // Rotas para progresso de conteúdo
            Route::post('/{courseSlug}/{moduleSlug}/conteudo/{contentId}/completar', [StudentDashboardController::class, 'markContentAsCompleted'])->name('module.content.complete');
            Route::post('/{courseSlug}/{moduleSlug}/conteudo/{contentId}/descompletar', [StudentDashboardController::class, 'markContentAsIncomplete'])->name('module.content.incomplete');

            // Grupo de rotas para quiz/teste
            Route::prefix('{courseSlug}/{moduleSlug}')->group(function () {
                // Quiz de treino
                Route::prefix('quiz')->name('quiz.')->group(function () {
                    Route::get('/{testSlug}', [StudentQuizController::class, 'startQuiz'])->name('start');
                    Route::get('/{testSlug}/historico', [StudentQuizController::class, 'showHistory'])->name('history');
                });

                // Teste final
                Route::prefix('teste')->name('test.')->group(function () {
                    Route::get('/{testSlug}', [StudentQuizController::class, 'startTest'])->name('start');
                    Route::get('/{testSlug}/historico', [StudentQuizController::class, 'showHistory'])->name('history');
                });
            });
        });

        // API routes essenciais para quiz (VERSÃO SIMPLIFICADA)
        Route::post('/quiz/start', [StudentQuizController::class, 'initializeQuiz'])->name('quiz.initialize');
        Route::get('/quiz/{testId}/history', [StudentQuizController::class, 'getAttemptHistory'])->name('quiz.history');
        Route::get('/quiz/{attemptId}/question/{questionNumber}', [StudentQuizController::class, 'getCurrentQuestion'])->name('quiz.question');
        Route::post('/quiz/{attemptId}/answer', [StudentQuizController::class, 'submitAnswer'])->name('quiz.answer');
        Route::post('/quiz/{attemptId}/complete', [StudentQuizController::class, 'completeQuiz'])->name('quiz.complete');
        Route::get('/quiz/results/{attemptId}', [StudentQuizController::class, 'showResults'])->name('quiz.results');
    });
    
    // Rota de fallback para redirecionar usuários autenticados para seus respectivos dashboards
    Route::fallback(function () {
        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        } elseif (Auth::guard('students')->check()) {
            return redirect()->route('student.dashboard');
        }
        return redirect('/');
    });
});

// Rota de fallback global para redirecionar usuários autenticados para seus respectivos dashboards
Route::fallback(function () {
    if (Auth::check()) {
        return redirect()->route('admin.dashboard');
    } elseif (Auth::guard('students')->check()) {
        return redirect()->route('student.dashboard');
    }
    return redirect('/');
});