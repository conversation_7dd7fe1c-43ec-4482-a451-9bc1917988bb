@extends('layouts.panel')

@section('title', 'Edit Teachers')
@section('page_title', 'Edit Teachers')
@section('content')
<div class="container mx-auto py-6 px-6">
    <h1 class="text-2xl font-bold mb-4">Edit Teacher</h1>
    <div class="bg-white shadow rounded p-4">
        <form action="{{ route('admin.teachers.update', $teacher) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="mb-4">
                <label class="block text-gray-700">Name</label>
                <input type="text" name="name" class="w-full border rounded px-3 py-2" value="{{ old('name', $teacher->name) }}" required>
                @error('name')<div class="text-red-600 text-sm">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Email</label>
                <input type="email" name="email" class="w-full border rounded px-3 py-2" value="{{ old('email', $teacher->email) }}" required>
                @error('email')<div class="text-red-600 text-sm">{{ $message }}</div>@enderror
            </div>
            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Update</button>
            <a href="{{ route('admin.teachers.index') }}" class="ml-2 text-gray-600 hover:underline">Cancel</a>
        </form>
    </div>
</div>
@endsection 