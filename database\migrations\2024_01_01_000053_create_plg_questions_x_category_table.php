<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_questions_x_category', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->foreignId('question_id')->constrained('plg_questions')->onDelete('cascade');
            $table->foreignId('category_id')->constrained('plg_categories')->onDelete('cascade');
            $table->timestamps();
            
            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            
            // Índices para performance
            $table->unique(['question_id', 'category_id']);
            $table->index(['company_id', 'category_id']); // Multi-tenancy + categoria
            $table->index(['category_id']);
            $table->index(['question_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_questions_x_category');
    }
};
