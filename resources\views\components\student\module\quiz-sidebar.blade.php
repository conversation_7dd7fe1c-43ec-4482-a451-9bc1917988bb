@props(['module', 'course', 'user' => null])

@php
    $user = $user ?? auth()->user();
@endphp

<div  class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-graduation-cap mr-2 text-purple-600"></i>
            Quizzes do Módulo
        </h3>
    </div>

    <!-- Quiz List -->
    <div class="p-0">
        <!-- Quiz Hooks Avançados -->
        <div class="p-4 border-b border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-2">Quiz Hooks Avançados</h4>
            <div class="text-sm text-gray-600 mb-3">
                <p><i class="fas fa-question-circle mr-1"></i> 5 questões • 15 min</p>
                <p class="text-xs text-gray-500">Pontuação 100%</p>
            </div>
            <button class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-redo mr-1"></i>
                Refazer Quiz
            </button>
        </div>

        <!-- Quiz Extra: Hooks Avançados -->
        <div class="p-4 border-b border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-2">Quiz Extra: Hooks Avançados</h4>
            <div class="text-sm text-gray-600 mb-3">
                <p><i class="fas fa-question-circle mr-1"></i> 5 questões • 10 min</p>
                <div class="flex items-center mt-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span class="text-xs text-gray-600">Última tentativa</span>
                    <span class="ml-auto text-xs text-gray-600">55.56%</span>
                </div>
                <div class="text-xs text-gray-500 mt-1">Em andamento</div>
            </div>
            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-play mr-1"></i>
                Tentar Novamente
            </button>
        </div>

        <!-- Quiz - Performance -->
        <div class="p-4 border-b border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-2">Quiz - Performance</h4>
            <div class="text-sm text-gray-600 mb-3">
                <p><i class="fas fa-question-circle mr-1"></i> 1 questões • 10 min</p>
            </div>
            <button class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-play mr-1"></i>
                Iniciar Quiz
            </button>
        </div>

        <!-- Quiz Final - React Avançado -->
        <div class="p-4">
            <h4 class="font-semibold text-gray-900 mb-2">Quiz Final - React Avançado</h4>
            <div class="text-sm text-gray-600 mb-3">
                <p><i class="fas fa-question-circle mr-1"></i> 1 questões • 20 min</p>
            </div>
            <button class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-play mr-1"></i>
                Iniciar Quiz
            </button>
        </div>
    </div>
</div>
