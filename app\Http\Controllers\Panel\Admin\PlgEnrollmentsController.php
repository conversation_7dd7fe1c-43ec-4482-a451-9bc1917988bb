<?php

namespace App\Http\Controllers\Panel\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlgEnrollment;
use App\Models\PlgStudent;
use App\Models\PlgModule;
use App\Models\PlgCourse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class PlgEnrollmentsController extends Controller
{
    public function index()
    {
        return view('panel.admin.enrollments.index', [
            'columns' => $this->getColumns(),
            'id' => 'enrollments-table',
            'ajaxUrl' => route('admin.enrollments.data'),
            'filters' => $this->getFilters(),
            'clearFiltersBtn' => true
        ]);
    }

    public function getData()
    {
        $user = Auth::user();

        $query = PlgEnrollment::with([
                'student:id,name,email',
                'module:id,title,course_id',
                'module.course:id,title'
            ])
            ->select('plg_enrollments.*')
            ->where('company_id', $user->company_id)
            ->latest('id');

        // Filtro: professores veem apenas matrículas que eles processaram
        if ($user->role === 'teacher') {
            $query->where('user_id', $user->id);
        }

        return DataTables::of($query)
            ->addColumn('student_name', function($enrollment) {
                return $enrollment->student?->name ?? 'N/A';
            })
            ->addColumn('student_email', function($enrollment) {
                return $enrollment->student?->email ?? 'N/A';
            })
            ->addColumn('course_title', function($enrollment) {
                return $enrollment->module?->course?->title ?? 'N/A';
            })
            ->addColumn('module_title', function($enrollment) {
                return $enrollment->module?->title ?? 'N/A';
            })
            ->addColumn('status_badge', function($enrollment) {
                return view('panel.admin.enrollments.partials.status', compact('enrollment'))->render();
            })
            ->addColumn('approval_status_badge', function($enrollment) {
                return view('panel.admin.enrollments.partials.approval-status', compact('enrollment'))->render();
            })
            ->addColumn('enrollment_method_badge', function($enrollment) {
                return view('panel.admin.enrollments.partials.enrollment-method', compact('enrollment'))->render();
            })
            ->addColumn('price_formatted', function($enrollment) {
                return $enrollment->module_price_at_time > 0 
                    ? 'R$ ' . number_format($enrollment->module_price_at_time, 2, ',', '.') 
                    : 'Gratuito';
            })
            ->addColumn('enrolled_at_formatted', function($enrollment) {
                return $enrollment->enrolled_at?->format('d/m/Y H:i') ?? 'N/A';
            })
            ->addColumn('expires_at_formatted', function($enrollment) {
                return $enrollment->expires_at?->format('d/m/Y') ?? 'Sem expiração';
            })
            ->addColumn('actions', fn($enrollment) => view('panel.admin.includes.table-actions', [
                'actions' => [
                    ['type' => 'show', 'route' => route('admin.enrollments.show', $enrollment->id), 'title' => 'Ver detalhes'],
                    ['type' => 'edit', 'route' => route('admin.enrollments.edit', $enrollment->id), 'title' => 'Editar matrícula'],
                    ['type' => 'delete', 'route' => route('admin.enrollments.destroy', $enrollment->id), 'title' => 'Excluir matrícula', 'confirm_message' => 'Tem certeza que deseja excluir esta matrícula?']
                ]
            ])->render())
            ->rawColumns(['status_badge', 'approval_status_badge', 'enrollment_method_badge', 'actions'])
            ->filter(function ($query) {
                $searchValue = trim(request('search.value', ''));
                
                if (empty($searchValue)) {
                    return;
                }

                $query->where(function($q) use ($searchValue) {
                    // Busca por ID se for numérico
                    if (is_numeric($searchValue)) {
                        $q->where('plg_enrollments.id', $searchValue);
                    }
                    
                    // Busca no nome do estudante
                    $q->orWhereHas('student', function($studentQuery) use ($searchValue) {
                        $studentQuery->where('name', 'LIKE', "%{$searchValue}%");
                    })
                    // Busca no email do estudante
                    ->orWhereHas('student', function($studentQuery) use ($searchValue) {
                        $studentQuery->where('email', 'LIKE', "%{$searchValue}%");
                    })
                    // Busca no título do curso
                    ->orWhereHas('module.course', function($courseQuery) use ($searchValue) {
                        $courseQuery->where('title', 'LIKE', "%{$searchValue}%");
                    })
                    // Busca no título do módulo
                    ->orWhereHas('module', function($moduleQuery) use ($searchValue) {
                        $moduleQuery->where('title', 'LIKE', "%{$searchValue}%");
                    });
                });
            })
            ->make(true);
    }



    public function create()
    {
        $user = Auth::user();
        $data = $this->getEnrollmentFormData($user);

        return view('panel.admin.enrollments.create', $data);
    }

    /**
     * Buscar módulos de um curso (filtrado por role)
     */
    public function getModulesByCourse($courseId)
    {
        $user = Auth::user();

        $courseQuery = PlgCourse::with('modules');

        // Filtrar por role
        if ($user->role === 'teacher') {
            $courseQuery->where('user_id', $user->id);
        }

        $course = $courseQuery->findOrFail($courseId);

        $modules = $course->modules->map(function($module) {
            return [
                'id' => $module->id,
                'title' => $module->title,
                'duration_text' => $module->getDurationText(),
                'price' => $module->getCurrentPrice()
            ];
        });

        return response()->json($modules);
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        $validated = $this->validateEnrollmentRequest($request, $user);

        try {
            DB::beginTransaction();

            $this->checkExistingEnrollment($validated['student_id'], $validated['module_id']);
            $enrollmentData = $this->prepareEnrollmentData($validated, $user);

            PlgEnrollment::create($enrollmentData);
            DB::commit();

            return redirect()->route('admin.enrollments.index')
                ->with('success', 'Matrícula criada com sucesso!');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Erro ao criar matrícula: ' . $e->getMessage()])->withInput();
        }
    }

    public function show($id)
    {
        $enrollment = PlgEnrollment::with(['student', 'module.course', 'company'])
            ->findOrFail($id);

        return view('panel.admin.enrollments.show', compact('enrollment'));
    }

    public function edit($id)
    {
        $user = Auth::user();
        $enrollment = PlgEnrollment::findOrFail($id);
        $data = $this->getEnrollmentFormData($user);
        $data['enrollment'] = $enrollment;

        return view('panel.admin.enrollments.create', $data);
    }

    public function update(Request $request, $id)
    {
        $enrollment = PlgEnrollment::findOrFail($id);
        
        $validated = $request->validate([
            'expires_at' => 'nullable|date',
            'status' => 'required|in:active,expired,canceled',
            'approval_status' => 'required|in:pending,approved,rejected',
            'module_price_at_time' => 'nullable|numeric|min:0',
            'rejection_reason' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Se está aprovando
            if ($validated['approval_status'] === 'approved' && $enrollment->approval_status !== 'approved') {
                $validated['approved_at'] = now();
                $validated['approved_by'] = Auth::id();
            }

            $enrollment->update($validated);

            DB::commit();

            return redirect()->route('admin.enrollments.index')
                ->with('success', 'Matrícula atualizada com sucesso!');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Erro ao atualizar matrícula: ' . $e->getMessage()])->withInput();
        }
    }

    public function destroy($id)
    {
        try {
            $enrollment = PlgEnrollment::findOrFail($id);
            $enrollment->delete();

            return redirect()->route('admin.enrollments.index')
                ->with('success', 'Matrícula excluída com sucesso!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Erro ao excluir matrícula: ' . $e->getMessage()]);
        }
    }

    public function approve($id)
    {
        try {
            $enrollment = PlgEnrollment::findOrFail($id);
            
            $enrollment->update([
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
                'status' => 'active'
            ]);

            return redirect()->route('admin.enrollments.index')
                ->with('success', 'Matrícula aprovada com sucesso!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Erro ao aprovar matrícula: ' . $e->getMessage()]);
        }
    }

    public function reject(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000'
        ]);

        try {
            $enrollment = PlgEnrollment::findOrFail($id);
            
            $enrollment->update([
                'approval_status' => 'rejected',
                'rejection_reason' => $request->rejection_reason,
                'status' => 'canceled'
            ]);

            return redirect()->route('admin.enrollments.index')
                ->with('success', 'Matrícula rejeitada com sucesso!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Erro ao rejeitar matrícula: ' . $e->getMessage()]);
        }
    }

    private function getColumns()
    {
        return [
            ['data' => 'id', 'name' => 'id', 'label' => 'ID', 'width' => '60px', 'searchable' => true],
            ['data' => 'student_name', 'name' => 'student.name', 'label' => 'Estudante', 'searchable' => true],
            ['data' => 'student_email', 'name' => 'student.email', 'label' => 'Email', 'searchable' => true],
            ['data' => 'course_title', 'name' => 'module.course.title', 'label' => 'Curso', 'searchable' => true],
            ['data' => 'module_title', 'name' => 'module.title', 'label' => 'Módulo', 'searchable' => true],
            ['data' => 'status_badge', 'name' => 'status', 'label' => 'Status', 'orderable' => false, 'searchable' => false],
            ['data' => 'approval_status_badge', 'name' => 'approval_status', 'label' => 'Aprovação', 'orderable' => false, 'searchable' => false],
            ['data' => 'enrollment_method_badge', 'name' => 'enrollment_method', 'label' => 'Método', 'orderable' => false, 'searchable' => false],
            ['data' => 'price_formatted', 'name' => 'module_price_at_time', 'label' => 'Preço', 'searchable' => false],
            ['data' => 'enrolled_at_formatted', 'name' => 'enrolled_at', 'label' => 'Matriculado em', 'searchable' => false],
            ['data' => 'expires_at_formatted', 'name' => 'expires_at', 'label' => 'Expira em', 'searchable' => false],
            ['data' => 'actions', 'name' => 'actions', 'label' => 'Ações', 'orderable' => false, 'searchable' => false, 'width' => '120px']
        ];
    }

    private function getFilters()
    {
        return [
            [
                'label' => 'Status',
                'column' => 'status',
                'options' => [
                    ['value' => '', 'label' => 'Todos'],
                    ['value' => 'active', 'label' => 'Ativo'],
                    ['value' => 'expired', 'label' => 'Expirado'],
                    ['value' => 'canceled', 'label' => 'Cancelado']
                ]
            ],
            [
                'label' => 'Aprovação',
                'column' => 'approval_status',
                'options' => [
                    ['value' => '', 'label' => 'Todos'],
                    ['value' => 'pending', 'label' => 'Pendente'],
                    ['value' => 'approved', 'label' => 'Aprovado'],
                    ['value' => 'rejected', 'label' => 'Rejeitado']
                ]
            ],
            [
                'label' => 'Método',
                'column' => 'enrollment_method',
                'options' => [
                    ['value' => '', 'label' => 'Todos'],
                    ['value' => 'self_registration', 'label' => 'Auto-matrícula'],
                    ['value' => 'admin_created', 'label' => 'Admin']
                ]
            ]
        ];
    }

    /**
     * Obter dados comuns para formulários de matrícula
     */
    private function getEnrollmentFormData($user)
    {
        $students = PlgStudent::where('active', true)
            ->where('registration_status', 'approved')
            ->orderBy('name')
            ->get();

        // Filtrar cursos por role
        $coursesQuery = PlgCourse::with('modules')->where('status', 'published');
        if ($user->role === 'teacher') {
            $coursesQuery->where('user_id', $user->id);
        }
        $courses = $coursesQuery->orderBy('title')->get();

        // Buscar professores para super_admin
        $teachers = $user->role === 'super_admin'
            ? \App\Models\SysUser::where('role', 'teacher')
                ->where('active', true)
                ->orderBy('name')
                ->get()
            : collect();

        return compact('students', 'courses', 'teachers');
    }

    /**
     * Validar dados da requisição de matrícula
     */
    private function validateEnrollmentRequest(Request $request, $user)
    {
        $rules = [
            'student_id' => 'required|exists:plg_students,id',
            'module_id' => 'required|exists:plg_modules,id',
            'expires_at' => 'nullable|date|after:today',
            'module_price_at_time' => 'nullable|numeric|min:0',
            'enrollment_method' => 'required|in:admin_created,teacher_created',
        ];

        if ($user->role === 'super_admin') {
            $rules['assigned_teacher_id'] = 'required|exists:sys_users,id';
        }

        return $request->validate($rules);
    }

    /**
     * Verificar se já existe matrícula ativa
     */
    private function checkExistingEnrollment($studentId, $moduleId)
    {
        $exists = PlgEnrollment::where('student_id', $studentId)
            ->where('module_id', $moduleId)
            ->where('status', 'active')
            ->exists();

        if ($exists) {
            throw new \Exception('Estudante já está matriculado neste módulo.');
        }
    }

    /**
     * Preparar dados para criação da matrícula
     */
    private function prepareEnrollmentData(array $validated, $user)
    {
        $module = PlgModule::findOrFail($validated['module_id']);
        $enrolledAt = now();

        return [
            'company_id' => $user->company_id,
            'user_id' => $user->id,
            'student_id' => $validated['student_id'],
            'module_id' => $validated['module_id'],
            'enrolled_at' => $enrolledAt,
            'expires_at' => $validated['expires_at'] ?? $module->calculateExpiryDate($enrolledAt),
            'module_price_at_time' => $validated['module_price_at_time'] ?? $module->getCurrentPrice(),
            'status' => 'active',
            'enrollment_method' => $validated['enrollment_method'],
            'assigned_teacher_id' => $user->role === 'super_admin'
                ? $validated['assigned_teacher_id']
                : $user->id,
        ];
    }
}