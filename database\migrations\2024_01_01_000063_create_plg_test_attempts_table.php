<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_test_attempts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->foreignId('test_id')->constrained('plg_tests')->onDelete('cascade');
            $table->foreignId('student_id')->constrained('plg_students')->onDelete('cascade');
            $table->integer('attempt_number');

            // Dados da tentativa
            $table->json('answers')->nullable(); // Respostas do estudante em formato JSON
            $table->decimal('score', 5, 2)->nullable();
            $table->integer('correct_count')->default(0);
            $table->integer('total_questions');
            $table->boolean('passed')->default(false);

            // Controle de tempo e status (SIMPLIFICADO)
            $table->timestamp('started_at');
            $table->timestamp('finished_at')->nullable();
            $table->enum('status', ['in_progress', 'completed', 'abandoned'])->default('in_progress');

            $table->timestamps();

            // Chave estrangeira
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');

            // Índices para performance
            $table->index(['company_id', 'test_id']);
            $table->index(['student_id', 'test_id']);
            $table->index(['test_id', 'status']);
            $table->index(['student_id', 'status']);
            $table->index(['started_at']);
            $table->index(['finished_at']);
            $table->unique(['company_id', 'student_id', 'test_id', 'attempt_number'], 'plg_test_attempts_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_test_attempts');
    }
};
