@props(['module', 'course'])

<div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 dark:text-white">Detalhes do Módulo</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Visualize todos os materiais e conteúdos deste módulo
                </p>
            </div>
        </div>
        
        <div class="flex items-center space-x-2">
            @if($module->contents && $module->contents->count() > 0)
                <x-ui.badge variant="default" size="xs">
                    {{ $module->contents->count() }} conteúdo{{ $module->contents->count() > 1 ? 's' : '' }}
                </x-ui.badge>
            @endif
            
            @if($module->slug)
                <a href="{{ route('student.module.details', [$course->slug, $module->slug]) }}"
                   class="inline-flex items-center justify-center px-4 py-2 border border-blue-500 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Ver Detalhes
                </a>
            @else
                <button disabled 
                        class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 bg-gray-100 text-gray-400 text-sm font-medium rounded-lg cursor-not-allowed">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    Em Configuração
                </button>
            @endif
        </div>
    </div>
</div>
