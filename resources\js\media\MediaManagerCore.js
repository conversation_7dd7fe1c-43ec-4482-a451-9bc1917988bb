import { MediaAPI } from './MediaAPI.js';
import { notify } from '../notifications.js';

export class MediaManagerCore {
    constructor(options = {}) {
        this.options = {
            multiple: false,
            selectable: true,
            target: 'thumb', // 'thumb' ou 'gallery'
            onSelect: null,
            onSelectionChange: null,
            ...options
        };
        
        // Detectar target automaticamente se não foi especificado
        if (!options.target) {
            this.options.target = this.options.multiple ? 'gallery' : 'thumb';
        }
        
        this.selectedMedia = new Map();
        this.currentPage = 1;
        this.currentFilters = {};
        this.allMediaItems = new Map();
        this.isDestroyed = false;
    }

    // ==================== CARREGAMENTO DE DADOS ====================

    async loadMedia(page = 1, filters = {}) {
        if (this.isDestroyed) return null;
        
        this.currentPage = page;
        this.currentFilters = filters;
        
        try {
            const data = await MediaAPI.loadMedia(page, filters);
            console.log(data, 'MediaManagerCore.loadMedia()');
            // Cache dos itens
            data.items.forEach(item => {
                this.allMediaItems.set(item.id, item);
            });
            
            return data;
        } catch (error) {
            console.error('Erro ao carregar mídias:', error);
            notify.error('Erro ao carregar mídias');
            throw error;
        }
    }

    async loadMediaTypes() {
        try {
            return await MediaAPI.getMediaTypes();
        } catch (error) {
            console.error('Erro ao carregar tipos de mídia:', error);
            return {
                'all': { label: 'Todas as Mídias', icon: 'fas fa-layer-group', count: 0 },
                'images': { label: 'Imagens', icon: 'fas fa-image', count: 0 }
            };
        }
    }

    // ==================== SELEÇÃO ====================

    toggleItemSelection(mediaId, media) {
        const isSelected = this.selectedMedia.has(mediaId);
        const allowMultiple = this.isMultipleAllowed();
        
        if (allowMultiple) {
            if (isSelected) {
                this.selectedMedia.delete(mediaId);
            } else {
                this.selectedMedia.set(mediaId, media);
            }
        } else {
            this.clearSelection();
            this.selectedMedia.set(mediaId, media);
        }

        this.notifySelectionChange();
        return !isSelected; // Retorna se foi selecionado
    }

    isMultipleAllowed() {
        // Targets que permitem múltiplos
        const multipleTargets = ['gallery', 'images', 'files', 'media'];
        // Targets que são únicos
        const singleTargets = ['thumb', 'thumbnail', 'avatar', 'logo', 'cover', 'image'];
        
        if (multipleTargets.includes(this.options.target)) {
            return true;
        }
        
        if (singleTargets.includes(this.options.target)) {
            return false;
        }
        
        // Fallback: se contém palavras-chave de múltiplos
        const target = this.options.target.toLowerCase();
        return target.includes('gallery') || target.includes('multiple') || target.includes('varios');
    }

    toggleSelectAll(currentPageItems) {
        const allSelected = currentPageItems.every(item => this.selectedMedia.has(item.id));

        currentPageItems.forEach(item => {
            if (allSelected) {
                this.selectedMedia.delete(item.id);
            } else {
                this.selectedMedia.set(item.id, item);
            }
        });

        this.notifySelectionChange();
        return !allSelected; // Retorna se todos foram selecionados
    }

    async selectAllItems() {
        try {
            // Carregar todos os itens do filtro atual
            let page = 1;
            let hasMorePages = true;
            let allItems = [];

            while (hasMorePages) {
                const data = await MediaAPI.loadMedia(page, this.currentFilters);
                allItems = allItems.concat(data.items);
                
                hasMorePages = data.pagination.has_more_pages;
                page++;
            }

            // Selecionar todos os itens
            allItems.forEach(item => {
                this.selectedMedia.set(item.id, item);
                this.allMediaItems.set(item.id, item);
            });

            this.notifySelectionChange();
            notify.success(`${allItems.length} itens selecionados`);
            return allItems.length;
        } catch (error) {
            console.error('Erro ao selecionar todos os itens:', error);
            notify.error('Erro ao selecionar todos os itens');
            throw error;
        }
    }

    clearSelection() {
        this.selectedMedia.clear();
        this.notifySelectionChange();
    }

    getSelectedArray() {
        return Array.from(this.selectedMedia.values());
    }

    getSelectedCount() {
        return this.selectedMedia.size;
    }

    isSelected(mediaId) {
        return this.selectedMedia.has(mediaId);
    }

    // ==================== UPLOAD ====================

    async uploadFiles(files) {
        if (!files || !files.length) return;

        const filesArray = Array.from(files);
        const fileCount = filesArray.length;

        try {
            const uploadPromises = filesArray.map(file => MediaAPI.uploadFile(file));
            const results = await Promise.all(uploadPromises);
            
            notify.success(`${fileCount} arquivo(s) enviado(s) com sucesso!`);
            return results;
        } catch (error) {
            console.error('Erro no upload:', error);
            notify.error('Erro ao enviar arquivo(s): ' + (error.message || 'Erro desconhecido'));
            throw error;
        }
    }

    // ==================== EXCLUSÃO ====================

    async deleteSelected() {
        const selectedItems = this.getSelectedArray();
        if (selectedItems.length === 0) return;

        const message = selectedItems.length === 1 ? 
            `Excluir "${selectedItems[0].filename}"?` :
            `Excluir ${selectedItems.length} itens selecionados?`;

        if (!confirm(message)) return;

        try {
            if (selectedItems.length > 1) {
                await MediaAPI.deleteMultiple(selectedItems.map(m => m.id));
            } else {
                await MediaAPI.deleteMedia(selectedItems[0].id);
            }
            
            this.clearSelection();
            notify.success(`${selectedItems.length} item(ns) excluído(s) com sucesso!`);
            return true;
        } catch (error) {
            console.error('Erro ao excluir:', error);
            notify.error('Erro ao excluir itens');
            throw error;
        }
    }

    async deleteMedia(media) {
        if (!confirm(`Excluir "${media.filename}"?`)) return;

        try {
            await MediaAPI.deleteMedia(media.id);
            this.selectedMedia.delete(media.id);
            this.allMediaItems.delete(media.id);
            notify.success('Mídia excluída com sucesso!');
            return true;
        } catch (error) {
            console.error('Erro ao excluir mídia:', error);
            notify.error('Erro ao excluir mídia');
            throw error;
        }
    }

    // ==================== MÍDIAS ÓRFÃS ====================

    async searchOrphanedMedia() {
        try {
            const data = await this.loadMedia(1, { orphaned: 'true' });
            return data;
        } catch (error) {
            console.error('Erro ao buscar mídias órfãs:', error);
            notify.error('Erro ao buscar mídias órfãs');
            throw error;
        }
    }

    async cleanOrphanedMedia() {
        if (!confirm('Tem certeza que deseja limpar todas as mídias órfãs? Esta ação não pode ser desfeita.')) {
            return;
        }

        try {
            const result = await MediaAPI.cleanOrphaned();
            
            if (result.success) {
                notify.success(result.message);
                return true;
            } else {
                notify.error('Erro ao limpar mídias órfãs');
                return false;
            }
        } catch (error) {
            console.error('Erro ao limpar mídias órfãs:', error);
            notify.error('Erro ao limpar mídias órfãs');
            throw error;
        }
    }

    // ==================== UTILITÁRIOS ====================

    notifySelectionChange() {
        const selectedArray = this.getSelectedArray();
        
        if (this.options.onSelectionChange) {
            this.options.onSelectionChange(selectedArray);
        }
    }

    getFileIcon(mimeType) {
        const icons = {
            'application/pdf': 'fas fa-file-pdf text-red-500',
            'application/msword': 'fas fa-file-word text-blue-500',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word text-blue-500',
            'video/mp4': 'fas fa-file-video text-purple-500',
            'audio/mp3': 'fas fa-file-audio text-green-500',
        };
        return icons[mimeType] || 'fas fa-file text-gray-500';
    }

    destroy() {
        this.isDestroyed = true;
        this.selectedMedia.clear();
        this.allMediaItems.clear();
    }
} 