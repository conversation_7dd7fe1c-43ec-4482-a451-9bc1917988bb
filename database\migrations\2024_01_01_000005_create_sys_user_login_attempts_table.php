<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sys_user_login_attempts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id');
            $table->string('current_session_id');
            $table->string('attempt_ip', 45);
            $table->text('attempt_user_agent');
            $table->string('attempt_device_info')->nullable();
            $table->string('attempt_location')->nullable();
            $table->timestamp('attempt_at');
            $table->enum('status', ['pending', 'approved', 'denied', 'timeout'])->default('pending');
            $table->timestamp('resolved_at')->nullable();
            $table->text('resolution_note')->nullable();
            $table->timestamps();
            
            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('cascade');
            
            // Índices para performance
            $table->index(['company_id', 'user_id']); // Multi-tenancy + usuário
            $table->index(['status', 'attempt_at']); // Status + data
            $table->index(['current_session_id']); // Sessão atual
            $table->index(['attempt_ip']); // IP da tentativa
            $table->index(['company_id', 'status']); // Multi-tenancy + status
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_user_login_attempts');
    }
};
