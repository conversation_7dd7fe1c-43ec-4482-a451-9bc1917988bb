@if(session('success'))
    <div class="mb-4 rounded-lg bg-green-50 dark:bg-green-900/20 p-4 text-sm text-green-600 dark:text-green-400 alert-message" role="alert">
        <div class="flex items-center gap-2">
            <i class="fas fa-check-circle"></i>
            <span>{{ session('success') }}</span>
        </div>
    </div>
@endif

@if(session('error'))
    <div class="mb-4 rounded-lg bg-red-50 dark:bg-red-900/20 p-4 text-sm text-red-600 dark:text-red-400 alert-message" role="alert">
        <div class="flex items-center gap-2">
            <i class="fas fa-times-circle"></i>
            <span>{{ session('error') }}</span>
        </div>
    </div>
@endif

@if(session('warning'))
    <div class="mb-4 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 p-4 text-sm text-yellow-600 dark:text-yellow-400 alert-message" role="alert">
        <div class="flex items-center gap-2">
            <i class="fas fa-exclamation-circle"></i>
            <span>{{ session('warning') }}</span>
        </div>
    </div>
@endif

@if(session('info'))
    <div class="mb-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 text-sm text-blue-600 dark:text-blue-400 alert-message" role="alert">
        <div class="flex items-center gap-2">
            <i class="fas fa-info-circle"></i>
            <span>{{ session('info') }}</span>
        </div>
    </div>
@endif

@if($errors->any())
    <div class="mb-4 rounded-lg bg-red-50 dark:bg-red-900/20 p-4 text-sm text-red-600 dark:text-red-400 alert-message" role="alert">
        <div class="flex items-center gap-2">
            <i class="fas fa-times-circle"></i>
            <div>
                <ul class="list-disc list-inside space-y-1">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
@endif

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Seleciona todos os alertas na página
        const alerts = document.querySelectorAll('.alert-message');
        
        if (alerts.length > 0) {
            // Para cada alerta encontrado
            alerts.forEach(function(alert) {
                // Configura timeout de 3 segundos
                setTimeout(function() {
                    // Adiciona classe para transição suave
                    alert.style.transition = 'opacity 0.5s ease';
                    alert.style.opacity = '0';
                    
                    // Remove o elemento após a animação terminar
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }, 3000);
            });
        }
    });
</script> 
