@extends('layouts.panel')

@section('title', 'Detalhes do Teste')
@section('page_title', 'Detalhes do Teste')

@section('content')
<div class="animate-fade-in">
    <div class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg">
        @include('panel.admin.includes.page-header', [
            'title' => 'Detalhes do Teste',
            'description' => 'Visualize os detalhes deste teste.',
            'actions' => [
                [
                    'route' => route('admin.tests.edit', $test->id),
                    'text' => 'Editar',
                    'icon' => 'fas fa-edit',
                    'class' => 'bg-trends-primary text-white',
                    'hover_class' => 'bg-trends-primary/90'
                ],
                [
                    'route' => route('admin.tests.index'),
                    'text' => 'Voltar',
                    'icon' => 'fas fa-arrow-left',
                    'class' => 'bg-white dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-700',
                    'hover_class' => 'bg-zinc-50 dark:bg-zinc-700'
                ]
            ]
        ])

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-zinc-800 dark:text-zinc-100">Informações Gerais</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-zinc-500 dark:text-zinc-400">Nome:</span>
                            <p class="text-zinc-800 dark:text-zinc-100">{{ $test->name }}</p>
                        </div>
                        <div>
                            <span class="text-sm text-zinc-500 dark:text-zinc-400">Tipo:</span>
                            <p>
                                @switch($test->test_type)
                                    @case('quiz')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                            Quiz
                                        </span>
                                        @break
                                    @case('challenge')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                                            Desafio
                                        </span>
                                        @break
                                    @case('exam')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                            Exame
                                        </span>
                                        @break
                                @endswitch
                            </p>
                        </div>
                        <div>
                            <span class="text-sm text-zinc-500 dark:text-zinc-400">Status:</span>
                            <p>
                                @if($test->active)
                                    <span class="inline-flex items-center text-success-dark">
                                        <i class="fas fa-check-circle mr-1"></i> Ativo
                                    </span>
                                @else
                                    <span class="inline-flex items-center text-danger-dark">
                                        <i class="fas fa-times-circle mr-1"></i> Inativo
                                    </span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4 text-zinc-800 dark:text-zinc-100">Informações Adicionais</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-zinc-500 dark:text-zinc-400">Módulo:</span>
                            <p class="text-zinc-800 dark:text-zinc-100">
                                {{ $test->module ? $test->module->title : 'Sem módulo' }}
                            </p>
                        </div>
                        <div>
                            <span class="text-sm text-zinc-500 dark:text-zinc-400">Criado em:</span>
                            <p class="text-zinc-800 dark:text-zinc-100">
                                {{ $test->created_at->format('d/m/Y H:i:s') }}
                            </p>
                        </div>
                        <div>
                            <span class="text-sm text-zinc-500 dark:text-zinc-400">Última atualização:</span>
                            <p class="text-zinc-800 dark:text-zinc-100">
                                {{ $test->updated_at->format('d/m/Y H:i:s') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-zinc-200 dark:border-zinc-800 pt-6">
                <h3 class="text-lg font-semibold mb-4 text-zinc-800 dark:text-zinc-100">
                    Questões ({{ count($questions) }})
                </h3>

                @if(empty($questions))
                    <p class="text-zinc-500 dark:text-zinc-400">Nenhuma questão adicionada a este teste.</p>
                @else
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-800">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-800/50 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                        Ordem
                                    </th>
                                    <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-800/50 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                        Questão
                                    </th>
                                    <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-800/50 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                        Tipo
                                    </th>
                                    <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-800/50 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">
                                        Categorias
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-zinc-900 divide-y divide-zinc-200 dark:divide-zinc-800">
                                @foreach($questions as $index => $question)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-zinc-800 dark:text-zinc-100">
                                            {{ $index + 1 }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-zinc-800 dark:text-zinc-100">
                                            {{ Str::limit($question->question, 100) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            @switch($question->question_type)
                                                @case('multiple_choice')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                                        Múltipla Escolha
                                                    </span>
                                                    @break
                                                @case('true_false')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                                        Verdadeiro/Falso
                                                    </span>
                                                    @break
                                                @default
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-zinc-100 text-zinc-800 dark:bg-zinc-900/30 dark:text-zinc-400">
                                                        {{ $question->question_type }}
                                                    </span>
                                            @endswitch
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <div class="flex flex-wrap gap-1">
                                                @if($question->categories)
                                                    @foreach($question->categories as $category)
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-zinc-100 text-zinc-800 dark:bg-zinc-900/30 dark:text-zinc-400">
                                                            {{ $category->title }}
                                                        </span>
                                                    @endforeach
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection 