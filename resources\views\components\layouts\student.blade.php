@props(['title' => config('app.name')])

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <!-- Anti-flash snippet -->
    <script>
        (function() {
            try {
                const currentRoute = window.location.pathname;
                const isDashboard = currentRoute.endsWith('/panel/student/dashboard') || currentRoute.endsWith('/panel/student');
                
                // Se for dashboard, sempre usar tema escuro
                let theme = isDashboard ? 'dark' : (localStorage.getItem('theme') || 'dark');
                
                const root = document.documentElement;
                root.classList.add(theme);
                
                // Aplicar cores críticas imediatamente
                const style = document.createElement('style');
                style.textContent = `
                    :root { background: #121212 !important; }
                    .dark { background: #000000 !important; }
                    body { opacity: 0; }
                    .theme-loaded body { opacity: 1; transition: opacity .2s ease; }
                `;
                document.head.appendChild(style);
                
                // Marcar como carregado após um pequeno delay
                setTimeout(() => root.classList.add('theme-loaded'), 50);
                
                // Armazenar informação se estamos no dashboard
                window.isDashboardPage = isDashboard;
            } catch (e) {
                console.error('Theme initialization error:', e);
            }
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? config('app.name', 'Laravel') }}</title>
    
    <!-- Font Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Swiper.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    
    <style>
        /* Garantir que a página de dashboard sempre use o tema escuro */
        .dashboard-page {
            background-color: #000 !important;
        }
        .dashboard-page .bg-gray-50,
        .dashboard-page .bg-gray-100,
        .dashboard-page .bg-white {
            background-color: #000 !important;
        }
        .dashboard-page .text-gray-700,
        .dashboard-page .text-gray-800,
        .dashboard-page .text-gray-900 {
            color: #fff !important;
        }
        
        /* Esconder elementos no dashboard */
        .dashboard-page .dashboard-hide {
            display: none !important;
        }
    </style>
    
    @vite(['resources/css/app.css'])
    @stack('styles')



    <script>
        // Verifica se o usuário tem preferência por darkmode
        (function() {
            try {
                const currentRoute = window.location.pathname;
                const isDashboard = currentRoute.endsWith('/panel/student/dashboard') || currentRoute.endsWith('/panel/student');
                
                // Se for dashboard, sempre usar tema escuro
                if (isDashboard) {
                    document.documentElement.classList.add('dark');
                    return;
                }
                
                // Para outras páginas, respeitar a preferência do usuário
        if (localStorage.getItem('darkMode') === 'true' || 
            (!('darkMode' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
            } catch (e) {
                console.error('Theme preference error:', e);
            }
        })();
    </script>

    <script>
        // Adicionar classe ao body quando estiver no dashboard
        document.addEventListener('DOMContentLoaded', function() {
            const currentRoute = window.location.pathname;
            const isDashboard = currentRoute.endsWith('/panel/student/dashboard') || 
                               currentRoute.endsWith('/panel/student') || 
                               currentRoute === '/panel/student/';
            
            if (isDashboard) {
                document.body.classList.add('dashboard-page');
                document.documentElement.classList.add('dark');
            }
        });
    </script>
</head>
<body class="font-sans antialiased h-full bg-black dark:bg-gray-900">
    <div class="min-h-screen flex flex-col">
        <!-- Navbar Component -->
        <x-student.navbar :user="Auth::guard('students')->user()" />

        <!-- Page Content -->
        <main class="flex-grow bg-gray-50 dark:bg-gray-900">
            {{ $slot }}
        </main>

        <!-- Footer -->
        <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4">
            <div class="container mx-auto">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        &copy; {{ date('Y') }} {{ config('app.name') }}. Todos os direitos reservados.
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modal de Contato (Componente Blade) -->
    @auth('students')
        <x-student.contact-modal :student="auth('students')->user()" />
    @endauth



    @vite(['resources/js/app.js'])

    <!-- Netflix Carousel Script (apenas para páginas de estudante) -->
    @vite(['resources/js/netflix-carousel.js'])

    @stack('scripts')
</body>
</html>