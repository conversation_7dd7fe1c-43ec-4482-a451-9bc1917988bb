<x-layouts.student title="{{ $module->title }} - {{ $course->title }}">
    <!-- Breadcrumb -->
    <x-student.breadcrumb :course="$course" :module="$module" />

    <div class="min-h-screen">
        <!-- Main Content Area -->
        <div class="container mx-auto">
            <div class="grid grid-cols-12 gap-6">
                <!-- Left Sidebar - Content Navigation + Quizzes (4 columns) -->
                <div class="col-span-4">
                    <x-student.module.content-sidebar
                        :module="$module"
                        :course="$course"
                        :progress="$progress ?? collect()"
                        :progressPercentage="$progressPercentage ?? 0" />
                </div>

                <!-- Main Content Area (8 columns) -->
                <div class="col-span-8">
                    @php
                        // Definir variáveis necessárias
                        $firstContent = $module->contents->count() > 0 ? $module->contents->sortBy('order')->first() : null;
                        $isFirstContentCompleted = $firstContent && $progress->has($firstContent->id) && $progress[$firstContent->id]->completed;
                    @endphp

                    @if($module->contents->count() > 0 && $firstContent)

                        <!-- Content Header -->
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="">
                                        <h1 class="text-lg font-bold text-gray-900">
                                            @switch($firstContent->type)
                                                @case('video')
                                                    <i class="fas fa-video text-blue-600 text-xl"></i>
                                                    @break
                                                @case('pdf')
                                                    <i class="fas fa-file-pdf text-blue-600 text-xl"></i>
                                                    @break
                                                @case('link')
                                                    <i class="fas fa-external-link-alt text-blue-600 text-xl"></i>
                                                    @break
                                                @case('word')
                                                    <i class="fas fa-file-word text-blue-600 text-xl"></i>
                                                    @break
                                                @default
                                                    <i class="fas fa-file-text text-blue-600 text-xl"></i>
                                            @endswitch
                                    
                                            {{ $firstContent->title }}
                                        </h1>
                                        
                                        <span class="text-xs text-gray-500">Material 1 de {{ $module->contents->count() }}</span>
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                            {{ ucfirst($firstContent->content_type) }}
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button id="completion-btn"
                                                onclick="toggleCompletion({{ $firstContent->id }})"
                                                class="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 {{ $isFirstContentCompleted ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700' }}">
                                            <i class="fas {{ $isFirstContentCompleted ? 'fa-check' : 'fa-circle' }} mr-2"></i>
                                            <span id="completion-text">{{ $isFirstContentCompleted ? 'Concluído' : 'Marcar como Concluído' }}</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Fallback se não houver conteúdos -->
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                            <div class="p-6">
                                <h1 class="text-2xl font-bold text-gray-900">{{ $module->title }}</h1>
                                <p class="text-gray-600 mt-2">Este módulo ainda não possui conteúdos.</p>
                            </div>
                        </div>
                    @endif

                    @if($module->contents->count() > 0 && $firstContent)
                        <!-- Content Body -->
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ $firstContent->title }}</h2>
                                @php
                                    // Detectar tipo de conteúdo
                                    $isPdf = $firstContent->content_type === 'pdf' ||
                                             (is_string($firstContent->content) && str_ends_with(strtolower($firstContent->content), '.pdf'));
                                @endphp

                                <div class="prose max-w-none dark:prose-invert">
                                    @if($firstContent->content_type === 'video')
                                        <!-- Video Player Preview -->
                                        <div class="bg-black rounded-lg flex items-center justify-center mb-4" style="height: 300px;">
                                            <div class="text-center text-white">
                                                <i class="fas fa-video text-4xl mb-3 opacity-50"></i>
                                                <h3 class="text-lg font-semibold mb-2">Conteúdo em Vídeo</h3>
                                                <p class="text-gray-300 mb-4">{{ $firstContent->description ?? 'Clique em "Próximo" para assistir ao vídeo completo' }}</p>
                                            </div>
                                        </div>
                                    @elseif($isPdf)
                                        <!-- PDF Preview -->
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-4">
                                            <div class="text-center">
                                                <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                                                    <i class="fas fa-file-pdf text-red-600 dark:text-red-400 text-2xl"></i>
                                                </div>
                                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Documento PDF</h3>
                                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                                    {{ $firstContent->description ?? 'Documento em formato PDF disponível para visualização' }}
                                                </p>
                                                <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                                                    <p class="text-sm text-blue-800 dark:text-blue-200">
                                                        <i class="fas fa-info-circle mr-2"></i>
                                                        Clique em "Próximo" para visualizar o PDF completo com navegação integrada
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <!-- Text Content Preview -->
                                        <div class="text-gray-600 dark:text-gray-400">
                                            <p>{{ $firstContent->description ?? 'Conteúdo disponível. Clique em "Próximo" para visualizar.' }}</p>

                                            @if($firstContent->content)
                                                <div class="mt-4">
                                                    {!! Str::limit($firstContent->content, 300) !!}
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Footer -->
                        <div  class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div class="p-6">
                                <div class="flex items-center justify-between">
                                    <!-- Previous Button -->
                                    <div>
                                        <a href="{{ route('student.course.show', $course->slug) }}"
                                           class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                                            <i class="fas fa-chevron-left mr-2"></i>
                                            Anterior
                                        </a>
                                    </div>

                                    <!-- Progress Dots -->
                                    <div class="flex items-center space-x-2">
                                        @foreach($module->contents->sortBy('order') as $index => $moduleContent)
                                            <div class="w-3 h-3 rounded-full {{ $loop->first ? 'bg-red-600' : ($progress->has($moduleContent->id) && $progress[$moduleContent->id]->status === 'completed' ? 'bg-green-500' : 'bg-gray-300') }}"></div>
                                        @endforeach
                                    </div>

                                    <!-- Next Button -->
                                    <div>
                                        @if($module->contents->count() === 1)
                                            <a href="{{ route('student.course.show', $course->slug) }}"
                                               class="inline-flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                                                Voltar aos Módulos
                                                <i class="fas fa-arrow-left ml-2"></i>
                                            </a>
                                        @elseif($firstContent)
                                            <a href="{{ route('student.module.content', [$course->slug, $module->slug, $firstContent->id]) }}"
                                               class="inline-flex items-center px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                                Próximo
                                                <i class="fas fa-chevron-right ml-2"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Fallback para módulo sem conteúdos -->
                        <div  class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div class="p-6 text-center">
                                <i class="fas fa-info-circle text-gray-400 text-4xl mb-4"></i>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Módulo em Preparação</h3>
                                <p class="text-gray-600 mb-6">Este módulo ainda não possui conteúdos disponíveis.</p>
                                <a href="{{ route('student.course.show', $course->slug) }}"
                                   class="inline-flex items-center px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Voltar para especialidade
                                </a>
                            </div>
                        </div>
                    @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript para funcionalidades interativas -->
    @if($module->contents->count() > 0 && $firstContent)
    <script>
    let isCompleted = {{ $isFirstContentCompleted ? 'true' : 'false' }};
    const contentId = {{ $firstContent->id }};
    const courseSlug = '{{ $course->slug }}';
    const moduleSlug = '{{ $module->slug }}';

    function toggleCompletion(contentId) {
        const btn = document.getElementById('completion-btn');
        const text = document.getElementById('completion-text');

        // Desabilitar botão temporariamente
        btn.disabled = true;

        const url = isCompleted
            ? `/panel/student/${courseSlug}/${moduleSlug}/conteudo/${contentId}/descompletar`
            : `/panel/student/${courseSlug}/${moduleSlug}/conteudo/${contentId}/completar`;

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                isCompleted = data.completed;

                // Atualizar botão
                if (isCompleted) {
                    btn.className = 'px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 bg-green-600 hover:bg-green-700 text-white';
                    text.innerHTML = '<i class="fas fa-check mr-2"></i>Concluído';
                } else {
                    btn.className = 'px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 bg-gray-200 hover:bg-gray-300 text-gray-700';
                    text.innerHTML = '<i class="fas fa-circle mr-2"></i>Marcar como Concluído';
                }

                // Atualizar progresso na sidebar
                updateSidebarProgress(data);

                // Verificar se deve redirecionar para página de conclusão
                if (data.all_contents_completed && data.redirect_url) {
                    setTimeout(() => {
                        window.location.href = data.redirect_url;
                    }, 1500); // Aguardar 1.5s para mostrar o feedback visual
                }
            }
        })
        .catch(error => {
            console.error('Erro:', error);
        })
        .finally(() => {
            btn.disabled = false;
        });
    }

    function updateSidebarProgress(data) {
        // Atualizar contador
        const counter = document.getElementById('progress-counter');
        if (counter) {
            counter.textContent = `${data.completed_count}/${data.total_contents}`;
        }

        // Atualizar barra de progresso
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            progressBar.style.width = `${data.progress_percentage}%`;

            // Atualizar cor da barra dinamicamente
            const isAlmostComplete = data.total_contents > 0 && data.completed_count === (data.total_contents - 1);
            const isComplete = data.progress_percentage >= 100;

            // Remover classes de cor existentes
            progressBar.classList.remove('bg-gray-400', 'bg-blue-600', 'bg-green-600');

            // Aplicar nova cor
            if (isComplete) {
                progressBar.classList.add('bg-green-600');
            } else if (isAlmostComplete) {
                progressBar.classList.add('bg-blue-600');
            } else {
                progressBar.classList.add('bg-gray-400');
            }
        }

        // Atualizar item na sidebar
        const contentItem = document.querySelector(`[data-content-id="${contentId}"]`);
        if (contentItem) {
            const icon = contentItem.querySelector('i');
            const statusText = contentItem.querySelector('.text-xs');

            if (isCompleted) {
                icon.className = 'fas fa-check-circle text-green-600 text-lg mt-1';
                if (statusText && !statusText.textContent.includes('Concluído')) {
                    statusText.innerHTML += ' • <span class="text-green-600 font-medium">Concluído</span>';
                }
            } else {
                // Restaurar ícone original baseado no tipo
                const contentType = '{{ $firstContent ? $firstContent->type : "text" }}';
                let iconClass = 'fas ';
                switch(contentType) {
                    case 'video': iconClass += 'fa-video'; break;
                    case 'pdf': iconClass += 'fa-file-pdf'; break;
                    case 'link': iconClass += 'fa-external-link-alt'; break;
                    case 'word': iconClass += 'fa-file-word'; break;
                    default: iconClass += 'fa-file-text';
                }
                icon.className = iconClass + ' text-blue-600 text-lg mt-1';

                if (statusText) {
                    statusText.innerHTML = statusText.textContent.replace(' • Concluído', '');
                }
            }
        }
    }

    function navigateToContent(contentId) {
        // Buscar o elemento clicado para obter a URL correta
        const contentItem = document.querySelector(`[data-content-id="${contentId}"]`);
        if (contentItem && contentItem.dataset.href) {
            window.location.href = contentItem.dataset.href;
        } else {
            // Fallback: usar as variáveis do blade
            const courseSlug = '{{ $course->slug }}';
            const moduleSlug = '{{ $module->slug }}';
            window.location.href = `/student/${courseSlug}/${moduleSlug}/conteudo/${contentId}`;
        }
    }

    // Funções para scroll para seções específicas
    function scrollToQuizzes() {
        const quizzesSection = document.querySelector('[data-section="quizzes"]');
        if (quizzesSection) {
            quizzesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    function scrollToFinalTest() {
        const finalTestSection = document.querySelector('[data-section="final-test"]');
        if (finalTestSection) {
            finalTestSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    </script>
    @endif
</x-layouts.student>
