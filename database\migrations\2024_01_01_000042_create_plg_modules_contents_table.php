<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plg_modules_contents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id'); // Segunda coluna para multi-tenancy
            $table->unsignedBigInteger('user_id')->nullable(); // Quem criou o conteúdo
            $table->foreignId('module_id')->constrained('plg_modules')->onDelete('cascade');
            
            $table->integer('order')->default(0);
            $table->boolean('active')->default(true);
            
            $table->string('title');
            $table->string('slug')->nullable(); // Campo adicionado posteriormente
            $table->text('content')->nullable();
            $table->text('description')->nullable(); // Campo adicionado posteriormente
            $table->string('content_type')->default('text'); // text, video, quiz, etc.
            $table->string('duration')->nullable(); // duração em minutos
            $table->timestamps();
            $table->softDeletes();

            // Chaves estrangeiras
            $table->foreign('company_id')->references('id')->on('sys_company')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('sys_users')->onDelete('set null');

            // Índices para performance
            $table->index(['company_id', 'module_id']); // Multi-tenancy + módulo
            $table->index(['module_id', 'order']);
            $table->index(['slug']);
            $table->index(['active']);
            $table->index(['company_id', 'active']); // Multi-tenancy + ativo
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plg_modules_contents');
    }
};
