<?php

namespace App\Events;

use App\Models\SysUserLoginAttempt;
use App\Models\SysUserActiveSession;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LoginAttemptDetected implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $attempt;
    public $activeSession;

    /**
     * Create a new event instance.
     */
    public function __construct(SysUserLoginAttempt $attempt, SysUserActiveSession $activeSession)
    {
        $this->attempt = $attempt;
        $this->activeSession = $activeSession;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user-session.' . $this->activeSession->user_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'attempt_id' => $this->attempt->id,
            'attempt_ip' => $this->attempt->attempt_ip,
            'attempt_device_info' => $this->attempt->attempt_device_info,
            'attempt_location' => $this->attempt->attempt_location,
            'attempt_at' => $this->attempt->attempt_at->format('H:i:s'),
            'current_session_id' => $this->activeSession->session_id,
            'timeout_seconds' => 60, // Será pego das configurações
            'message' => 'Alguém está tentando acessar sua conta',
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'login.attempt.detected';
    }
}
