<x-layouts.student title="Minhas especialidades" class="dashboard-page dark">
    <!-- Breadcrumb -->
    <x-student.breadcrumb />

    <!-- <PERSON><PERSON> de Cursos -->
    <div class="container mx-auto relative">
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">Minhas especialidades</h2>
            <p class="text-sm text-zinc-600 dark:text-zinc-400 mt-1">Especialidades onde você possui matrícula ativa</p>
        </div>

        @if($courses->count() > 0)
            <!-- Grid de Cursos usando o mesmo padrão do dashboard -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($courses as $course)
                    <x-course-card :course="$course" />
                @endforeach
            </div>
        @else
            <!-- Estado Vazio -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="bg-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-graduation-cap text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                        Nenhum curso encontrado
                    </h3>
                    <p class="text-gray-400 mb-6">
                        Você ainda não possui matrícula ativa em nenhum curso.
                        Explore nosso catálogo e comece a aprender!
                    </p>
                    <a href="{{ route('student.dashboard') }}"
                       class="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search"></i>
                        Explorar Cursos
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-layouts.student>
