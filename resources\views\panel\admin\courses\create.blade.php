{{-- H:\quizz\resources\views\panel\admin\courses\create.blade.php --}}
@extends('layouts.panel')

@section('title', isset($course) ? 'Editar Curso' : 'Novo Curso')
@section('page_title', isset($course) ? 'Editar Curso' : 'Novo Curso')

@push('styles')
    @vite(['resources/css/quill-editor.css'])
@endpush

@section('content')
    <div class="animate-fade-in">
        @if ($errors->any())
            <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <strong class="font-medium">Atenção!</strong>
                <ul class="mt-3 list-disc list-inside text-sm">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form
            action="{{ isset($course) ? route('admin.courses.update', $course->id) : route('admin.courses.store') }}"
            method="POST" id="courseForm" enctype="multipart/form-data">
            @csrf
            @if (isset($course))
                @method('PUT')
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div
                        class="bg-white dark:bg-zinc-900 border border-zinc-800/20 dark:border-zinc-800 rounded-lg shadow-lg mb-6">
                        <div class="px-6 py-6 border-b border-zinc-200 dark:border-zinc-800 bg-gradient-to-r from-trends-primary/5 to-transparent dark:from-trends-primary/10">
                            <div class="flex items-center gap-2">
                                <div class="h-5 w-1 bg-trends-primary rounded-full"></div>
                                <h1 class="text-xl font-semibold text-zinc-800 dark:text-zinc-100">Informações do Curso
                                </h1>
                            </div>
                            <p class="text-zinc-500 dark:text-zinc-400 text-sm mt-1">
                                Preencha os dados para criar um novo curso na plataforma.
                            </p>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- Título do Curso -->
                            <div>
                                <label for="title"
                                    class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Título <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="title" name="title"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                    value="{{ old('title', $course->title ?? '') }}" required>
                            </div>

                            <!-- Descrição Curta -->
                            <div>
                                <label for="short_description"
                                    class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Descrição Curta <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="short_description" name="short_description"
                                    class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                    value="{{ old('short_description', $course->short_description ?? '') }}" required>
                            </div>

                            <!-- Descrição Completa -->
                            <div>
                                <label for="description"
                                    class="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
                                    Descrição Completa <span class="text-red-500">*</span>
                                </label>
                                <div id="description-editor" class="editor" data-input="description" data-placeholder="Digite a descrição completa do curso..."></div>
                                <input type="hidden" name="description" id="description" value="{{ old('description', $course->description ?? '') }}" required>
                            </div>

                            <!-- Thumbnail do curso -->
                            <div class="media-selector">
                                <div class="flex justify-between items-center mb-2">
                       
                                <button type="button" 
                                class="{{ isset($course) && $course->hasMedia('thumbnail') ? 'hidden' : '' }} open-media-modal w-full px-4 py-8 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg hover:border-trends-primary dark:hover:border-trends-primary transition-colors group"
                               data-mediable-type="App\Models\PlgCourse"
                                        data-tag="thumbnail"
                                        data-input-id="course_thumbnail"
                                        data-preview-id="thumbnail-preview" 
                                        data-multiple="false">
                                <div class="text-center">
                                    <i class="fas fa-image text-2xl text-zinc-400 dark:text-zinc-600 group-hover:text-trends-primary transition-colors"></i>
                                    <p class="mt-2 text-sm text-zinc-500 dark:text-zinc-400 group-hover:text-trends-primary transition-colors">
                                        Clique para selecionar uma imagem para a capa do Curso
                                    </p>
                                </div>
                            </button>

                                </div>

                                <input type="hidden" name="thumbnail" class="media-input" data-preview-id="thumbnail-preview" id="course_thumbnail" value="{{ old('thumbnail', isset($course) && $course->hasMedia('thumbnail') ? $course->firstMedia('thumbnail')->id : '') }}">

                                <div class="media-preview {{ isset($course) && $course->hasMedia('thumbnail') ? '' : 'hidden' }}" id="thumbnail-preview">
                                    @if (isset($course) && $course->hasMedia('thumbnail'))
                                        @php
                                            $thumbnailMedia = $course->firstMedia('thumbnail');
                                            $thumbnailUrl = route('media.serve', ['path' => $thumbnailMedia->getDiskPath()]) . '?w=250&fit=crop&fm=webp';
                                        @endphp
                                            <div class="relative w-64">
                                            <img src="{{ $thumbnailUrl }}" alt="{{ $course->title ?? 'Thumbnail do curso' }}" class="w-full h-32 object-cover rounded-lg">
                                                <button type="button" class="preview-remove-btn absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600">
                                                    <i class="fas fa-times text-xs"></i>
                                                </button>
                                            </div>
                                            <p class="text-sm text-zinc-500 mt-2 truncate">{{ $thumbnailMedia->filename }}</p>
                                    @endif
                                </div>
                                <p class="text-xs text-zinc-500 mt-1 text-right">Formatos aceitos: JPG, PNG, GIF. Tamanho recomendado: 1280x720px</p>
                            </div>

                            <!-- Categoria e Professor -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="category_id"
                                        class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Categoria <span class="text-red-500">*</span>
                                    </label>
                                    <select name="category_id" id="category_id" class="choices-select w-full category-select"
                                        data-placeholder="Selecione uma categoria" required>
                                        <option value="">Selecione uma categoria</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ old('category_id', $course->category_id ?? '') == $category->id ? 'selected' : '' }}>
                                                {{ $category->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="space-y-2">
                                    <label for="teacher_id" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Professor <span class="text-red-500">*</span>
                                    </label>
                                    <select name="teacher_id" id="teacher_id" class="choices-select w-full teacher-select"
                                        data-placeholder="Selecione um professor" required>
                                        <option value="">Selecione um professor</option>
                                        @foreach ($teachers as $teacher)
                                            <option value="{{ $teacher->id }}"
                                                {{ old('teacher_id', isset($course) ? $course->user_id : '') == $teacher->id ? 'selected' : '' }}>
                                                {{ $teacher->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Duração e Status -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="duration_minutes" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Duração (minutos)
                                    </label>
                                    <input type="number" id="duration_minutes" name="duration_minutes"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        value="{{ old('duration_minutes', $course->duration_minutes ?? '') }}">
                                </div>

                                <div class="space-y-2">
                                    <label for="status" class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                        Status <span class="text-red-500">*</span>
                                    </label>
                                    <select name="status" id="status"
                                        class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-trends-primary/30 dark:bg-zinc-800/50 dark:text-zinc-200"
                                        required>
                                        <option value="draft"
                                            {{ old('status', isset($course) ? $course->status : 'draft') == 'draft' ? 'selected' : '' }}>
                                            Rascunho
                                        </option>
                                        <option value="published"
                                            {{ old('status', isset($course) ? $course->status : '') == 'published' ? 'selected' : '' }}>
                                            Publicado
                                        </option>
                                        <option value="archived"
                                            {{ old('status', isset($course) ? $course->status : '') == 'archived' ? 'selected' : '' }}>
                                            Arquivado
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <!-- Configurações -->
                            <div class="flex items-center gap-4">
                                <label class="inline-flex items-center cursor-pointer relative">
                                    <input type="checkbox" name="featured" value="1" class="sr-only peer"
                                        {{ old('featured', isset($course) ? $course->featured : false) ? 'checked' : '' }}>
                                    <div
                                        class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-trends-primary/30 dark:peer-focus:ring-trends-primary/30 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-trends-primary">
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-zinc-700 dark:text-zinc-300">Destacar na página inicial</span>
                                </label>
                            </div>
                        </div>

                        <div
                            class="px-6 py-4 bg-zinc-50 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800">
                            <div class="flex justify-end gap-4">
                                <a href="{{ route('admin.courses.index') }}"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors">
                                    <i class="fas fa-times"></i>
                                    <span>Cancelar</span>
                                </a>
                                <button type="submit" id="submit-button"
                                    class="inline-flex items-center gap-2 px-4 py-2 bg-trends-primary text-white rounded-lg hover:bg-trends-primary/90 transition-colors">
                                    <i class="fas fa-save"></i>
                                    <span>{{ isset($course) ? 'Atualizar' : 'Salvar' }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                @include('panel.admin.courses.partials.preview')
            </div>
        </form>
    </div>
@endsection

@section('modals')
    {{-- @include('panel.admin.media.partials.modal') --}}
@endsection


@push('scripts')
    
  @vite(['resources/js/quill-editor.js'])

@endpush 